# 个推推送初始化问题修复需求文档

## 介绍

当前应用中的个推推送服务存在初始化问题，`onReceiveClientId` 回调没有被触发。根本原因是 `appPermissionGranted` 状态没有正确更新，导致 `thirdSdkInit()` 中的条件判断失败，个推初始化被跳过。需要将个推初始化整合到现有的隐私政策对话框流程中。

## 需求

### 需求 1：诊断初始化流程

**用户故事：** 作为开发者，我希望能够清楚地看到推送服务的初始化过程，以便识别问题所在。

#### 验收标准

1. WHEN 应用启动 THEN 系统应该记录详细的初始化日志
2. WHEN `Global.init()` 被调用 THEN 应该记录初始化开始的日志
3. WHEN `thirdSdkInit()` 被调用 THEN 应该记录第三方SDK初始化的日志
4. WHEN 隐私协议状态被检查 THEN 应该记录当前的授权状态
5. WHEN `GtPush().init()` 被调用 THEN 应该记录个推初始化的详细过程

### 需求 2：整合个推初始化到隐私政策对话框

**用户故事：** 作为开发者，我希望将个推推送服务的初始化整合到现有的隐私政策对话框流程中，确保用户同意后立即初始化推送服务。

#### 验收标准

1. WHEN 用户在隐私政策对话框中点击同意 THEN 应该立即更新 `appPermissionGranted` 状态为 true
2. WHEN `appPermissionGranted` 状态更新为 true THEN 应该立即调用 `thirdSdkInit()` 初始化个推服务
3. WHEN `thirdSdkInit()` 被调用 THEN 应该记录"用户同意隐私政策，开始初始化第三方SDK"的日志
4. WHEN 个推初始化完成 THEN 应该在30秒内收到 `onReceiveClientId` 回调
5. WHEN 应用重启且用户已同意隐私协议 THEN 应该在应用启动时自动初始化推送服务

### 需求 3：增强错误处理和重试机制

**用户故事：** 作为开发者，我希望推送服务具有健壮的错误处理和重试机制，确保服务的可靠性。

#### 验收标准

1. WHEN 推送初始化失败 THEN 应该记录详细的错误信息
2. WHEN 初始化失败 THEN 应该在5秒后自动重试，最多重试3次
3. WHEN 网络连接恢复 THEN 应该自动重新尝试初始化
4. WHEN 所有重试都失败 THEN 应该记录最终失败状态并提供手动重试方法

### 需求 4：提供初始化状态查询接口

**用户故事：** 作为开发者，我希望能够随时查询推送服务的初始化状态和客户端ID。

#### 验收标准

1. WHEN 查询初始化状态 THEN 应该返回当前的初始化状态（未初始化/初始化中/已初始化/初始化失败）
2. WHEN 查询客户端ID THEN 应该返回当前的CID或null
3. WHEN 推送服务状态发生变化 THEN 应该通过回调通知状态变化
4. WHEN 需要手动重新初始化 THEN 应该提供重置和重新初始化的方法

### 需求 5：改进调试和监控能力

**用户故事：** 作为开发者，我希望有完善的调试工具来监控推送服务的运行状态。

#### 验收标准

1. WHEN 开启调试模式 THEN 应该输出详细的初始化和运行日志
2. WHEN 推送服务运行异常 THEN 应该记录异常信息和堆栈跟踪
3. WHEN 需要诊断问题 THEN 应该提供获取诊断信息的方法
4. WHEN 在生产环境 THEN 应该只记录关键的错误和状态信息