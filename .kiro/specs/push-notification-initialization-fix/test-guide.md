# 个推推送初始化修复测试指南

## 测试前准备

1. **清理应用数据**（可选，用于测试首次安装场景）
   ```bash
   # Android
   adb shell pm clear com.your.app.package
   
   # iOS - 删除应用重新安装
   ```

2. **确保网络连接正常**

3. **准备日志查看工具**
   - Android: `adb logcat | grep -i "个推\|GtPush\|推送"`
   - iOS: Xcode Console

## 测试场景

### 场景1：首次安装用户的隐私政策同意流程

**测试步骤：**
1. 启动应用
2. 观察是否显示隐私政策对话框
3. 点击"同意"按钮
4. 观察日志输出

**预期结果：**
```
🔍 检查第三方SDK初始化状态...
📋 隐私协议状态: 未同意
⏸️ 用户未同意隐私协议，等待用户同意后初始化
[用户点击同意后]
✅ 用户同意隐私政策，MobSDK 初始化完成
🚀 开始初始化第三方SDK...
🚀 用户同意隐私政策，开始初始化第三方SDK...
📍 初始化高德地图定位服务
📱 初始化个推推送服务
🚀 个推初始化开始 - 第1次尝试
📱 Android 平台：调用 startSdk (或 🍎 iOS 平台：调用 startSdk)
📋 注册个推事件处理器
✅ 个推SDK调用完成，等待CID回调...
✅ 个推推送服务初始化调用成功
✅ 第三方SDK初始化完成
[等待几秒到几十秒]
✅ 个推 onReceiveClientId 触发: [CID字符串]
✅ 个推初始化成功 - CID: [CID字符串]
📊 个推状态监控已启动，检查间隔: 60秒
```

### 场景2：已同意用户的应用重启初始化流程

**测试步骤：**
1. 重启应用（确保之前已同意隐私政策）
2. 观察日志输出

**预期结果：**
```
🔍 检查第三方SDK初始化状态...
📋 隐私协议状态: 已同意
✅ 用户已同意隐私协议，开始初始化第三方SDK
🚀 用户同意隐私政策，开始初始化第三方SDK...
📍 初始化高德地图定位服务
📱 初始化个推推送服务
🚀 个推初始化开始 - 第1次尝试
[后续流程同场景1]
```

### 场景3：网络异常情况下的重试机制

**测试步骤：**
1. 断开网络连接
2. 启动应用并同意隐私政策
3. 观察重试日志
4. 恢复网络连接
5. 观察是否成功初始化

**预期结果：**
```
🚀 个推初始化开始 - 第1次尝试
⏰ 个推初始化超时 (或其他网络错误)
🔄 个推初始化失败，5秒后重试 (1/3)
🚀 个推初始化开始 - 第2次尝试
[重试过程...]
[网络恢复后]
✅ 个推 onReceiveClientId 触发: [CID字符串]
```

### 场景4：验证初始化状态查询接口

**测试步骤：**
1. 在应用中添加临时代码查询状态：
   ```dart
   // 在某个页面的 initState 或按钮点击中添加
   final gtPush = GtPush();
   QLog("当前初始化状态: ${gtPush.isInitialized}");
   QLog("当前CID: ${gtPush.clientId}");
   QLog("诊断信息: ${gtPush.getDiagnosticInfo()}");
   ```

**预期结果：**
- 初始化前：`isInitialized: false, clientId: null`
- 初始化后：`isInitialized: true, clientId: [实际CID]`

### 场景5：验证状态监控和自动修复

**测试步骤：**
1. 等待初始化成功
2. 观察每60秒的状态检查日志
3. 手动清除SharedPreferences中的CID（模拟数据不一致）
4. 等待下次状态检查

**预期结果：**
```
📊 个推状态监控已启动，检查间隔: 60秒
[60秒后]
🔍 执行个推状态检查...
📋 个推状态检查完成: PushInitStatus.initialized, CID: [CID]
[数据不一致时]
🔧 检测到CID不一致，修复中...
✅ 状态已修复
```

## 验证清单

### ✅ 功能验证
- [ ] 隐私政策对话框正常显示
- [ ] 点击同意后立即初始化推送服务
- [ ] `onReceiveClientId` 回调正常触发
- [ ] CID 正确保存到 SharedPreferences
- [ ] 应用重启后自动初始化（已同意用户）
- [ ] 网络异常时的重试机制工作正常
- [ ] 状态查询接口返回正确信息

### ✅ 日志验证
- [ ] 所有关键步骤都有日志输出
- [ ] 日志格式统一，使用表情符号标识
- [ ] 错误信息详细且有用
- [ ] 没有多余的调试信息干扰

### ✅ 性能验证
- [ ] 初始化不阻塞UI线程
- [ ] 内存使用正常，无泄漏
- [ ] 状态监控不影响应用性能

### ✅ 异常处理验证
- [ ] 网络异常时正确处理
- [ ] 初始化超时时正确处理
- [ ] 重试次数达到上限时正确处理
- [ ] 状态不一致时能自动修复

## 常见问题排查

### 问题1：`onReceiveClientId` 仍然不触发

**可能原因：**
1. 网络连接问题
2. 个推服务器配置错误
3. 应用包名与个推配置不匹配
4. Android权限不足

**排查步骤：**
1. 检查网络连接
2. 验证个推配置信息
3. 检查应用权限
4. 查看完整的错误日志

### 问题2：初始化成功但CID为空

**可能原因：**
1. SharedPreferences保存失败
2. 状态更新时序问题

**排查步骤：**
1. 检查存储权限
2. 查看CID保存的错误日志
3. 验证状态一致性检查是否工作

### 问题3：重试机制不工作

**可能原因：**
1. Timer被意外取消
2. 状态判断逻辑错误

**排查步骤：**
1. 检查Timer相关日志
2. 验证状态转换逻辑
3. 查看重试计数是否正确

## 测试完成标准

当以下所有条件都满足时，可以认为修复成功：

1. ✅ 首次安装用户同意隐私政策后，30秒内收到CID
2. ✅ 已同意用户重启应用后，30秒内收到CID  
3. ✅ 网络异常时能正确重试，网络恢复后能成功初始化
4. ✅ 状态查询接口返回正确信息
5. ✅ 状态监控和自动修复机制正常工作
6. ✅ 所有关键操作都有清晰的日志记录
7. ✅ 没有内存泄漏或性能问题