# Coding Conventions & Patterns

## Naming Conventions

### Files & Directories
- Use snake_case for file and directory names
- Feature-based organization: group related files in feature directories
- Custom components prefixed with `q_` (e.g., `q_text.dart`, `q_container.dart`)

### Classes & Variables
- PascalCase for class names (e.g., `ApiService`, `QText`)
- camelCase for variables and methods
- SCREAMING_SNAKE_CASE for constants (e.g., `APP_PERMISSION`, `BASE_URL`)

## Architecture Patterns

### GetX Pattern
- Use GetX for state management, routing, and dependency injection
- Controllers handle business logic, Views handle UI
- Separate view and controller files for complex pages

### Service Layer Pattern
- Services in `lib/services/` handle external integrations
- Use static methods for utility services (e.g., `ApiService.get()`)
- Services return standardized response format with `state`, `msg`, `data`, `timestamp`

### Component Design
- Custom components extend Flutter widgets (e.g., `QText extends Text`)
- Use `flutter_screenutil` for responsive design (`.sp` for font sizes, `.w/.h` for dimensions)
- Default styling in custom components with override options

## Code Quality Standards

### Error Handling
- Use try-catch blocks for async operations
- Return standardized error responses with meaningful messages
- Log errors using `QLog()` utility

### Logging
- Use `QLog()` for consistent logging throughout the app
- Include emojis for log categorization (🔍, ✅, ❌, ⚠️, 📱, etc.)
- Log API requests, responses, and errors

### Privacy & Permissions
- Initialize third-party SDKs only after user consent
- Check `appPermissionGranted` before SDK initialization
- Use `Global.checkAndInitThirdPartySdk()` pattern

## Environment Management

### Configuration
- Use `Config.ENV` enum for environment switching
- Separate test and production URLs/configurations
- Environment-specific initialization in `Global.init()`

### Build Variants
- Test environment: `EnvType.TEST`
- Production environment: `EnvType.PROD`
- Switch via `Config.ENV` constant

## Common Patterns

### Async Initialization
- Non-blocking UI startup in `main.dart`
- Async initialization in `Global.init()`
- SDK initialization after privacy consent

### State Persistence
- Use `SharedPreferences` for local storage
- Standardized keys in `Config` class
- Helper methods in `Global` class for common operations

### API Integration
- Centralized HTTP client in `ApiService`
- Automatic token injection via interceptors
- Consistent error handling and logging

### UI Responsiveness
- Use `ScreenUtilInit` with design size 375x812
- Responsive font sizes with `.sp`
- Responsive dimensions with `.w` and `.h`

## Third-Party Integration

### SDK Initialization Order
1. Check privacy consent status
2. Initialize core services (API, storage)
3. Initialize third-party SDKs after consent
4. Handle initialization failures gracefully

### Common SDKs
- AliCloud DYPNS for carrier login
- WeChat Kit for social features
- Getui for push notifications
- AMap for location services
- Flutter Blue Plus for Bluetooth

## Testing & Debugging

### Debug Configuration
- WebView debugging disabled in production
- Comprehensive logging in development
- Test utilities in `lib/utils/crypto_test.dart`

### Build Commands
- Use `build_simple.sh` for iOS builds
- Gradle proxy configuration available
- Fastlane integration for both platforms