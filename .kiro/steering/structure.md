# Project Structure

## Root Directory Layout
```
lima/
├── lib/                    # Main Dart source code
├── android/               # Android-specific configuration
├── ios/                   # iOS-specific configuration  
├── assets/                # Static assets (images, JS files)
├── docs/                  # Project documentation
├── packages/              # Custom Flutter packages
├── test/                  # Unit and widget tests
└── web/                   # Web platform support
```

## Core Application Structure (`lib/`)
```
lib/
├── main.dart              # Application entry point
├── global.dart            # Global configuration and initialization
├── common/                # Shared utilities and configurations
│   ├── config.dart        # Environment and API configurations
│   ├── bridge_controller.dart
│   └── ble/               # Bluetooth-related common code
├── pages/                 # UI screens/pages (feature-based)
│   ├── login/             # Authentication screens
│   ├── main_tab/          # Main tab navigation
│   ├── phone_input/       # Phone input functionality
│   ├── verification/      # Verification screens
│   ├── webview/           # WebView integration
│   └── [other features]/
├── components/            # Reusable UI components
├── services/              # Business logic and API services
├── utils/                 # Utility functions and helpers
├── routers/               # Navigation and routing
├── res/                   # Resources (colors, icons)
└── widgets/               # Custom widgets
```

## Key Architectural Patterns

### Page Organization
- Each feature has its own directory under `pages/`
- Pages follow GetX pattern with separate view and controller files
- Related pages are grouped by feature (e.g., `login/`, `verification/`)

### Component Structure
- `components/`: Reusable UI components used across multiple pages
- `widgets/`: Custom widgets with specific functionality
- Custom components include: `curved_tab_bar.dart`, `privacy_agreement_dialog.dart`, `q_*` prefixed components

### Service Layer
- `services/`: Contains business logic and API integrations
- Key services: `api_service.dart`, `carrier_login_service.dart`, `third_party_login_service.dart`
- Services handle external integrations and data management

### Utilities
- `utils/`: Helper functions and utility classes
- Includes: HTTP clients, crypto utilities, push notifications, WebView helpers
- `jsbridge/`: JavaScript bridge communication utilities

### Configuration Management
- `common/config.dart`: Centralized configuration with environment switching
- `global.dart`: Application-wide initialization and state management
- Environment-specific URLs and settings

## Asset Organization
```
assets/
├── images/                # App images and icons
├── tab-bar/              # Tab bar specific icons
├── jsBridgeHelper.js     # JavaScript bridge utilities
└── receiveMsg.js         # Message handling scripts
```

## Platform-Specific Code
- `android/`: Gradle configuration, native Android integration
- `ios/`: Xcode project, CocoaPods, native iOS integration
- Both platforms include extensive third-party SDK integrations

## Documentation Structure
- `docs/`: Comprehensive project documentation
- Feature-specific guides in subdirectories (`login/`, `phone_input/`, `verification/`)
- Integration guides for external services (AliCloud, iOS optimization)

## Custom Packages
- `packages/ali_auth_custom/`: Custom authentication package for AliCloud integration
- Local packages for specialized functionality