# Lima Flutter Mobile App

Lima is a cross-platform mobile application built with Flutter that provides comprehensive functionality for users including authentication, device management, and various integrated services.

## Core Features

- **Multi-modal Authentication**: Phone verification, WeChat, Apple ID, and carrier one-click login
- **Bluetooth Device Management**: BLE device connection, data transmission, and OTA upgrades  
- **Location Services**: Amap (高德地图) integration with real-time positioning
- **Push Notifications**: Getui SDK integration with local notifications
- **Payment Integration**: Alipay and WeChat Pay support
- **Media Capabilities**: Camera, QR code scanning, video playback, and image gallery
- **WebView Integration**: In-app web content with JavaScript bridge communication

## Target Platforms

- iOS (primary focus with extensive native integration)
- Android (with custom build configurations)

## Business Context

Lima appears to be a consumer-facing mobile app with emphasis on user authentication, device connectivity (particularly Bluetooth), and integrated services. The app includes comprehensive privacy compliance features and third-party SDK integrations.