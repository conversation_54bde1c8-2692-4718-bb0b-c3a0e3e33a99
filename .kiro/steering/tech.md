# Technology Stack

## Framework & Language
- **Flutter**: 3.27.0+ (cross-platform mobile framework)
- **Dart SDK**: 3.0.0+
- **State Management**: GetX (routing, dependency injection, state management)

## Build System
- **Android**: Gradle 8.5.2, Kotlin 1.9.24, compileSdkVersion 34
- **iOS**: Xcode with CocoaPods, Swift integration
- **JDK**: 17 required
- **Android SDK**: 35.0.0

## Key Dependencies
- **HTTP**: Dio (with cookie management)
- **UI/UX**: flutter_screenutil (responsive design), flutter_easyloading
- **Storage**: SharedPreferences, path_provider
- **WebView**: flutter_inappwebview
- **Media**: image_picker, video_player, mobile_scanner (QR codes)
- **Location**: amap_flutter_location (高德地图)
- **Bluetooth**: flutter_blue_plus
- **Push**: getuiflut (个推SDK)
- **Auth**: Custom ali_auth package, wechat_kit, sign_in_with_apple
- **Payment**: alipay_kit, wechat_kit
- **Permissions**: permission_handler
- **Crypto**: encrypt package

## Common Commands

### Development
```bash
# Install dependencies
flutter pub get

# Run app (debug)
flutter run

# Clean project
flutter clean

# Analyze code
flutter analyze
```

### Building
```bash
# Android APK
flutter build apk --release

# iOS
flutter build ios --release

# iOS Archive (from build_simple.sh)
cd ios && xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release -destination generic/platform=iOS -archivePath build/Runner.xcarchive archive

# iOS Export
cd ios && xcodebuild -exportArchive -archivePath build/Runner.xcarchive -exportPath build -exportOptionsPlist ExportOptions.plist
```

## Environment Configuration
- **Test Environment**: Uses test URLs and configurations
- **Production Environment**: Controlled via `Config.ENV` enum
- **Privacy Compliance**: Third-party SDKs initialized only after user consent

## Code Quality
- **Linting**: flutter_lints package with standard Flutter rules
- **Analysis**: analysis_options.yaml configured for Flutter best practices