#!/bin/bash

cd /Volumes/MyPassport/yingka/lima-app-frontend && flutter clean
cd /Volumes/MyPassport/yingka/lima-app-frontend && flutter pub get
cd /Volumes/MyPassport/yingka/lima-app-frontend && flutter build ios --release
cd /Volumes/MyPassport/yingka/lima-app-frontend/ios && xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release -destination generic/platform=iOS -archivePath build/Runner.xcarchive archive
cd /Volumes/MyPassport/yingka/lima-app-frontend/ios && xcodebuild -exportArchive -archivePath build/Runner.xcarchive -exportPath build -exportOptionsPlist ExportOptions.plist