plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.sean.rao.aliAuth"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.sean.rao.aliAuth"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }


    buildTypes {
        debug {
            ndk {
                abiFilters "armeabi", "armeabi-v7a","arm64-v8a", "x86", "arm64"
            }
            signingConfig signingConfigs.debug
        }
        release {
            ndk{
                //这里其实我觉可以直接是用"armeabi-v7a"，但国内几个大哥之前使用的都是"armeabi"
                abiFilters "armeabi", "armeabi-v7a"
                // abiFilters "armeabi", "armeabi-v7a","arm64-v8a", "x86", "arm64"
            }
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }
}


dependencies {
    implementation "androidx.appcompat:appcompat:1.3.0"
}

flutter {
    source '../..'
}
