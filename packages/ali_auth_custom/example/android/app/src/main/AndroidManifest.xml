<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.sean.rao.aliAuth">
   <application
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name="com.sean.rao.aliAuth.MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!--   自定义协议页面    -->
        <!--    ali_auth/example/android/app/src/main/res/layout/activity_custom_web.xml   -->
        <!--    ali_auth/example/android/app/src/main/res/drawable/icon_close.png   -->
        <activity
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode|fontScale"
            android:name="com.sean.rao.aliAuth.CustomWebViewActivity"
            android:theme="@style/AppTheme"
            android:exported="true"
            android:screenOrientation="sensorPortrait">
           <intent-filter>
               <action android:name="com.sean.rao.ali_auth_example.protocolWeb"/>
               <category android:name="android.intent.category.DEFAULT"/>
           </intent-filter>
        </activity>


       <!-- 重点关注！！！！！！！-->
       <!-- 全屏时使用主题 android:theme="@android:style/Theme.NoTitleBar.Fullscreen" -->
       <!-- 弹窗时使用主题 android:theme="@style/authsdk_activity_dialog" -->
       <!-- 如果不需要使用窗口模式，不要使用authsdk_activity_dialog主题，会出现异常动画-->
       <!-- 如果需要使用authsdk_activity_dialog主题，则screenOrientation一定不能指定明确的方向，
       比如portrait、sensorPortrait，在8.0的系统上不允许窗口模式指定orientation，会发生crash，需要指定为behind，
       然后在授权页的前一个页面指定具体的orientation-->

       <!--协议页面webview-->
       <activity
           android:name="com.mobile.auth.gatewayauth.activity.AuthWebVeiwActivity"
           android:configChanges="orientation|keyboardHidden|screenSize"
           tools:replace="android:theme"
           android:exported="false"
           android:launchMode="singleTop"
           android:screenOrientation="behind"
           android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />

       <!--联通电信授权页-->
       <activity
           android:name="com.mobile.auth.gatewayauth.LoginAuthActivity"
           android:configChanges="orientation|keyboardHidden|screenSize"
           tools:replace="android:configChanges"
           android:exported="false"
           android:launchMode="singleTop"
           android:screenOrientation="behind"
           android:theme="@style/authsdk_activity_dialog"/>

       <!--移动授权页-->
       <activity
           android:name="com.cmic.sso.sdk.activity.LoginAuthActivity"
           android:configChanges="orientation|keyboardHidden|screenSize"
           tools:replace="android:configChanges"
           android:exported="false"
           android:launchMode="singleTask"
           android:screenOrientation="behind"
           android:theme="@style/authsdk_activity_dialog" />

       <!--适配华为（huawei）刘海屏-->
       <meta-data
           android:name="android.notch_support"
           android:value="true"/>
       <!--适配小米（xiaomi）刘海屏-->
       <meta-data
           android:name="notch.config"
           android:value="portrait|landscape" />

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
