name: ali_auth
description: This is a plug-in for one click login in the alicloud number authentication service. Alibaba cloud is also used in the one click login function
version: 1.3.0
homepage: https://github.com/CodeGather/flutter_ali_auth
repository: https://github.com/CodeGather/flutter_ali_auth/tree/master/example
issue_tracker: https://github.com/CodeGather/flutter_ali_auth/issues
topics: [aliyun, phone]

environment:
  sdk: ">=2.19.0 <4.0.0"
  flutter: 3.16.8

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.1.8
  js: ">=0.6.0"

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.sean.rao.ali_auth
        pluginClass: AliAuthPlugin
      ios:
        pluginClass: AliAuthPlugin
      web:
        pluginClass: AliAuthPluginWeb
        fileName: ali_auth_web.dart

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

screenshots:
  - description: The flutter ali_auth package logo.
    path: screenshots/logo.png