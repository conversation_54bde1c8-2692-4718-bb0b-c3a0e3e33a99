<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.sean.rao.ali_auth">

    <!--允许访问网络状态的权限-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--允许修改网络状态的权限-->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <!--允许修改 WIFI 状态的权限。-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <!--允许访问 WIFI 状态的权限-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>

    <!--  跳转原生调用flutter时页面的样式  -->
    <application
        android:usesCleartextTraffic="true"
        tools:targetApi="m">
        <activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar"
            android:windowSoftInputMode="adjustResize"
            android:usesCleartextTraffic="true"/>

        <!--二次弹窗-->
        <activity
            android:name="com.mobile.auth.gatewayauth.PrivacyDialogActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            tools:replace="android:configChanges"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/authsdk_activity_dialog"/>

        <!--自定义隐私协议弹窗Activity - 确保显示在一键登录页面之上-->
        <activity
            android:name=".PrivacyDialogActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:excludeFromRecents="true"/>

    </application>
</manifest>
