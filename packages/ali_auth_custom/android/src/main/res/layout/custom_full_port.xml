<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/navColor">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/icon_close"
                android:scaleType="center"
                android:layout_centerVertical="true"
                android:cropToPadding="true"
                android:layout_marginLeft="12dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:text="@string/custom_title"/>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/phone" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="70dp"
                android:text="@string/custom_slogan"
                android:textColor="@color/black"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="350dp"
                android:text="@string/switch_msg"
                android:textSize="18sp" />

        </RelativeLayout>

    </LinearLayout>






</RelativeLayout>