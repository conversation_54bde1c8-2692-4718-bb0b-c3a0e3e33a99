package com.sean.rao.ali_auth;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;

/**
 * 透明的全屏Activity，专门用于显示隐私协议弹窗
 * 确保弹窗能够显示在一键登录页面之上
 */
public class PrivacyDialogActivity extends Activity {
    private static final String TAG = "PrivacyDialogActivity";
    
    public static final String EXTRA_TITLE = "title";
    public static final String EXTRA_MESSAGE = "message";
    public static final String EXTRA_CONFIRM_TEXT = "confirm_text";
    public static final String EXTRA_CANCEL_TEXT = "cancel_text";
    
    // 静态变量用于传递结果回调
    public static PrivacyDialogCallback callback;
    
    public interface PrivacyDialogCallback {
        void onResult(boolean agreed);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置为全屏透明Activity
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN | 
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON,
            WindowManager.LayoutParams.FLAG_FULLSCREEN | 
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
        );
        
        // 设置透明背景
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        
        // 获取传入的参数
        Intent intent = getIntent();
        String title = intent.getStringExtra(EXTRA_TITLE);
        String message = intent.getStringExtra(EXTRA_MESSAGE);
        String confirmText = intent.getStringExtra(EXTRA_CONFIRM_TEXT);
        String cancelText = intent.getStringExtra(EXTRA_CANCEL_TEXT);
        
        // 设置默认值
        if (title == null) title = "用户协议及隐私保护政策";
        if (message == null) message = "请阅读并同意用户协议";
        if (confirmText == null) confirmText = "同意";
        if (cancelText == null) cancelText = "不同意";
        
        Log.d(TAG, "显示隐私协议弹窗: " + title);
        
        // 显示弹窗
        showPrivacyDialog(title, message, confirmText, cancelText);
    }
    
    private void showPrivacyDialog(String title, String message, String confirmText, String cancelText) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(title)
               .setMessage(message)
               .setCancelable(false)
               .setPositiveButton(confirmText, (dialog, which) -> {
                   Log.d(TAG, "用户同意协议");
                   if (callback != null) {
                       callback.onResult(true);
                   }
                   finish();
               })
               .setNegativeButton(cancelText, (dialog, which) -> {
                   Log.d(TAG, "用户拒绝协议");
                   if (callback != null) {
                       callback.onResult(false);
                   }
                   finish();
               });
        
        AlertDialog dialog = builder.create();
        
        // 确保弹窗显示在最顶层
        Window window = dialog.getWindow();
        if (window != null) {
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        }
        
        dialog.show();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理回调
        callback = null;
    }
    
    @Override
    public void onBackPressed() {
        // 禁用返回键，强制用户选择
        // super.onBackPressed();
    }
}