package com.sean.rao.ali_auth.config;

import android.app.Activity;
import android.content.pm.ActivityInfo;
import android.os.Build;

import com.alibaba.fastjson2.JSONObject;
import com.mobile.auth.gatewayauth.AuthRegisterViewConfig;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.sean.rao.ali_auth.common.CustomAuthUIControlClickListener;

import io.flutter.plugin.common.EventChannel;

public class FullPortConfig extends BaseUIConfig {
    private final String TAG = "全屏竖屏样式";

    public FullPortConfig() {
        super();
    }

    @Override
    public void configAuthPage() {
        mAuthHelper.setUIClickListener(new CustomAuthUIControlClickListener());
        //添加自定义切换其他登录方式
        mAuthHelper.addAuthRegistViewConfig("switch_msg", new AuthRegisterViewConfig.Builder()
                .setView(initSwitchView(420))
                .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY)
                .build());
        
        // //添加自定义切换按钮（支持背景）
        // mAuthHelper.addAuthRegistViewConfig("custom_switch_btn", new AuthRegisterViewConfig.Builder()
        //         .setView(initCustomSwitchButton(480))
        //         .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY)
        //         .build());
        
        // 确保隐藏登录按钮的Toast和Loading
        if (jsonObject != null && jsonObject.getBooleanValue("isHiddenLoading", true)) {
            config.setLogBtnToastHidden(true);
        }
        
        int authPageOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;
        if (Build.VERSION.SDK_INT == 26) {
            authPageOrientation = ActivityInfo.SCREEN_ORIENTATION_BEHIND;
        }
        mAuthHelper.setAuthUIConfig(config.setScreenOrientation(authPageOrientation).create());
    }
}
