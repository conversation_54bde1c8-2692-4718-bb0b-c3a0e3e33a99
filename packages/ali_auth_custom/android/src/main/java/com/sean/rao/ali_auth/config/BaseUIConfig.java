package com.sean.rao.ali_auth.config;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.Surface;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hjq.toast.Toaster;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.sean.rao.ali_auth.common.Constant;

import com.sean.rao.ali_auth.common.LoginParams;
import com.sean.rao.ali_auth.utils.AppUtils;
import com.sean.rao.ali_auth.utils.UtilTool;

import java.io.IOException;

import io.flutter.plugin.common.EventChannel;

public abstract class BaseUIConfig extends LoginParams {
    public int mScreenWidthDp;
    public int mScreenHeightDp;

    public static BaseUIConfig init(int type) {
        isChecked = false;
        switch (type) {
            case Constant.FULL_PORT:
                return new FullPortConfig();
            case Constant.FULL_LAND:
                return new FullLandConfig();
            case Constant.DIALOG_PORT:
                return new DialogPortConfig();
            case Constant.DIALOG_LAND:
                return new DialogLandConfig();
            case Constant.DIALOG_BOTTOM:
                return new DialogBottomConfig();
            case Constant.CUSTOM_XML:
                return new CustomXmlConfig();
            default:
                if (jsonObject.getString("pageBackgroundPath") != null && !jsonObject.getString("pageBackgroundPath").isEmpty()) {
                    if (jsonObject.getString("pageBackgroundPath").equals("xml")) {
                        return new CustomXmlConfig();
                    } else if (jsonObject.getString("pageBackgroundPath").equals("view")) {
                        return new CustomViewConfig();
                    } else {
                        return new CustomAssetsConfig();
                    }
                }
                return null;
        }
    }


    /**
     * 删除自定义布局参数，防止内存溢出
     */
    public BaseUIConfig() {
        // 防止内存泄漏
        mAuthHelper.removeAuthRegisterXmlConfig();
        mAuthHelper.removeAuthRegisterViewConfig();
    }

    /**
     * 第三方布局设置
     * @param marginTop
     * @return
     */
    protected View initSwitchView(int marginTop) {
        JSONObject customThirdView = jsonObject.getJSONObject("customThirdView");
        /// 名称列表
        JSONArray customThirdViewName = customThirdView.getJSONArray("viewItemName");
        /// 图片路径列表
        JSONArray customThirdViewItem = customThirdView.getJSONArray("viewItemPath");
        if (customThirdViewName != null && customThirdViewItem != null) {
            // 创建主容器布局 - 垂直布局包含分割线和第三方登录按钮
            LinearLayout mainContainer = new LinearLayout(mContext);
            mainContainer.setOrientation(LinearLayout.VERTICAL);
            LinearLayout.LayoutParams mainLayoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
            );
            // 读取 customThirdView 的 top 属性，如果没有设置则使用默认的 marginTop
            float customTop = customThirdView.getFloatValue("top");
            int finalTopMargin = customTop > 0 ? (int)customTop : marginTop;
            mainLayoutParams.setMargins(0, AppUtils.dp2px(mContext, finalTopMargin), 0, 0);
            mainContainer.setLayoutParams(mainLayoutParams);
            
            // 添加分割线图片
            try {
                // 从customThirdView配置中读取分割线图片路径
                String dividerImagePath = customThirdView.getString("dividerImagePath");
                if (dividerImagePath == null || dividerImagePath.isEmpty()) {
                    dividerImagePath = "assets/images/third-party-bg_2x.png"; // 默认路径
                }
                
                android.widget.ImageView dividerImageView = new android.widget.ImageView(mContext);
                dividerImageView.setImageDrawable(
                    UtilTool.getBitmapToBitmapDrawable(
                        mContext,
                        UtilTool.flutterToPath(dividerImagePath)
                    )
                );
                dividerImageView.setScaleType(android.widget.ImageView.ScaleType.FIT_XY);
                
                // 设置分割线图片参数
                LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    AppUtils.dp2px(mContext, 20) // 高度20dp
                );
                dividerParams.setMargins(
                    AppUtils.dp2px(mContext, 20), // 左边距20dp
                    0,
                    AppUtils.dp2px(mContext, 20), // 右边距20dp
                    AppUtils.dp2px(mContext, 20)  // 下边距20dp
                );
                dividerImageView.setLayoutParams(dividerParams);
                mainContainer.addView(dividerImageView);
            } catch (Exception e) {
                // 如果分割线图片加载失败，继续执行
                e.printStackTrace();
            }
            
            LinearLayout linearLayout = new LinearLayout(mContext);
            // 创建一个最大宽度和适量高度的布局
            LinearLayout.LayoutParams LayoutParams = new LinearLayout.LayoutParams(
                    customThirdView.getFloatValue("width") > 0 ? AppUtils.dp2px(mContext, customThirdView.getFloatValue("width")) : LinearLayout.LayoutParams.MATCH_PARENT,
                    customThirdView.getFloatValue("height") > 0 ? AppUtils.dp2px(mContext, customThirdView.getFloatValue("height")) : LinearLayout.LayoutParams.WRAP_CONTENT
            );

            // 居中边距
            LayoutParams.setMargins(
                    AppUtils.dp2px(mContext, customThirdView.getFloatValue("left") > 0 ? customThirdView.getFloatValue("left") : 10),
                    0, // top边距设为0，因为已经在主容器中设置了
                    AppUtils.dp2px(mContext, customThirdView.getFloatValue("right") > 0 ? customThirdView.getFloatValue("right") : 10),
                    AppUtils.dp2px(mContext, customThirdView.getFloatValue("bottom") > 0 ? customThirdView.getFloatValue("bottom") : 10)
            );
            linearLayout.setLayoutParams(LayoutParams);
            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
            linearLayout.setGravity(Gravity.CENTER_HORIZONTAL);

            for (int i = 0; i < customThirdViewItem.size(); i++) {
                if (customThirdViewItem.get(i) != null && !String.valueOf(customThirdViewItem.get(i)).isEmpty()) {
                    int finalI = i;
                    /// 每个item布局
                    LinearLayout itemLinearLayout = new LinearLayout(mContext);
                    /// 按钮和文字布局
                    itemLinearLayout.setOrientation(LinearLayout.VERTICAL);
                    /// 按钮控件
                    ImageButton itemButton = new ImageButton(mActivity);
                    /// 需要转化路径
                    try {
                        itemButton.setBackground(
                                UtilTool.getBitmapToBitmapDrawable(
                                        mContext,
                                        UtilTool.flutterToPath(String.valueOf(customThirdViewItem.get(i)))
                                )
                        );
                    } catch (IOException e) {
                        // eventSink.success(UtilTool.resultFormatData("500000", null, e.getMessage()));
                        showResult("500000", "出现错误", e.getMessage());
                    }
                    ViewGroup.LayoutParams buttonLayoutParams = new ViewGroup.LayoutParams(
                            AppUtils.dp2px(mContext, customThirdView.getFloatValue("itemWidth") > 0 ? customThirdView.getFloatValue("itemWidth") : 60),
                            AppUtils.dp2px(mContext, customThirdView.getFloatValue("itemHeight") > 0 ? customThirdView.getFloatValue("itemHeight") : 60)
                    );
                    itemButton.setLayoutParams(buttonLayoutParams);

                    /// 第三方按钮的点击事件
                    itemButton.setOnClickListener(v -> {
                        // 判断是否隐藏toast
                        showResult("700005", "点击第三方登录按钮", finalI);
                        // eventSink.success(UtilTool.resultFormatData("600019", null, finalI));
                        if (!jsonObject.getBooleanValue("isHideToast") && !isChecked) {
                            Toaster.show(jsonObject.getString("toastText"));
                            return;
                        }
                        if (jsonObject.getBooleanValue("autoQuitPage")) {
                            mAuthHelper.quitLoginPage();
                        }
                    });
                    itemLinearLayout.addView(itemButton);

                    Object itemName = customThirdViewName.get(i);
                    if (itemName != null && !String.valueOf(itemName).isEmpty()) {
                        // 按钮下文字控件
                        TextView textView = new TextView(mContext);
                        textView.setText(String.valueOf(itemName));
                        // 文字颜色
                        textView.setTextColor(customThirdView.getString("color") != null && !customThirdView.getString("color").isEmpty() ? Color.parseColor(customThirdView.getString("color")) : Color.BLACK);
                        textView.setTextSize(
                                TypedValue.COMPLEX_UNIT_SP,
                                customThirdView.getFloatValue("size") > 0 ? customThirdView.getFloatValue("size") : 14F
                        );
                        textView.setGravity(Gravity.CENTER);
                        itemLinearLayout.addView(textView);
                    }

                    /// 新增按钮间距控件
                    if (i > 0 && i < customThirdViewItem.size()) {
                        Space space = new Space(mContext);
                        space.setLayoutParams(new ViewGroup.LayoutParams(
                                AppUtils.dp2px(mContext, customThirdView.getFloatValue("space") > 0 ? customThirdView.getFloatValue("space") : 10),
                                LinearLayout.LayoutParams.MATCH_PARENT)
                        );
                        linearLayout.addView(space);
                    }
                    /// 将item放入布局中
                    linearLayout.addView(itemLinearLayout);
                }
            }
            
            // 将第三方登录按钮布局添加到主容器中
            mainContainer.addView(linearLayout);

            return mainContainer;
        } else {
            return null;
        }
    }

    /**
     * 创建自定义切换按钮（支持背景）
     * @param marginTop
     * @return
     */
    protected View initCustomSwitchButton(int marginTop) {
        String switchAccBackgroundPath = jsonObject.getString("switchAccBackgroundPath");
        String switchAccText = jsonObject.getString("switchAccText");
        String switchAccTextColor = jsonObject.getString("switchAccTextColor");
        boolean switchAccHidden = jsonObject.getBooleanValue("switchAccHidden");
        
        if (switchAccHidden || switchAccBackgroundPath == null || switchAccBackgroundPath.isEmpty()) {
            return null;
        }

        LinearLayout linearLayout = new LinearLayout(mContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        layoutParams.setMargins(
                AppUtils.dp2px(mContext, 28),
                AppUtils.dp2px(mContext, marginTop),
                AppUtils.dp2px(mContext, 28),
                AppUtils.dp2px(mContext, 10)
        );
        linearLayout.setLayoutParams(layoutParams);
        linearLayout.setGravity(Gravity.CENTER_HORIZONTAL);

        // 创建切换按钮
        android.widget.Button switchButton = new android.widget.Button(mContext);
        
        // 设置按钮背景
        try {
            String[] backgrounds = switchAccBackgroundPath.split(",");
            String normalBackground = backgrounds[0].trim();
            
            // 创建StateListDrawable来处理不同状态的背景
            android.graphics.drawable.StateListDrawable stateListDrawable = new android.graphics.drawable.StateListDrawable();
            
            // 正常状态背景
            android.graphics.drawable.Drawable normalDrawable = UtilTool.getBitmapToBitmapDrawable(
                    mContext,
                    UtilTool.flutterToPath(normalBackground)
            );
            stateListDrawable.addState(new int[]{}, normalDrawable);
            
            // 如果有按压状态背景
            if (backgrounds.length > 1) {
                String pressedBackground = backgrounds[1].trim();
                android.graphics.drawable.Drawable pressedDrawable = UtilTool.getBitmapToBitmapDrawable(
                        mContext,
                        UtilTool.flutterToPath(pressedBackground)
                );
                stateListDrawable.addState(new int[]{android.R.attr.state_pressed}, pressedDrawable);
            }
            
            switchButton.setBackground(stateListDrawable);
        } catch (IOException e) {
            showResult("500000", "切换按钮背景加载失败", e.getMessage());
        }

        // 设置按钮文字
        if (switchAccText != null && !switchAccText.isEmpty()) {
            switchButton.setText(switchAccText);
        } else {
            switchButton.setText("切换手机号登录");
        }

        // 设置文字颜色
        if (switchAccTextColor != null && !switchAccTextColor.isEmpty()) {
            try {
                switchButton.setTextColor(Color.parseColor(switchAccTextColor));
            } catch (Exception e) {
                switchButton.setTextColor(Color.WHITE);
            }
        }

        // 设置按钮尺寸
        ViewGroup.LayoutParams buttonParams = new ViewGroup.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                AppUtils.dp2px(mContext, 45)
        );
        switchButton.setLayoutParams(buttonParams);

        // 设置点击事件
        switchButton.setOnClickListener(v -> {
            showResult("700001", "用户点击了切换按钮", "");
            if (jsonObject.getBooleanValue("autoQuitPage")) {
                mAuthHelper.quitLoginPage();
            }
        });

        linearLayout.addView(switchButton);
        return linearLayout;
    }

    /**
     * 更新屏幕
     * @param authPageScreenOrientation
     */
    protected void updateScreenSize(int authPageScreenOrientation) {
        int screenHeightDp = AppUtils.px2dp(mContext, AppUtils.getPhoneHeightPixels(mContext));
        int screenWidthDp = AppUtils.px2dp(mContext, AppUtils.getPhoneWidthPixels(mContext));
        int rotation = mActivity.getWindowManager().getDefaultDisplay().getRotation();
        if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_BEHIND) {
            authPageScreenOrientation = mActivity.getRequestedOrientation();
        }
        if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_LANDSCAPE) {
            rotation = Surface.ROTATION_90;
        } else if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_PORTRAIT) {
            rotation = Surface.ROTATION_180;
        }
        switch (rotation) {
            case Surface.ROTATION_0:
            case Surface.ROTATION_180:
                mScreenWidthDp = screenWidthDp;
                mScreenHeightDp = screenHeightDp;
                break;
            case Surface.ROTATION_90:
            case Surface.ROTATION_270:
                mScreenWidthDp = screenHeightDp;
                mScreenHeightDp = screenWidthDp;
                break;
            default:
                break;
        }
    }

    public abstract void configAuthPage();

    private void configBuild(){}

    /**
     *  在横屏APP弹竖屏一键登录页面或者竖屏APP弹横屏授权页时处理特殊逻辑
     *  Android8.0只能启动SCREEN_ORIENTATION_BEHIND模式的Activity
     */
    public void onResume() {}
}
