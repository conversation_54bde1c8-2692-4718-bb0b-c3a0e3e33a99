package com.sean.rao.ali_auth.common;

/**
 * @ProjectName: android
 * @Package: com.sean.rao.ali_auth.common
 * @ClassName: MediaFileUtil
 * @Description: java类作用描述
 * @Author: liys
 * @CreateDate: 5/1/22 10:33 AM
 * @UpdateUser: 更新者
 * @UpdateDate: 5/1/22 10:33 AM
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
import java.util.HashMap;
import java.util.Iterator;


/**
 * 判断文件的类型  视频 音频  图片
 */
public class MediaFileUtil {
  public static String sFileExtensions;

  // Audio
  public static final int FILE_TYPE_MP3     = 1;
  public static final int FILE_TYPE_M4A     = 2;
  public static final int FILE_TYPE_WAV     = 3;
  public static final int FILE_TYPE_AMR     = 4;
  public static final int FILE_TYPE_AWB     = 5;
  public static final int FILE_TYPE_WMA     = 6;
  public static final int FILE_TYPE_OGG     = 7;
  private static final int FIRST_AUDIO_FILE_TYPE = FILE_TYPE_MP3;
  private static final int LAST_AUDIO_FILE_TYPE = FILE_TYPE_OGG;

  // MIDI
  public static final int FILE_TYPE_MID     = 11;
  public static final int FILE_TYPE_SMF     = 12;
  public static final int FILE_TYPE_IMY     = 13;
  private static final int FIRST_MIDI_FILE_TYPE = FILE_TYPE_MID;
  private static final int LAST_MIDI_FILE_TYPE = FILE_TYPE_IMY;

  // Video
  public static final int FILE_TYPE_MP4     = 21;
  public static final int FILE_TYPE_M4V     = 22;
  public static final int FILE_TYPE_3GPP    = 23;
  public static final int FILE_TYPE_3GPP2   = 24;
  public static final int FILE_TYPE_WMV     = 25;
  private static final int FIRST_VIDEO_FILE_TYPE = FILE_TYPE_MP4;
  private static final int LAST_VIDEO_FILE_TYPE = FILE_TYPE_WMV;

  // Image
  public static final int FILE_TYPE_JPEG    = 31;
  public static final int FILE_TYPE_GIF     = 32;
  public static final int FILE_TYPE_PNG     = 33;
  public static final int FILE_TYPE_BMP     = 34;
  public static final int FILE_TYPE_WBMP    = 35;
  private static final int FIRST_IMAGE_FILE_TYPE = FILE_TYPE_JPEG;
  private static final int LAST_IMAGE_FILE_TYPE = FILE_TYPE_WBMP;

  // Playlist
  public static final int FILE_TYPE_M3U     = 41;
  public static final int FILE_TYPE_PLS     = 42;
  public static final int FILE_TYPE_WPL     = 43;
  private static final int FIRST_PLAYLIST_FILE_TYPE = FILE_TYPE_M3U;
  private static final int LAST_PLAYLIST_FILE_TYPE = FILE_TYPE_WPL;

  //静态内部类
  public static class MediaFileType {

    int fileType;
    String mimeType;

    MediaFileType(int fileType, String mimeType) {
      this.fileType = fileType;
      this.mimeType = mimeType;
    }
  }

  private static HashMap<String, MediaFileType> sFileTypeMap
          = new HashMap<String, MediaFileType>();
  private static HashMap<String, Integer> sMimeTypeMap
          = new HashMap<String, Integer>();
  static void addFileType(String extension, int fileType, String mimeType) {
    sFileTypeMap.put(extension, new MediaFileType(fileType, mimeType));
    sMimeTypeMap.put(mimeType, new Integer(fileType));
  }
  static {
    addFileType("MP3", FILE_TYPE_MP3, "audio/mpeg");
    addFileType("M4A", FILE_TYPE_M4A, "audio/mp4");
    addFileType("WAV", FILE_TYPE_WAV, "audio/x-wav");
    addFileType("AMR", FILE_TYPE_AMR, "audio/amr");
    addFileType("AWB", FILE_TYPE_AWB, "audio/amr-wb");
    addFileType("WMA", FILE_TYPE_WMA, "audio/x-ms-wma");
    addFileType("OGG", FILE_TYPE_OGG, "application/ogg");

    addFileType("MID", FILE_TYPE_MID, "audio/midi");
    addFileType("XMF", FILE_TYPE_MID, "audio/midi");
    addFileType("RTTTL", FILE_TYPE_MID, "audio/midi");
    addFileType("SMF", FILE_TYPE_SMF, "audio/sp-midi");
    addFileType("IMY", FILE_TYPE_IMY, "audio/imelody");

    addFileType("MP4", FILE_TYPE_MP4, "video/mp4");
    addFileType("M4V", FILE_TYPE_M4V, "video/mp4");
    addFileType("3GP", FILE_TYPE_3GPP, "video/3gpp");
    addFileType("3GPP", FILE_TYPE_3GPP, "video/3gpp");
    addFileType("3G2", FILE_TYPE_3GPP2, "video/3gpp2");
    addFileType("3GPP2", FILE_TYPE_3GPP2, "video/3gpp2");
    addFileType("WMV", FILE_TYPE_WMV, "video/x-ms-wmv");

    addFileType("JPG", FILE_TYPE_JPEG, "image/jpeg");
    addFileType("JPEG", FILE_TYPE_JPEG, "image/jpeg");
    addFileType("GIF", FILE_TYPE_GIF, "image/gif");
    addFileType("PNG", FILE_TYPE_PNG, "image/png");
    addFileType("BMP", FILE_TYPE_BMP, "image/x-ms-bmp");
    addFileType("WBMP", FILE_TYPE_WBMP, "image/vnd.wap.wbmp");

    addFileType("M3U", FILE_TYPE_M3U, "audio/x-mpegurl");
    addFileType("PLS", FILE_TYPE_PLS, "audio/x-scpls");
    addFileType("WPL", FILE_TYPE_WPL, "application/vnd.ms-wpl");

    // compute file extensions list for native Media Scanner
    StringBuilder builder = new StringBuilder();
    Iterator<String> iterator = sFileTypeMap.keySet().iterator();

    while (iterator.hasNext()) {
      if (builder.length() > 0) {
        builder.append(',');
      }
      builder.append(iterator.next());
    }
    sFileExtensions = builder.toString();
  }

  public static final String UNKNOWN_STRING = "<unknown>";

  public static boolean isAudioFileType(int fileType) {
    return ((fileType >= FIRST_AUDIO_FILE_TYPE &&
            fileType <= LAST_AUDIO_FILE_TYPE) ||
            (fileType >= FIRST_MIDI_FILE_TYPE &&
                    fileType <= LAST_MIDI_FILE_TYPE));
  }

  public static boolean isVideoFileType(int fileType) {
    return (fileType >= FIRST_VIDEO_FILE_TYPE &&
            fileType <= LAST_VIDEO_FILE_TYPE);
  }

  public static boolean isImageFileType(int fileType) {
    return (fileType >= FIRST_IMAGE_FILE_TYPE &&
            fileType <= LAST_IMAGE_FILE_TYPE);
  }

  public static boolean isPlayListFileType(int fileType) {
    return (fileType >= FIRST_PLAYLIST_FILE_TYPE &&
            fileType <= LAST_PLAYLIST_FILE_TYPE);
  }

  public static MediaFileType getFileType(String path) {
    int lastDot = path.lastIndexOf(".");
    if (lastDot < 0)
      return null;
    return sFileTypeMap.get(path.substring(lastDot + 1).toUpperCase());
  }

  //根据视频文件路径判断文件类型
  public static boolean isVideoFileType(String path) {
    MediaFileType type = getFileType(path);
    if(null != type) {
      return isVideoFileType(type.fileType);
    }
    return false;
  }

  //根据音频文件路径判断文件类型
  public static boolean isAudioFileType(String path) {
    MediaFileType type = getFileType(path);
    if(null != type) {
      return isAudioFileType(type.fileType);
    }
    return false;
  }

  //根据mime类型查看文件类型
  public static int getFileTypeForMimeType(String mimeType) {
    Integer value = sMimeTypeMap.get(mimeType);
    return (value == null ? 0 : value.intValue());
  }

  //根据图片文件路径判断是否时gif文件类型
  public static boolean isImageGifFileType(String path) {
    MediaFileType type = getFileType(path);
    if(null != type) {
      return FILE_TYPE_GIF == type.fileType;
    }
    return false;
  }

  //根据图片文件路径判断文件类型
  public static boolean isImageFileType(String path) {
    MediaFileType type = getFileType(path);
    if(null != type) {
      return isImageFileType(type.fileType);
    }
    return false;
  }
}
