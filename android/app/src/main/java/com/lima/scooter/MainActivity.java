package com.lima.scooter;

import android.content.Intent;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
// MobSDK 和 MobLink 已移除
// import com.mob.moblink.MobLink;
// import com.mob.MobSDK;

public class MainActivity extends FlutterActivity {
    // MobLink channel 已移除
    // private static final String CHANNEL = "moblink";

    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        // 注册阿里云DYPNS插件
        flutterEngine.getPlugins().add(new AlicloudDYPNSPlugin());

        // MobLink MethodChannel 已移除
        // MobSDK 相关功能已移除
    }

    // MobLink onNewIntent 处理已移除
    // @Override
    // protected void onNewIntent(Intent intent) {
    //     super.onNewIntent(intent);
    //     setIntent(intent);
    //     MobLink.updateNewIntent(getIntent(), this);
    // }
}
