package com.lima.scooter;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * 阿里云号码认证服务 (DYPNS) Flutter插件
 * 支持中国移动、中国联通、中国电信的一键登录功能
 * 官方文档：https://www.aliyun.com/product/dypns
 * 
 * 使用说明：
 * 1. 在build.gradle中添加阿里云DYPNS SDK依赖
 * 2. 在阿里云控制台申请号码认证服务
 * 3. 配置AccessKey和AccessKeySecret
 */
public class AlicloudDYPNSPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "AlicloudDYPNS";
    private static final String CHANNEL = "alicloud_auth";
    
    private MethodChannel channel;
    private Context context;
    private Activity activity;

    // 阿里云DYPNS配置
    private String accessKeyId;
    private String accessKeySecret;
    private String scheme;

    // 第三方登录视图
    private AliAuthThirdPartyView thirdPartyView;
    
    // 运营商类型
    private static final String CHINA_MOBILE = "中国移动";
    private static final String CHINA_UNICOM = "中国联通";
    private static final String CHINA_TELECOM = "中国电信";

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "initialize":
                initialize(call, result);
                break;
            case "checkEnvironment":
                checkEnvironment(result);
                break;
            case "accelerateLoginPage":
                accelerateLoginPage(call, result);
                break;
            case "getLoginToken":
                getLoginToken(call, result);
                break;
            case "hideLoginLoading":
                hideLoginLoading(result);
                break;
            case "quitLoginPage":
                quitLoginPage(result);
                break;
            case "getCurrentCarrierName":
                getCurrentCarrierName(result);
                break;
            case "verifyToken":
                verifyToken(call, result);
                break;
            case "createThirdPartyView":
                createThirdPartyView(call, result);
                break;
            case "simulateThirdPartyClick":
                simulateThirdPartyClick(call, result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * 初始化阿里云DYPNS SDK
     */
    private void initialize(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始初始化阿里云DYPNS SDK");
            
            accessKeyId = call.argument("accessKeyId");
            accessKeySecret = call.argument("accessKeySecret");
            scheme = call.argument("scheme");
            String environment = call.argument("environment");
            
            // TODO: 集成真实的阿里云DYPNS SDK
            // 需要在build.gradle中添加依赖：
            // implementation 'com.aliyun.dpa:oss-android-sdk:+'
            
            // 示例代码（需要真实SDK）：
            // PNSAuthHelper.getInstance(context, accessKeyId, accessKeySecret);
            // PNSAuthHelper.getInstance().setAuthSDKInfo(scheme);
            
            Log.d(TAG, "阿里云DYPNS SDK初始化成功");
            result.success(true);
        } catch (Exception e) {
            Log.e(TAG, "阿里云DYPNS SDK初始化失败", e);
            result.success(false);
        }
    }

    /**
     * 检查环境是否支持一键登录
     */
    private void checkEnvironment(Result result) {
        try {
            Map<String, Object> availability = new HashMap<>();
            
            boolean isWifiConnected = isWifiConnected();
            String operatorType = getCurrentOperatorType();
            
            // 一键登录需要移动网络
            boolean isAvailable = !isWifiConnected && !operatorType.equals("未知运营商");
            
            availability.put("isAvailable", isAvailable);
            availability.put("isWifiConnected", isWifiConnected);
            availability.put("operatorType", operatorType);
            
            if (!isAvailable) {
                if (isWifiConnected) {
                    availability.put("errorCode", "WIFI_CONNECTED");
                    availability.put("errorMessage", "请关闭WiFi，使用移动网络");
                } else {
                    availability.put("errorCode", "OPERATOR_NOT_SUPPORTED");
                    availability.put("errorMessage", "当前运营商不支持一键登录");
                }
            }
            
            Log.d(TAG, "环境检查完成: " + isAvailable);
            result.success(availability);
        } catch (Exception e) {
            Log.e(TAG, "环境检查失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("isAvailable", false);
            error.put("errorCode", "CHECK_ERROR");
            error.put("errorMessage", "检查失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 预取号（加速登录页面）
     */
    private void accelerateLoginPage(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始预取号");
            
            Integer timeout = call.argument("timeout");
            
            // TODO: 调用阿里云DYPNS预取号API
            // PNSAuthHelper.getInstance().accelerateLoginPage(timeout, callback);
            
            // 模拟预取号成功
            Map<String, Object> preLoginResult = new HashMap<>();
            preLoginResult.put("success", true);
            preLoginResult.put("preLoginToken", "prelogin_token_" + System.currentTimeMillis());
            
            Log.d(TAG, "预取号成功");
            result.success(preLoginResult);
        } catch (Exception e) {
            Log.e(TAG, "预取号失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("errorCode", "PRELOGIN_ERROR");
            error.put("errorMessage", "预取号失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 获取登录Token
     */
    private void getLoginToken(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始获取登录Token");
            
            Integer timeout = call.argument("timeout");
            Map<String, Object> authUIConfig = call.argument("authUIConfig");
            
            // TODO: 调用阿里云DYPNS获取登录Token API
            // PNSAuthHelper.getInstance().getLoginToken(activity, timeout, authUIConfig, callback);
            
            // 模拟获取Token成功
            Map<String, Object> loginResult = new HashMap<>();
            loginResult.put("success", true);
            loginResult.put("token", "alicloud_token_" + System.currentTimeMillis());
            loginResult.put("accessCode", "encrypted_phone_number");
            loginResult.put("operatorType", getCurrentOperatorType());
            loginResult.put("phoneNumber", "138****8888");
            
            Log.d(TAG, "获取登录Token成功");
            result.success(loginResult);
        } catch (Exception e) {
            Log.e(TAG, "获取登录Token失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("errorCode", "LOGIN_ERROR");
            error.put("errorMessage", "获取Token失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 隐藏登录加载页面
     */
    private void hideLoginLoading(Result result) {
        try {
            // TODO: 调用阿里云DYPNS隐藏加载页面API
            // PNSAuthHelper.getInstance().hideLoginLoading();
            result.success(null);
        } catch (Exception e) {
            Log.e(TAG, "隐藏登录加载页面失败", e);
            result.error("HIDE_ERROR", "隐藏加载页面失败", e.getMessage());
        }
    }

    /**
     * 退出登录页面
     */
    private void quitLoginPage(Result result) {
        try {
            // TODO: 调用阿里云DYPNS退出登录页面API
            // PNSAuthHelper.getInstance().quitLoginPage();
            result.success(null);
        } catch (Exception e) {
            Log.e(TAG, "退出登录页面失败", e);
            result.error("QUIT_ERROR", "退出登录页面失败", e.getMessage());
        }
    }

    /**
     * 获取当前运营商名称
     */
    private void getCurrentCarrierName(Result result) {
        try {
            String operatorType = getCurrentOperatorType();
            result.success(operatorType);
        } catch (Exception e) {
            Log.e(TAG, "获取运营商名称失败", e);
            result.success("未知运营商");
        }
    }

    /**
     * 验证Token
     */
    private void verifyToken(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始验证Token");
            
            String token = call.argument("token");
            String keyId = call.argument("accessKeyId");
            String keySecret = call.argument("accessKeySecret");
            
            // TODO: 调用阿里云DYPNS服务端API验证Token
            // 这里需要调用阿里云的服务端API来验证Token并获取真实手机号
            
            // 模拟验证成功
            Map<String, Object> verifyResult = new HashMap<>();
            verifyResult.put("success", true);
            verifyResult.put("phoneNumber", "13800138000");
            verifyResult.put("carrier", getCurrentOperatorType());
            
            Log.d(TAG, "Token验证成功");
            result.success(verifyResult);
        } catch (Exception e) {
            Log.e(TAG, "Token验证失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("errorCode", "VERIFY_ERROR");
            error.put("errorMessage", "验证失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 检查是否连接WiFi
     */
    private boolean isWifiConnected() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            return networkInfo != null && networkInfo.getType() == ConnectivityManager.TYPE_WIFI;
        } catch (Exception e) {
            Log.e(TAG, "检查WiFi连接状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前运营商类型
     */
    private String getCurrentOperatorType() {
        try {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String operator = tm.getSimOperator();
            
            if (operator != null) {
                if (operator.startsWith("460") && (operator.endsWith("00") || operator.endsWith("02") || operator.endsWith("07"))) {
                    return CHINA_MOBILE;
                } else if (operator.startsWith("460") && (operator.endsWith("01") || operator.endsWith("06") || operator.endsWith("09"))) {
                    return CHINA_UNICOM;
                } else if (operator.startsWith("460") && (operator.endsWith("03") || operator.endsWith("05") || operator.endsWith("11"))) {
                    return CHINA_TELECOM;
                }
            }
            
            return "未知运营商";
        } catch (Exception e) {
            Log.e(TAG, "获取运营商类型失败", e);
            return "未知运营商";
        }
    }

    /**
     * 创建第三方登录视图
     */
    private void createThirdPartyView(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始创建第三方登录视图");

            if (activity == null) {
                result.error("NO_ACTIVITY", "Activity未初始化", null);
                return;
            }

            // 获取customThirdView配置
            Map<String, Object> customThirdViewMap = call.argument("customThirdView");
            if (customThirdViewMap == null) {
                result.error("NO_CONFIG", "customThirdView配置为空", null);
                return;
            }

            // 转换为JSONObject
            JSONObject customThirdView = new JSONObject(customThirdViewMap);

            // 创建第三方登录视图
            if (thirdPartyView == null) {
                thirdPartyView = new AliAuthThirdPartyView(context, activity);
                thirdPartyView.setOnThirdPartyClickListener(new AliAuthThirdPartyView.OnThirdPartyClickListener() {
                    @Override
                    public void onThirdPartyClick(int index, String action) {
                        Log.d(TAG, "第三方登录按钮点击: index=" + index + ", action=" + action);

                        // 发送事件到Flutter
                        Map<String, Object> eventData = new HashMap<>();
                        eventData.put("eventType", "700005");
                        eventData.put("message", "点击第三方登录按钮");
                        eventData.put("data", String.valueOf(index));

                        channel.invokeMethod("onThirdPartyLoginClick", eventData);
                    }
                });
            }

            View thirdPartyViewWidget = thirdPartyView.initSwitchView(customThirdView, 400);

            if (thirdPartyViewWidget != null) {
                Log.d(TAG, "第三方登录视图创建成功");
                result.success(true);
            } else {
                Log.w(TAG, "第三方登录视图创建失败");
                result.success(false);
            }

        } catch (Exception e) {
            Log.e(TAG, "创建第三方登录视图失败", e);
            result.error("CREATE_ERROR", "创建失败: " + e.getMessage(), null);
        }
    }

    /**
     * 模拟第三方登录按钮点击（用于测试）
     */
    private void simulateThirdPartyClick(MethodCall call, Result result) {
        try {
            Integer index = call.argument("index");
            if (index == null) {
                index = 0;
            }

            Log.d(TAG, "模拟第三方登录按钮点击: " + index);

            // 发送事件到Flutter
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("eventType", "700005");
            eventData.put("message", "点击第三方登录按钮");
            eventData.put("data", String.valueOf(index));

            channel.invokeMethod("onThirdPartyLoginClick", eventData);
            result.success(true);

        } catch (Exception e) {
            Log.e(TAG, "模拟第三方登录点击失败", e);
            result.error("SIMULATE_ERROR", "模拟点击失败: " + e.getMessage(), null);
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }
}
