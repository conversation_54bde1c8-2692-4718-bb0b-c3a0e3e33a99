package com.lima.scooter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;

/**
 * AliAuth第三方登录视图扩展类
 * 实现类似AliAuth SDK中initSwitchView方法的功能
 * 用于创建第三方登录按钮（微信、Apple等）
 */
public class AliAuthThirdPartyView {
    private static final String TAG = "AliAuthThirdParty";
    
    private Context mContext;
    private Activity mActivity;
    private OnThirdPartyClickListener mClickListener;
    
    public interface OnThirdPartyClickListener {
        void onThirdPartyClick(int index, String action);
    }
    
    public AliAuthThirdPartyView(Context context, Activity activity) {
        this.mContext = context;
        this.mActivity = activity;
    }
    
    public void setOnThirdPartyClickListener(OnThirdPartyClickListener listener) {
        this.mClickListener = listener;
    }
    
    /**
     * 第三方布局设置
     * 根据customThirdView配置创建第三方登录按钮
     * @param customThirdView JSON配置对象
     * @param marginTop 顶部边距
     * @return 创建的视图
     */
    public View initSwitchView(JSONObject customThirdView, int marginTop) {
        try {
            if (customThirdView == null) {
                Log.w(TAG, "customThirdView配置为空");
                return null;
            }
            
            Log.d(TAG, "开始创建第三方登录视图，配置: " + customThirdView.toString());
            
            // 获取图片路径列表
            JSONArray viewItemPath = customThirdView.optJSONArray("viewItemPath");
            // 获取名称列表
            JSONArray viewItemName = customThirdView.optJSONArray("viewItemName");
            
            if (viewItemPath == null || viewItemPath.length() == 0) {
                Log.w(TAG, "viewItemPath为空，无法创建第三方登录按钮");
                return null;
            }
            
            // 创建主容器
            LinearLayout linearLayout = new LinearLayout(mContext);
            
            // 设置布局参数
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    customThirdView.optInt("width", 300) > 0 ? 
                        dp2px(customThirdView.optInt("width", 300)) : LinearLayout.LayoutParams.MATCH_PARENT,
                    customThirdView.optInt("height", 80) > 0 ? 
                        dp2px(customThirdView.optInt("height", 80)) : LinearLayout.LayoutParams.WRAP_CONTENT
            );
            
            // 设置边距
            layoutParams.setMargins(
                    dp2px(customThirdView.optInt("left", 10)),
                    dp2px(customThirdView.optInt("top", marginTop)),
                    dp2px(customThirdView.optInt("right", 10)),
                    dp2px(customThirdView.optInt("bottom", 10))
            );
            
            linearLayout.setLayoutParams(layoutParams);
            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
            linearLayout.setGravity(Gravity.CENTER_HORIZONTAL);
            
            // 创建每个第三方登录按钮
            for (int i = 0; i < viewItemPath.length(); i++) {
                String imagePath = viewItemPath.optString(i);
                if (imagePath == null || imagePath.isEmpty()) {
                    continue;
                }
                
                Log.d(TAG, "创建第" + i + "个按钮，图片路径: " + imagePath);
                
                final int index = i;
                
                // 创建按钮容器
                LinearLayout itemLayout = new LinearLayout(mContext);
                itemLayout.setOrientation(LinearLayout.VERTICAL);
                itemLayout.setGravity(Gravity.CENTER);
                
                // 创建图片按钮
                ImageButton imageButton = new ImageButton(mActivity);
                
                // 加载Flutter assets图片
                try {
                    BitmapDrawable drawable = loadFlutterAsset(imagePath);
                    if (drawable != null) {
                        imageButton.setBackground(drawable);
                        Log.d(TAG, "成功加载图片: " + imagePath);
                    } else {
                        Log.w(TAG, "无法加载图片: " + imagePath);
                        // 设置默认背景色
                        imageButton.setBackgroundColor(i == 0 ? Color.parseColor("#07C160") : Color.parseColor("#000000"));
                    }
                } catch (Exception e) {
                    Log.e(TAG, "加载图片失败: " + imagePath, e);
                    // 设置默认背景色
                    imageButton.setBackgroundColor(i == 0 ? Color.parseColor("#07C160") : Color.parseColor("#000000"));
                }
                
                // 设置按钮尺寸
                ViewGroup.LayoutParams buttonParams = new ViewGroup.LayoutParams(
                        dp2px(customThirdView.optInt("itemWidth", 60)),
                        dp2px(customThirdView.optInt("itemHeight", 60))
                );
                imageButton.setLayoutParams(buttonParams);
                
                // 设置点击事件
                imageButton.setOnClickListener(v -> {
                    Log.d(TAG, "第三方登录按钮被点击，索引: " + index);
                    if (mClickListener != null) {
                        mClickListener.onThirdPartyClick(index, String.valueOf(index));
                    }
                });
                
                itemLayout.addView(imageButton);
                
                // 添加文字标签（如果有）
                if (viewItemName != null && i < viewItemName.length()) {
                    String itemName = viewItemName.optString(i);
                    if (itemName != null && !itemName.isEmpty()) {
                        TextView textView = new TextView(mContext);
                        textView.setText(itemName);
                        textView.setTextColor(Color.parseColor(customThirdView.optString("color", "#000000")));
                        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, customThirdView.optInt("size", 14));
                        textView.setGravity(Gravity.CENTER);
                        itemLayout.addView(textView);
                    }
                }
                
                // 添加按钮间距（除了最后一个按钮）
                if (i > 0) {
                    Space space = new Space(mContext);
                    space.setLayoutParams(new ViewGroup.LayoutParams(
                            dp2px(customThirdView.optInt("space", 40)),
                            LinearLayout.LayoutParams.MATCH_PARENT
                    ));
                    linearLayout.addView(space);
                }
                
                linearLayout.addView(itemLayout);
            }
            
            Log.d(TAG, "第三方登录视图创建完成");
            return linearLayout;
            
        } catch (Exception e) {
            Log.e(TAG, "创建第三方登录视图失败", e);
            return null;
        }
    }
    
    /**
     * 加载Flutter assets资源
     */
    private BitmapDrawable loadFlutterAsset(String assetPath) {
        try {
            // 移除assets/前缀（如果有）
            String path = assetPath;
            if (path.startsWith("assets/")) {
                path = path.substring(7);
            }
            
            // Flutter assets路径格式
            String flutterAssetPath = "flutter_assets/" + path;
            
            Log.d(TAG, "尝试加载Flutter asset: " + flutterAssetPath);
            
            InputStream inputStream = mContext.getAssets().open(flutterAssetPath);
            BitmapDrawable drawable = new BitmapDrawable(mContext.getResources(), inputStream);
            inputStream.close();
            
            return drawable;
        } catch (IOException e) {
            Log.e(TAG, "加载Flutter asset失败: " + assetPath, e);
            return null;
        }
    }
    
    /**
     * dp转px
     */
    private int dp2px(int dp) {
        float density = mContext.getResources().getDisplayMetrics().density;
        return (int) (dp * density + 0.5f);
    }
}
