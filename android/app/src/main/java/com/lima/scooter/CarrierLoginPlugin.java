package com.lima.scooter;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * 阿里云号码认证服务 (DYPNS) 插件
 * 支持中国移动、中国联通、中国电信的一键登录功能
 * 官方文档：https://www.aliyun.com/product/dypns
 */
public class CarrierLoginPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "AlicloudDYPNS";
    private static final String CHANNEL = "alicloud_auth";

    private MethodChannel channel;
    private Context context;
    private Activity activity;

    // 阿里云DYPNS配置
    private String accessKeyId;
    private String accessKeySecret;
    private String scheme;

    // 运营商类型
    private static final String CHINA_MOBILE = "中国移动";
    private static final String CHINA_UNICOM = "中国联通";
    private static final String CHINA_TELECOM = "中国电信";

    // 阿里云DYPNS SDK实例
    // 注意：需要添加阿里云DYPNS SDK依赖
    // private PNSAuthHelper authHelper;
    
    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "initialize":
                initialize(call, result);
                break;
            case "checkEnvironment":
                checkAvailability(result);
                break;
            case "accelerateLoginPage":
                preLogin(result);
                break;
            case "getLoginToken":
                oneClickLogin(call, result);
                break;
            case "hideLoginLoading":
                closeAuthPage(result);
                break;
            case "quitLoginPage":
                closeAuthPage(result);
                break;
            case "getCurrentCarrierName":
                getCurrentOperator(result);
                break;
            case "verifyToken":
                // TODO: 实现token验证
                result.notImplemented();
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * 初始化阿里云DYPNS SDK
     */
    private void initialize(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始初始化阿里云DYPNS SDK");

            // 获取配置参数
            accessKeyId = call.argument("accessKeyId");
            accessKeySecret = call.argument("accessKeySecret");
            scheme = call.argument("scheme");
            String environment = call.argument("environment");

            // 初始化阿里云DYPNS SDK
            // 注意：需要添加阿里云DYPNS SDK依赖到build.gradle
            // implementation 'com.aliyun.dpa:oss-android-sdk:+'

            // authHelper = PNSAuthHelper.getInstance(context, accessKeyId, accessKeySecret);
            // authHelper.setAuthSDKInfo(scheme);
            // if ("sandbox".equals(environment)) {
            //     authHelper.setEnvironment(PNSAuthHelper.ENVIRONMENT_SANDBOX);
            // } else {
            //     authHelper.setEnvironment(PNSAuthHelper.ENVIRONMENT_PRODUCTION);
            // }

            Log.d(TAG, "阿里云DYPNS SDK初始化成功");
            result.success(true);
        } catch (Exception e) {
            Log.e(TAG, "阿里云DYPNS SDK初始化失败", e);
            result.success(false);
        }
    }

    /**
     * 检查运营商登录是否可用
     */
    private void checkAvailability(Result result) {
        try {
            Map<String, Object> availability = new HashMap<>();
            
            // 检查网络连接类型
            boolean isWifiConnected = isWifiConnected();
            String operatorType = getCurrentOperatorType();
            
            // 运营商登录需要移动网络
            boolean isAvailable = !isWifiConnected && !operatorType.equals("未知运营商");
            
            availability.put("isAvailable", isAvailable);
            availability.put("isWifiConnected", isWifiConnected);
            availability.put("operatorType", operatorType);
            
            if (!isAvailable) {
                if (isWifiConnected) {
                    availability.put("errorCode", "WIFI_CONNECTED");
                    availability.put("errorMessage", "请关闭WiFi，使用移动网络");
                } else if (operatorType.equals("未知运营商")) {
                    availability.put("errorCode", "OPERATOR_NOT_SUPPORTED");
                    availability.put("errorMessage", "当前运营商不支持一键登录");
                }
            }
            
            Log.d(TAG, "运营商登录可用性检查完成: " + isAvailable);
            result.success(availability);
        } catch (Exception e) {
            Log.e(TAG, "检查运营商登录可用性失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("isAvailable", false);
            error.put("errorCode", "CHECK_ERROR");
            error.put("errorMessage", "检查失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 预取号
     */
    private void preLogin(Result result) {
        try {
            Log.d(TAG, "开始预取号");
            
            // 这里应该调用真实的运营商SDK预取号方法
            // 例如：SecVerify.preLogin(callback);
            
            // 模拟预取号成功
            Map<String, Object> preLoginResult = new HashMap<>();
            preLoginResult.put("success", true);
            preLoginResult.put("preLoginToken", "prelogin_token_" + System.currentTimeMillis());
            
            Log.d(TAG, "预取号成功");
            result.success(preLoginResult);
        } catch (Exception e) {
            Log.e(TAG, "预取号失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("errorCode", "PRELOGIN_ERROR");
            error.put("errorMessage", "预取号失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 一键登录
     */
    private void oneClickLogin(MethodCall call, Result result) {
        try {
            Log.d(TAG, "开始一键登录");
            
            String customTitle = call.argument("customTitle");
            String customLogo = call.argument("customLogo");
            Integer timeout = call.argument("timeout");
            
            // 这里应该调用真实的运营商SDK一键登录方法
            // 例如：SecVerify.oneClickLogin(activity, config, callback);
            
            // 模拟一键登录成功
            Map<String, Object> loginResult = new HashMap<>();
            loginResult.put("success", true);
            loginResult.put("token", "carrier_token_" + System.currentTimeMillis());
            loginResult.put("accessCode", "encrypted_phone_number");
            loginResult.put("operatorType", getCurrentOperatorType());
            loginResult.put("phoneNumber", "138****8888"); // 脱敏手机号
            
            Log.d(TAG, "一键登录成功");
            result.success(loginResult);
        } catch (Exception e) {
            Log.e(TAG, "一键登录失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("errorCode", "LOGIN_ERROR");
            error.put("errorMessage", "一键登录失败: " + e.getMessage());
            result.success(error);
        }
    }

    /**
     * 关闭授权页面
     */
    private void closeAuthPage(Result result) {
        try {
            Log.d(TAG, "关闭授权页面");
            
            // 这里应该调用真实的运营商SDK关闭授权页面方法
            // 例如：SecVerify.closeAuthPage();
            
            result.success(null);
        } catch (Exception e) {
            Log.e(TAG, "关闭授权页面失败", e);
            result.error("CLOSE_ERROR", "关闭授权页面失败", e.getMessage());
        }
    }

    /**
     * 获取当前运营商类型
     */
    private void getCurrentOperator(Result result) {
        try {
            String operatorType = getCurrentOperatorType();
            Log.d(TAG, "当前运营商: " + operatorType);
            result.success(operatorType);
        } catch (Exception e) {
            Log.e(TAG, "获取运营商类型失败", e);
            result.success("未知运营商");
        }
    }

    /**
     * 检查是否连接WiFi
     */
    private boolean isWifiConnected() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            return networkInfo != null && networkInfo.getType() == ConnectivityManager.TYPE_WIFI;
        } catch (Exception e) {
            Log.e(TAG, "检查WiFi连接状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前运营商类型
     */
    private String getCurrentOperatorType() {
        try {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String operator = tm.getSimOperator();
            
            if (operator != null) {
                if (operator.startsWith("460") && (operator.endsWith("00") || operator.endsWith("02") || operator.endsWith("07"))) {
                    return CHINA_MOBILE;
                } else if (operator.startsWith("460") && (operator.endsWith("01") || operator.endsWith("06") || operator.endsWith("09"))) {
                    return CHINA_UNICOM;
                } else if (operator.startsWith("460") && (operator.endsWith("03") || operator.endsWith("05") || operator.endsWith("11"))) {
                    return CHINA_TELECOM;
                }
            }
            
            return "未知运营商";
        } catch (Exception e) {
            Log.e(TAG, "获取运营商类型失败", e);
            return "未知运营商";
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }
}
