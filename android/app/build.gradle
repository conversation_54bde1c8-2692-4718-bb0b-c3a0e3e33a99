plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

// MobSDK 插件已移除

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// MobSDK.gradle 配置已移除

android {
    namespace "com.lima.scooter"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    signingConfigs{
//        release{
//            keyAlias 'senthink'
//            keyPassword '123456'
//            storeFile file('limaiot.jks')
//            storePassword '123456'
//        }
//        debug{
//            keyAlias 'senthink'
//            keyPassword '123456'
//            storeFile file('limaiot.jks')
//            storePassword '123456'
//        }
        release {
            keyAlias 'lima'
            keyPassword '091224'
            storeFile file("lima.jks")
            storePassword '091224'
        }
        debug {
            keyAlias 'lima'
            keyPassword '091224'
            storeFile file("lima.jks")
            storePassword '091224'
        }
    }

      compileOptions {
    // Flag to enable support for the new language APIs
    coreLibraryDesugaringEnabled true
    // Sets Java compatibility to Java 8
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  kotlinOptions {
    jvmTarget = '1.8'
  }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.lima.scooter"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 21
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        manifestPlaceholders = [
            AMAP_KEY: "af4171184527d758d6b6094f10aa6ae4",
            APP_NAME:"立马科技",
            GETUI_APP_ID    : "CwBoQsd8u873FT6wmfRrj2",
            GETUI_APPID    : "CwBoQsd8u873FT6wmfRrj2",
            GETUI_APP_KEY   : "Cve3Tz8k4G9hJT5a2eQmDA",
            GETUI_APP_SECRET: "F1dBADgSYz8HPdMVRNfMR9"
        ]
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled false

            // Enables resource shrinking, which is performed by the
            // Android Gradle plugin.
            shrinkResources false
        }
        debug {
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
        checkReleaseBuilds false
    }
}

dependencies{
    api 'com.amap.api:location:latest.integration'
    implementation 'com.getui:gtsdk:3.2.18.0'  //个推SDK
    implementation 'com.getui:gtc:3.2.6.0'  //个推核心组件

    // 阿里云号码认证服务 (DYPNS) SDK
    // 官方文档：https://help.aliyun.com/document_detail/144135.html
    // 注释掉不存在的依赖，使用ali_auth插件提供的依赖
    // implementation 'com.aliyun.dpa:oss-android-sdk:2.9.13'
    // implementation 'com.aliyun:alicloud-android-number-auth:1.0.0'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}

flutter {
    source '../..'
}