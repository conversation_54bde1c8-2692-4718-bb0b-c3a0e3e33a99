buildscript {
    ext.kotlin_version = '1.9.24'
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/public' }//central仓和jcenter仓的聚合仓
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        google()
        mavenCentral()
        // MobSDK maven仓库已移除
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // MobSDK classpath已移除
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/public' }//central仓和jcenter仓的聚合仓
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven {
            url "https://mvn.getui.com/nexus/content/repositories/releases/"
        }
        // 默认仓库作为备选
        google()
        mavenCentral()
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "1.8"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
     afterEvaluate {
         if (it.hasProperty('android')) {
             if (it.android.namespace == null) {
                 def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                 def packageName = <EMAIL>()
                 println("Setting ${packageName} as android namespace")
                 android.namespace = packageName
             }
             println("========================================")
         }
        android {
            compileSdkVersion 34
        }
     }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
