buildscript {
    ext.kotlin_version = '1.8.22'
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/public' }//central仓和jcenter仓的聚合仓
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        google()
        mavenCentral()
        maven {
            url "https://mvn.mob.com/android"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.mob.sdk:MobSDK:+'
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/public' }//central仓和jcenter仓的聚合仓
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        // 默认仓库作为备选
        google()
        mavenCentral()
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "1.8"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
     afterEvaluate {
         if (it.hasProperty('android')) {
             if (it.android.namespace == null) {
                 def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                 def packageName = <EMAIL>()
                 println("Setting ${packageName} as android namespace")
                 android.namespace = packageName
             }
             println("========================================")
         }
        android {
            compileSdkVersion 34
        }
     }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
