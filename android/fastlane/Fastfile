# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  lane :deployPgyer do
    gradle(task: "clean assembleRelease")
    notification( title: "LIMA", subtitle: "Android打包成功", message: "准备发布到蒲公英……")
#in the list of available options: api_key, apk, ipa, password, update_description, save_uploaded_info_json, install_type, install_date, install_start_date, install_end_date, oversea, channel
    pgyer(api_key:"",update_description:"测试环境", apk:"/Users/<USER>/Documents/projects/flutter_project/limaflutter/build/app/outputs/apk/release/app-release.apk")
  end

end
