```mermaid
graph TD
    A[用户登录] --> B{登录成功};
    B --> |是| C[获取 lima_token];
    C --> D[设置全局变量 lima_token];
    D --> E[提供 get_lima_token() 方法];
    E --> F[home_logic 调用 get_lima_token()];
    E --> G[service_logic 调用 get_lima_token()];
    F --> H[更新 home_logic 中的 token];
    G --> I[更新 service_logic 中的 token];
    H --> J[WebView (车辆) 注入 token];
    I --> K[WebView (服务) 注入 token];
    B --> |否| L[登录失败处理];
```
