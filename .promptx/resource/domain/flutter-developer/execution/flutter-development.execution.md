<execution>
  <constraint>
    ## Flutter开发客观限制
    - **平台约束**：必须遵循iOS和Android平台的设计规范和技术限制
    - **性能边界**：Flutter应用性能受Dart VM和Skia渲染引擎限制
    - **包大小限制**：应用商店对APK/IPA文件大小有限制要求
    - **API兼容性**：必须考虑不同Android版本和iOS版本的API差异
    - **硬件限制**：需要考虑不同设备的内存、CPU、屏幕等硬件差异
    - **网络环境**：需要处理弱网络、无网络等各种网络状况
  </constraint>

  <rule>
    ## Flutter开发强制规则
    - **代码规范**：严格遵循Dart语言规范和Flutter官方代码风格
    - **状态管理**：必须使用明确的状态管理方案，避免setState滥用
    - **错误处理**：所有异步操作必须包含错误处理逻辑
    - **资源管理**：及时释放不再使用的资源，防止内存泄漏
    - **平台适配**：UI必须在iOS和Android平台上都能正常显示和交互
    - **测试覆盖**：核心业务逻辑必须编写单元测试
    - **性能优化**：避免在build方法中进行耗时操作
  </rule>

  <guideline>
    ## Flutter开发指导原则
    - **组件化开发**：优先创建可复用的自定义Widget
    - **响应式设计**：使用MediaQuery和LayoutBuilder适配不同屏幕
    - **渐进式开发**：从MVP开始，逐步添加功能
    - **用户体验优先**：优先考虑用户交互的流畅性和直观性
    - **代码可读性**：编写自解释的代码，添加必要的注释
    - **持续集成**：建立自动化构建和测试流程
    - **版本管理**：合理使用Git分支策略管理代码版本
  </guideline>

  <process>
    ## Flutter开发执行流程

    ### Phase 1: 项目初始化 (1-2天)
    1. **环境准备**
       ```bash
       # 检查Flutter环境
       flutter doctor
       
       # 创建新项目
       flutter create --org com.company project_name
       
       # 配置IDE
       code project_name  # VS Code
       # 或 studio project_name  # Android Studio
       ```

    2. **项目结构规划**
       ```
       lib/
       ├── main.dart
       ├── app/
       │   ├── app.dart
       │   └── routes/
       ├── core/
       │   ├── constants/
       │   ├── utils/
       │   └── services/
       ├── features/
       │   └── [feature_name]/
       │       ├── data/
       │       ├── domain/
       │       └── presentation/
       └── shared/
           ├── widgets/
           └── themes/
       ```

    3. **依赖管理**
       ```yaml
       dependencies:
         flutter:
           sdk: flutter
         # 状态管理
         provider: ^6.0.0
         # 网络请求
         dio: ^5.0.0
         # 本地存储
         shared_preferences: ^2.0.0
       
       dev_dependencies:
         flutter_test:
           sdk: flutter
         flutter_lints: ^3.0.0
       ```

    ### Phase 2: 核心架构搭建 (2-3天)
    1. **状态管理配置**
       ```dart
       // Provider示例
       class AppState extends ChangeNotifier {
         bool _isLoading = false;
         bool get isLoading => _isLoading;
         
         void setLoading(bool loading) {
           _isLoading = loading;
           notifyListeners();
         }
       }
       ```

    2. **路由管理设置**
       ```dart
       // 路由配置
       class AppRoutes {
         static const String home = '/';
         static const String login = '/login';
         static const String profile = '/profile';
         
         static Route<dynamic> generateRoute(RouteSettings settings) {
           switch (settings.name) {
             case home:
               return MaterialPageRoute(builder: (_) => HomePage());
             case login:
               return MaterialPageRoute(builder: (_) => LoginPage());
             default:
               return MaterialPageRoute(builder: (_) => NotFoundPage());
           }
         }
       }
       ```

    3. **网络层搭建**
       ```dart
       // API服务基类
       class ApiService {
         final Dio _dio = Dio();
         
         ApiService() {
           _dio.options.baseUrl = 'https://api.example.com';
           _dio.interceptors.add(LogInterceptor());
         }
         
         Future<Response> get(String path) async {
           try {
             return await _dio.get(path);
           } catch (e) {
             throw ApiException(e.toString());
           }
         }
       }
       ```

    ### Phase 3: UI开发 (5-10天)
    1. **主题配置**
       ```dart
       class AppTheme {
         static ThemeData lightTheme = ThemeData(
           primarySwatch: Colors.blue,
           visualDensity: VisualDensity.adaptivePlatformDensity,
           appBarTheme: AppBarTheme(
             elevation: 0,
             backgroundColor: Colors.white,
             foregroundColor: Colors.black,
           ),
         );
       }
       ```

    2. **基础组件开发**
       ```dart
       class CustomButton extends StatelessWidget {
         final String text;
         final VoidCallback? onPressed;
         final bool isLoading;
         
         const CustomButton({
           Key? key,
           required this.text,
           this.onPressed,
           this.isLoading = false,
         }) : super(key: key);
         
         @override
         Widget build(BuildContext context) {
           return ElevatedButton(
             onPressed: isLoading ? null : onPressed,
             child: isLoading 
               ? CircularProgressIndicator()
               : Text(text),
           );
         }
       }
       ```

    3. **页面开发**
       - 使用StatefulWidget或StatelessWidget
       - 实现响应式布局
       - 添加用户交互逻辑
       - 集成状态管理

    ### Phase 4: 功能集成 (3-5天)
    1. **API集成**
       ```dart
       class UserRepository {
         final ApiService _apiService;
         
         UserRepository(this._apiService);
         
         Future<User> getUser(String id) async {
           final response = await _apiService.get('/users/$id');
           return User.fromJson(response.data);
         }
       }
       ```

    2. **本地存储**
       ```dart
       class StorageService {
         static const String _keyToken = 'auth_token';
         
         Future<void> saveToken(String token) async {
           final prefs = await SharedPreferences.getInstance();
           await prefs.setString(_keyToken, token);
         }
         
         Future<String?> getToken() async {
           final prefs = await SharedPreferences.getInstance();
           return prefs.getString(_keyToken);
         }
       }
       ```

    ### Phase 5: 测试与优化 (2-3天)
    1. **单元测试**
       ```dart
       void main() {
         group('UserRepository Tests', () {
           test('should return user when API call is successful', () async {
             // Arrange
             final mockApiService = MockApiService();
             final repository = UserRepository(mockApiService);
             
             // Act
             final user = await repository.getUser('123');
             
             // Assert
             expect(user.id, '123');
           });
         });
       }
       ```

    2. **性能优化**
       - 使用const构造函数
       - 避免不必要的rebuild
       - 优化图片加载
       - 使用ListView.builder处理长列表

    3. **构建发布**
       ```bash
       # Android发布包
       flutter build apk --release
       
       # iOS发布包
       flutter build ios --release
       ```
  </process>

  <criteria>
    ## Flutter开发质量标准

    ### 代码质量
    - ✅ 通过flutter analyze检查，无警告和错误
    - ✅ 遵循Dart语言规范和Flutter最佳实践
    - ✅ 代码结构清晰，职责分离明确
    - ✅ 适当的注释和文档说明

    ### 功能完整性
    - ✅ 所有需求功能正确实现
    - ✅ 用户交互流程顺畅
    - ✅ 错误处理完善
    - ✅ 边界情况处理得当

    ### 性能表现
    - ✅ 应用启动时间<3秒
    - ✅ 页面切换流畅，无明显卡顿
    - ✅ 内存使用合理，无内存泄漏
    - ✅ 网络请求响应及时

    ### 平台兼容性
    - ✅ iOS和Android平台功能一致
    - ✅ 不同屏幕尺寸适配良好
    - ✅ 支持主流系统版本
    - ✅ 平台特定功能正确实现

    ### 测试覆盖
    - ✅ 核心业务逻辑单元测试覆盖率>80%
    - ✅ 关键用户流程集成测试
    - ✅ UI组件测试
    - ✅ 性能测试通过

    ### 用户体验
    - ✅ 界面美观，符合平台设计规范
    - ✅ 交互逻辑直观易懂
    - ✅ 加载状态和错误提示友好
    - ✅ 无障碍功能支持
  </criteria>
</execution>
