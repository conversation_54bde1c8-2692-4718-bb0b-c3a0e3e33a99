# Flutter框架核心知识体系

## 🎯 Flutter基础架构

### 核心概念
- **Widget Tree**: Flutter中一切皆Widget的设计理念
- **Element Tree**: Widget的实例化表示，管理Widget生命周期
- **Render Tree**: 负责布局、绘制和合成的渲染对象
- **Layer Tree**: 最终的绘制指令，传递给Skia引擎

### 渲染引擎
```
Flutter App
    ↓
Dart Framework (Material/Cupertino)
    ↓
Flutter Engine (Skia + Dart VM)
    ↓
Platform (iOS/Android)
```

## 📱 Widget系统

### Widget分类
- **StatelessWidget**: 不可变的Widget，适用于静态UI
- **StatefulWidget**: 可变状态的Widget，适用于动态UI
- **InheritedWidget**: 数据向下传递的Widget
- **RenderObjectWidget**: 直接参与渲染的Widget

### 常用布局Widget
```dart
// 线性布局
Column(children: [...])  // 垂直排列
Row(children: [...])     // 水平排列

// 弹性布局
Flex(direction: Axis.vertical, children: [...])
Expanded(child: widget)  // 占用剩余空间
Flexible(child: widget)  // 灵活占用空间

// 堆叠布局
Stack(children: [...])   // 层叠布局
Positioned(child: widget) // 绝对定位

// 容器布局
Container(child: widget) // 通用容器
Padding(child: widget)   // 内边距
Margin(child: widget)    // 外边距
```

### 生命周期
```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  @override
  void initState() {
    super.initState();
    // 初始化操作
  }
  
  @override
  Widget build(BuildContext context) {
    // 构建UI
    return Container();
  }
  
  @override
  void dispose() {
    // 清理资源
    super.dispose();
  }
}
```

## 🎨 Material Design & Cupertino

### Material组件
```dart
// 应用结构
MaterialApp()
Scaffold()
AppBar()
Drawer()
BottomNavigationBar()

// 按钮组件
ElevatedButton()
TextButton()
OutlinedButton()
FloatingActionButton()

// 输入组件
TextField()
TextFormField()
Checkbox()
Radio()
Switch()

// 展示组件
Card()
ListTile()
DataTable()
```

### Cupertino组件
```dart
// iOS风格组件
CupertinoApp()
CupertinoPageScaffold()
CupertinoNavigationBar()
CupertinoTabScaffold()

CupertinoButton()
CupertinoTextField()
CupertinoSwitch()
CupertinoSlider()
```

## 🔄 状态管理

### 内置状态管理
```dart
// setState
setState(() {
  _counter++;
});

// ValueNotifier
ValueNotifier<int> counter = ValueNotifier(0);
ValueListenableBuilder<int>(
  valueListenable: counter,
  builder: (context, value, child) {
    return Text('$value');
  },
)
```

### Provider模式
```dart
// 1. 创建数据模型
class CounterModel extends ChangeNotifier {
  int _count = 0;
  int get count => _count;
  
  void increment() {
    _count++;
    notifyListeners();
  }
}

// 2. 提供数据
ChangeNotifierProvider(
  create: (context) => CounterModel(),
  child: MyApp(),
)

// 3. 消费数据
Consumer<CounterModel>(
  builder: (context, counter, child) {
    return Text('${counter.count}');
  },
)
```

### BLoC模式
```dart
// Event
abstract class CounterEvent {}
class Increment extends CounterEvent {}

// State
class CounterState {
  final int count;
  CounterState(this.count);
}

// BLoC
class CounterBloc extends Bloc<CounterEvent, CounterState> {
  CounterBloc() : super(CounterState(0)) {
    on<Increment>((event, emit) {
      emit(CounterState(state.count + 1));
    });
  }
}
```

## 🌐 网络与数据

### HTTP请求
```dart
// 使用dio
final dio = Dio();

// GET请求
Response response = await dio.get('/users');

// POST请求
Response response = await dio.post('/users', data: {
  'name': 'John',
  'email': '<EMAIL>'
});

// 拦截器
dio.interceptors.add(InterceptorsWrapper(
  onRequest: (options, handler) {
    options.headers['Authorization'] = 'Bearer $token';
    handler.next(options);
  },
));
```

### 本地存储
```dart
// SharedPreferences
final prefs = await SharedPreferences.getInstance();
await prefs.setString('key', 'value');
String? value = prefs.getString('key');

// SQLite (sqflite)
final database = await openDatabase(
  'app_database.db',
  version: 1,
  onCreate: (db, version) {
    return db.execute(
      'CREATE TABLE users(id INTEGER PRIMARY KEY, name TEXT)',
    );
  },
);
```

## 🎯 路由导航

### 基础路由
```dart
// 命名路由
Navigator.pushNamed(context, '/second');
Navigator.pop(context);

// 直接路由
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => SecondPage()),
);
```

### 高级路由 (Go Router)
```dart
final router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => HomePage(),
    ),
    GoRoute(
      path: '/details/:id',
      builder: (context, state) {
        final id = state.params['id']!;
        return DetailsPage(id: id);
      },
    ),
  ],
);
```

## 🎨 动画系统

### 基础动画
```dart
class AnimatedWidget extends StatefulWidget {
  @override
  _AnimatedWidgetState createState() => _AnimatedWidgetState();
}

class _AnimatedWidgetState extends State<AnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Container(width: 100, height: 100, color: Colors.blue),
        );
      },
    );
  }
}
```

### 隐式动画
```dart
AnimatedContainer(
  duration: Duration(seconds: 1),
  width: _isExpanded ? 200 : 100,
  height: _isExpanded ? 200 : 100,
  color: _isExpanded ? Colors.blue : Colors.red,
  child: child,
)
```

## 🔧 性能优化

### 构建优化
```dart
// 使用const构造函数
const Text('Hello World')

// 避免在build中创建对象
class MyWidget extends StatelessWidget {
  static const textStyle = TextStyle(fontSize: 16); // 静态常量
  
  @override
  Widget build(BuildContext context) {
    return Text('Hello', style: textStyle);
  }
}

// 使用Builder减少重建范围
Builder(
  builder: (context) {
    return ExpensiveWidget();
  },
)
```

### 列表优化
```dart
// 使用ListView.builder处理大列表
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(title: Text(items[index]));
  },
)

// 使用AutomaticKeepAliveClientMixin保持状态
class MyListItem extends StatefulWidget {
  @override
  _MyListItemState createState() => _MyListItemState();
}

class _MyListItemState extends State<MyListItem>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用
    return Container();
  }
}
```

## 🧪 测试策略

### 单元测试
```dart
void main() {
  test('Counter increments', () {
    final counter = Counter();
    counter.increment();
    expect(counter.value, 1);
  });
}
```

### Widget测试
```dart
void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    await tester.pumpWidget(MyApp());
    
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);
    
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();
    
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
```

### 集成测试
```dart
void main() {
  group('App Test', () {
    testWidgets('full app test', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 测试完整用户流程
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      expect(find.text('Welcome'), findsOneWidget);
    });
  });
}
```
