import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/pages/demo/demo_view.dart';
import 'package:lima/pages/login/login_view.dart';
import 'package:lima/routers/app_router.dart';

/// 测试登录功能的简单应用
class TestLoginApp extends StatelessWidget {
  const TestLoginApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '登录功能测试',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: TestHomePage(),
      getPages: [
        GetPage(
          name: AppRoute.demo,
          page: () => DemoPage(),
        ),
        GetPage(
          name: AppRoute.login,
          page: () => LoginPage(),
        ),
      ],
    );
  }
}

class TestHomePage extends StatelessWidget {
  const TestHomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('登录功能测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '立马科技App登录功能测试',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 40),
            
            ElevatedButton(
              onPressed: () {
                Get.toNamed(AppRoute.demo);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text('打开登录演示页面'),
            ),
            
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                Get.toNamed(AppRoute.login);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text('直接打开登录页面'),
            ),
            
            SizedBox(height: 40),
            
            Text(
              '功能说明：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                '• 演示页面：展示登录状态和功能说明\n'
                '• 登录页面：完整的登录功能实现\n'
                '• 支持手机号+验证码登录\n'
                '• 支持微信登录和苹果登录\n'
                '• 自动保存登录状态',
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(TestLoginApp());
}
