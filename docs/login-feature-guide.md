# 立马科技App Flutter原生登录功能

## 概述

基于现有H5登录功能，新开发了Flutter原生登录页面，支持多种登录方式，提供更好的用户体验。

## 功能特性

### 🔐 登录方式
- **手机号+验证码登录** - 主要登录方式
- **微信登录** - 第三方快速登录
- **苹果登录** - iOS平台专用，符合App Store要求

### 🎨 UI特性
- **现代化设计** - 简洁美观的Material Design风格
- **响应式布局** - 适配不同屏幕尺寸
- **流畅动画** - 页面切换和状态变化动画
- **深色模式支持** - 自适应系统主题

### 🛡️ 安全特性
- **验证码倒计时** - 防止频繁发送验证码
- **表单验证** - 实时验证手机号和验证码格式
- **错误处理** - 完善的错误提示和处理机制
- **Token管理** - 自动保存和管理登录状态

## 文件结构

```
lib/pages/login/
├── login_view.dart          # 登录页面UI
├── login_logic.dart         # 登录业务逻辑
└── README.md               # 使用说明

lib/pages/demo/
└── demo_view.dart          # 登录功能演示页面

lib/utils/http/
└── api.dart                # 新增登录相关API接口

lib/routers/
└── app_router.dart         # 新增登录路由配置

lib/res/
├── colors.dart             # 新增登录页面颜色定义
└── icons.dart              # 新增登录页面图标定义
```

## 使用方法

### 1. 基本使用

```dart
// 方式1：通过路由跳转
final result = await Get.toNamed(AppRoute.login);
if (result == true) {
  // 登录成功，刷新用户状态
  print('登录成功');
}

// 方式2：直接跳转页面
final result = await Get.to(() => LoginPage());
if (result == true) {
  // 登录成功处理
}
```

### 2. 检查登录状态

```dart
// 检查是否已登录
final token = await Global.getToken();
final userInfo = await Global.getUserPhone();

if (token != null) {
  // 已登录状态
} else {
  // 未登录，跳转登录页面
  Get.toNamed(AppRoute.login);
}
```

### 3. 退出登录

```dart
// 清除登录信息
await Global.removeToken();
await Global.removeUserInfo();
```

## API接口

### 发送验证码
```dart
final result = await API.sendSmsCodeApi(
  phone: '13800138000',
  type: 'login'
);
```

### 手机号登录
```dart
final result = await API.phoneLogin(
  phone: '13800138000',
  code: '123456'
);
```

### 微信登录
```dart
final result = await API.wechatLoginApi(
  code: 'wechat_code',
  openid: 'wechat_openid',
  unionid: 'wechat_unionid',
  nickname: '微信昵称',
  headimgurl: '头像URL'
);
```

### 苹果登录
```dart
final result = await API.appleLoginApi(
  appleOpenid: 'apple_user_id',
  nickname: '用户昵称',
  email: '<EMAIL>'
);
```

## 配置说明

### 1. 微信登录配置

在 `lib/common/bridge_controller.dart` 中配置微信参数：

```dart
String kWechatAppID = 'wxd92e6f7ab4454e9d';
String kWechatAppSecret = '87681e9868ee3d3ca66b14ebfed6f122';
String kWechatUniversalLink = 'your wechat universal link';
```

### 2. 苹果登录配置

苹果登录仅在iOS平台可用，需要在Apple Developer后台配置Sign in with Apple服务。

### 3. API地址配置

在 `lib/common/config.dart` 中配置API基础地址：

```dart
static String BASE_URL = ENV == EnvType.TEST? base_url_test : base_url_prod;
```

## 演示页面

项目包含一个完整的演示页面 `DemoPage`，展示了登录功能的使用方法：

```dart
// 跳转到演示页面
Get.toNamed(AppRoute.demo);
```

演示页面功能：
- 显示当前登录状态
- 展示Token和用户信息
- 提供登录/退出登录按钮
- 功能说明和使用指南

## 自定义配置

### 1. 修改UI样式

在 `lib/res/colors.dart` 中修改颜色配置：

```dart
static const Color primary = Color(0xFF2B6BFF);  // 主色调
static const Color error = Color(0xFFE53E3E);    // 错误色
static const Color success = Color(0xFF38A169);  // 成功色
```

### 2. 修改验证码倒计时

在 `login_logic.dart` 中修改倒计时时间：

```dart
void startCountdown() {
  countdown.value = 60; // 修改倒计时秒数
  // ...
}
```

### 3. 添加自定义验证

在 `validateForm()` 方法中添加自定义验证逻辑：

```dart
void validateForm() {
  final phone = phoneController.text.trim();
  final code = codeController.text.trim();
  
  // 自定义验证逻辑
  final phoneValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
  final codeValid = code.length == 6;
  
  // 更新按钮状态
  canLogin.value = phoneValid && codeValid && !isLoading.value;
}
```

## 注意事项

1. **网络请求** - 确保后端API接口已正确实现
2. **权限配置** - iOS需要配置相机权限用于二维码扫描
3. **第三方登录** - 微信和苹果登录需要相应的开发者账号配置
4. **错误处理** - 建议根据实际业务需求完善错误处理逻辑
5. **数据安全** - 敏感信息应加密存储

## 技术栈

- **Flutter** - 跨平台UI框架
- **GetX** - 状态管理和路由管理
- **flutter_screenutil** - 屏幕适配
- **shared_preferences** - 本地数据存储
- **dio** - 网络请求
- **sign_in_with_apple** - 苹果登录
- **wechat_kit** - 微信登录

## 更新日志

### v1.0.0 (2024-06-12)
- ✨ 新增Flutter原生登录页面
- ✨ 支持手机号+验证码登录
- ✨ 支持微信登录
- ✨ 支持苹果登录（iOS）
- ✨ 新增登录状态管理
- ✨ 新增演示页面
- 📝 完善文档和使用说明
