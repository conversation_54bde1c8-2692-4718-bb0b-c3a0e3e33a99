# Lima Flutter 项目文档

## 项目概述

**Lima** 是一个基于 Flutter 开发的跨平台移动应用，集成了多种功能模块，包括用户认证、蓝牙设备管理、地图定位、消息推送等。项目采用现代化的架构设计，支持 iOS 和 Android 双平台。

### 基本信息
- **项目名称**: Lima Flutter Project
- **当前版本**: 2.0.13+28
- **Flutter版本**: 3.27.0+
- **Dart SDK**: >=3.0.0 <4.0.0
- **开发环境**: JDK17, Android SDK 35.0.0

## 核心功能

### 🔐 用户认证系统
- 手机号+验证码登录
- 微信第三方登录
- 苹果登录（iOS）
- 阿里云DYPNS运营商一键登录
- Token自动管理

### 📱 蓝牙设备管理
- BLE设备扫描和连接
- 数据传输和通信
- OTA固件升级
- 多设备连接管理

### 🗺️ 地图和定位
- 高德地图集成
- 实时定位服务
- 地理位置权限管理

### 📢 消息推送
- 个推SDK集成
- 本地通知
- 推送消息管理

### 💳 支付集成
- 支付宝支付
- 微信支付
- 多种支付方式支持

### 📷 媒体功能
- 图片选择和拍照
- 二维码扫描
- 视频播放
- 图片保存到相册

## 技术架构

### 状态管理
- **GetX**: 用于状态管理、路由管理和依赖注入
- **RxDart**: 响应式编程支持

### 网络请求
- **Dio**: HTTP客户端
- **Cookie管理**: 自动Cookie处理
- **请求拦截**: 统一请求和响应处理

### 本地存储
- **SharedPreferences**: 轻量级键值存储
- **Path Provider**: 文件路径管理

### UI框架
- **Flutter ScreenUtil**: 屏幕适配
- **Material Design**: UI设计规范
- **自定义组件**: 可复用的UI组件库

## 项目结构

```
lima/
├── lib/
│   ├── common/          # 公共模块
│   ├── pages/           # 页面模块
│   ├── utils/           # 工具类
│   ├── res/             # 资源文件
│   └── routers/         # 路由配置
├── assets/              # 静态资源
├── android/             # Android平台配置
├── ios/                 # iOS平台配置
├── docs/                # 项目文档
└── packages/            # 自定义包
```

## 开发环境

### 必需工具
- Flutter 3.27.0+
- Dart SDK 3.0.0+
- Android Studio / VS Code
- Xcode (iOS开发)

### 环境配置
```bash
# 检查Flutter环境
flutter doctor

# 获取依赖
flutter pub get

# 运行项目
flutter run
```

## 部署脚本

项目提供了自动化部署脚本：
- `deploy_simple.sh`: 简单部署脚本
- `deploy_testflight.sh`: TestFlight部署脚本

## 文档索引

本目录包含了项目的所有技术文档，按功能模块分类：

### 📋 [项目升级指南](./flutter-upgrade-guide.md)
- Flutter 3.0+ 升级步骤
- 依赖包更新指南
- 兼容性问题解决

### 🔐 [登录功能说明](./login-feature-guide.md)
- 多种登录方式实现
- API接口文档
- 使用示例和配置

### 📶 [阿里云DYPNS集成](./alicloud-dypns-integration.md)
- 运营商一键登录集成
- 问题解决方案
- 配置和使用指南

### 📱 [iOS性能优化](./ios-performance-optimization.md)
- 启动性能分析
- 优化方案和实施步骤
- 性能监控建议

### 🔵 [蓝牙模块文档](./bluetooth-module-guide.md)
- BLE设备管理
- 数据传输协议
- OTA升级流程

## 开发规范

### 代码规范
- 遵循Dart官方代码规范
- 使用flutter_lints进行代码检查
- 统一的命名规范和注释标准

### Git规范
- 使用语义化提交信息
- 功能分支开发模式
- Code Review流程

### 测试规范
- 单元测试覆盖
- 集成测试验证
- 性能测试监控

## 常见问题

### 构建问题
- Gradle代理配置
- 依赖版本冲突解决
- 平台特定配置

### 运行时问题
- 权限申请失败
- 网络请求异常
- 第三方SDK集成问题

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用私有许可证，仅供内部开发使用。

## 联系方式

如有技术问题或建议，请联系开发团队。

---

**最后更新**: 2025年1月15日
**维护团队**: Lima开发团队