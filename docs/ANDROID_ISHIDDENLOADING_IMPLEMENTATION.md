# Android端 isHiddenLoading 功能实现

## 问题描述
`isHiddenLoading` 配置在Android端没有实现，需要从iOS端移植相关功能。

## iOS端实现逻辑分析

### iOS实现要点：
1. **读取配置**：`bool isHiddenLoading = [self->_callData.arguments boolValueForKey: @"isHiddenLoading" defaultValue: YES];`
2. **显示loading**：点击登录按钮时，如果 `isHiddenLoading` 为 `false` 则显示loading
3. **隐藏loading**：在 `showResult` 方法中，如果 `isHiddenLoading` 为 `false` 则隐藏loading

## Android端实现

### 1. 修改 LoginParams.java
在 `showResult` 方法中添加loading处理逻辑：

```java
public void showResult(String code, String message, Object data){
    // 处理isHiddenLoading逻辑 - 如果设置为false则需要隐藏loading
    if (jsonObject != null && !jsonObject.getBooleanValue("isHiddenLoading", true)) {
        if (mAuthHelper != null) {
            mAuthHelper.hideLoginLoading();
        }
    }
    
    if (eventSink != null) {
        JSONObject result = resultFormatData(code, message, data);
        result.put("isChecked", isChecked);
        eventSink.success(result);
    }
}
```

### 2. 修改 OneKeyLoginPublic.java
在授权页面成功显示时根据配置决定是否隐藏loading：

```java
if (ResultCode.CODE_START_AUTHPAGE_SUCCESS.equals(tokenRet.getCode())) {
    Log.i(TAG, "唤起授权页成功：" + s);
    // 根据isHiddenLoading配置决定是否隐藏loading
    if (jsonObject.getBooleanValue("isHiddenLoading", true)) {
        mAuthHelper.hideLoginLoading();
        Log.d(TAG, "isHiddenLoading=true，隐藏loading");
    } else {
        Log.d(TAG, "isHiddenLoading=false，保持loading显示");
    }
}
```

### 3. 修改 CustomAuthUIControlClickListener.java
在登录按钮点击处理中添加日志：

```java
case ResultCode.CODE_ERROR_USER_LOGIN_BTN:
    // 处理isHiddenLoading逻辑 - 如果设置为false则显示loading
    if (jsonObject != null && !jsonObject.getBooleanValue("isHiddenLoading", true)) {
        Log.d(TAG, "用户点击登录按钮，isHiddenLoading=false，将在结果回调中处理loading");
    }
    break;
```

## 实现逻辑

### 当 isHiddenLoading = true（默认值）：
1. 授权页面显示成功时立即隐藏loading
2. 用户看不到loading动画
3. 页面保持可操作状态

### 当 isHiddenLoading = false：
1. 授权页面显示成功时保持loading显示
2. 在所有结果回调中隐藏loading
3. 用户会看到loading动画直到操作完成

## 配置使用

在Flutter端的AliAuthModel中设置：

```dart
final authModel = AliAuthModel(
  // ... 其他配置
  isHiddenLoading: true, // 隐藏loading（推荐）
  // ... 其他配置
);
```

## 预期效果

- ✅ 与iOS端行为保持一致
- ✅ 支持通过配置控制loading显示
- ✅ 解决点击登录按钮后页面无法操作的问题
- ✅ 保持向后兼容性（默认隐藏loading）