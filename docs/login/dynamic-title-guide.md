# 动态页面标题功能使用指南

## 🎯 功能概述

实现了根据是否有bindKey动态显示页面标题和文案的功能：
- ✅ **有bindKey** → 显示"绑定手机号"相关文案
- ✅ **无bindKey** → 显示"手机号登录"相关文案

## 🔧 技术实现

### 1. 手机号输入页面

#### 动态标题方法 (`lib/pages/phone_input/phone_input_logic.dart`)
```dart
/// 动态获取页面标题
String getPageTitle() {
  if (bindKey != null && bindKey!.isNotEmpty) {
    return '绑定手机号'; // 有bindKey时显示绑定手机号
  } else {
    return '手机号登录'; // 没有bindKey时显示手机号登录
  }
}

/// 动态获取页面主标题
String getMainTitle() {
  if (bindKey != null && bindKey!.isNotEmpty) {
    return '绑定手机号';
  } else {
    return '手机号登录';
  }
}

/// 动态获取页面描述
String getPageDescription() {
  if (bindKey != null && bindKey!.isNotEmpty) {
    return '为了完成登录，请输入您的手机号\n我们将发送验证码到您的手机';
  } else {
    return '请输入您的手机号码\n我们将发送验证码进行身份验证';
  }
}
```

#### UI实现 (`lib/pages/phone_input/phone_input_view.dart`)
```dart
return BaseScaffold(
  title: logic.getPageTitle(), // 动态获取页面标题
  body: Column(
    children: [
      Text(logic.getMainTitle()), // 动态获取主标题
      Text(logic.getPageDescription()), // 动态获取页面描述
    ],
  ),
);
```

### 2. 验证码页面

#### 动态标题方法 (`lib/pages/verification/verification_logic.dart`)
```dart
/// 动态获取页面标题
String getPageTitle() {
  if (bindKey.value.isNotEmpty) {
    return '验证码验证'; // 有bindKey时显示验证码验证
  } else {
    return '手机号验证'; // 没有bindKey时显示手机号验证
  }
}

/// 动态获取页面主标题
String getMainTitle() {
  if (bindKey.value.isNotEmpty) {
    return '请输入验证码';
  } else {
    return '验证手机号';
  }
}

/// 动态获取确认按钮文案
String getConfirmButtonText() {
  if (bindKey.value.isNotEmpty) {
    return '确认绑定'; // 有bindKey时显示确认绑定
  } else {
    return '确认登录'; // 没有bindKey时显示确认登录
  }
}

/// 动态获取底部提示文案
String getBottomTipText() {
  if (bindKey.value.isNotEmpty) {
    return '验证码有效期为5分钟，输入验证码后将完成手机号绑定和登录';
  } else {
    return '验证码有效期为5分钟，输入验证码后将完成登录';
  }
}
```

#### UI实现 (`lib/pages/verification/verification_view.dart`)
```dart
return BaseScaffold(
  title: logic.getPageTitle(), // 动态获取页面标题
  body: Column(
    children: [
      Obx(() => Text(logic.getMainTitle())), // 动态主标题
      Obx(() => Text(logic.getPageDescription())), // 动态描述
      ElevatedButton(
        child: Obx(() => Text(logic.getConfirmButtonText())), // 动态按钮文案
      ),
      Obx(() => Text(logic.getBottomTipText())), // 动态底部提示
    ],
  ),
);
```

## 📋 文案对比

### 手机号输入页面

| 场景 | 页面标题 | 主标题 | 页面描述 |
|------|---------|--------|----------|
| **有bindKey** | 绑定手机号 | 绑定手机号 | 为了完成登录，请输入您的手机号<br>我们将发送验证码到您的手机 |
| **无bindKey** | 手机号登录 | 手机号登录 | 请输入您的手机号码<br>我们将发送验证码进行身份验证 |

### 验证码页面

| 场景 | 页面标题 | 主标题 | 确认按钮 | 底部提示 |
|------|---------|--------|----------|----------|
| **有bindKey** | 验证码验证 | 请输入验证码 | 确认绑定 | 验证码有效期为5分钟，输入验证码后将完成手机号绑定和登录 |
| **无bindKey** | 手机号验证 | 验证手机号 | 确认登录 | 验证码有效期为5分钟，输入验证码后将完成登录 |

## 🔄 使用场景

### 场景1：微信/Apple登录需要绑定手机号（有bindKey）
```
用户点击微信登录 → 微信授权成功 → 后端返回bindKey → 跳转到"绑定手机号"页面
```

**页面显示**:
- 页面标题: "绑定手机号"
- 主标题: "绑定手机号"
- 描述: "为了完成登录，请输入您的手机号..."
- 按钮: "确认绑定"

### 场景2：纯手机号登录（无bindKey）
```
用户选择手机号登录 → 跳转到"手机号登录"页面
```

**页面显示**:
- 页面标题: "手机号登录"
- 主标题: "手机号登录"
- 描述: "请输入您的手机号码..."
- 按钮: "确认登录"

### 场景3：测试功能（无bindKey）
```
点击"测试手机号+验证码登录"按钮 → 跳转到"手机号登录"页面
```

## 🧪 测试方法

### 测试有bindKey的情况
1. 点击"测试微信登录bindKey检测"按钮
2. 观察页面标题显示为"绑定手机号"
3. 观察主标题显示为"绑定手机号"
4. 观察确认按钮显示为"确认绑定"

### 测试无bindKey的情况
1. 点击"测试手机号+验证码登录"按钮
2. 观察页面标题显示为"手机号登录"
3. 观察主标题显示为"手机号登录"
4. 观察确认按钮显示为"确认登录"

## 🔍 判断逻辑

### bindKey检查
```dart
// 在PhoneInputLogic中
if (bindKey != null && bindKey!.isNotEmpty) {
  // 有bindKey：显示绑定相关文案
} else {
  // 无bindKey：显示登录相关文案
}

// 在VerificationLogic中
if (bindKey.value.isNotEmpty) {
  // 有bindKey：显示绑定相关文案
} else {
  // 无bindKey：显示登录相关文案
}
```

### 响应式更新
- 使用`Obx()`包装动态文案，确保bindKey变化时UI自动更新
- 所有动态文案都会根据bindKey的状态实时更新

## 🎨 UI优化

### 1. 一致性
- 所有相关页面都使用统一的判断逻辑
- 文案风格保持一致

### 2. 用户体验
- 明确区分绑定和登录两种场景
- 提供清晰的操作指引

### 3. 响应式设计
- 使用GetX的响应式特性
- 确保状态变化时UI及时更新

## 📱 支持的页面

- ✅ **手机号输入页面** - 动态标题、主标题、描述
- ✅ **验证码页面** - 动态标题、主标题、描述、按钮文案、底部提示
- ✅ **所有相关UI组件** - 完整的动态文案支持

## 🔒 注意事项

### 1. 参数传递
确保bindKey正确传递给手机号输入页面：
```dart
Get.toNamed('/phone-input', arguments: {
  'bindKey': userData['bindKey'], // 重要：正确传递bindKey
  'loginType': loginType,
  'originalData': userData,
});
```

### 2. 空值处理
```dart
// 安全的bindKey检查
if (bindKey != null && bindKey!.isNotEmpty) {
  // 有bindKey的逻辑
}
```

### 3. 响应式更新
```dart
// 使用Obx确保UI响应式更新
Obx(() => Text(logic.getMainTitle()))
```

现在页面会根据是否有bindKey智能显示不同的标题和文案：
- **有bindKey** → "绑定手机号"相关文案
- **无bindKey** → "手机号登录"相关文案

这提供了更准确和用户友好的界面体验！🎯
