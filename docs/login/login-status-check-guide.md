# 登录状态检查功能使用指南

## 🎯 功能概述

实现了完整的登录状态检查机制，在应用启动时自动判断用户登录状态：
- ✅ 如果能获取个人信息 → 跳转到首页
- ✅ 如果无法获取个人信息 → 跳转到登录界面

## 🔧 技术实现

### 1. 获取个人信息API

**接口地址**: `/v3/member/front/member/memberInfo`
**请求方式**: `GET`
**需要Authorization**: `true`

#### API实现 (`lib/utils/http/api.dart`)
```dart
/// 获取个人信息
static Future<NetModel> getMemberInfoApi() async {
  QLog('=== 获取个人信息API请求 ===');
  QLog('接口地址: $memberInfo');
  QLog('请求方式: GET');
  QLog('需要Authorization: true');
  
  var response = await Request().get(memberInfo);
  return NetModel.fromJson(response);
}
```

#### 响应数据结构
```json
{
  "data": {
    "memberId": 21,
    "memberName": "O1882831274",
    "memberNickName": "用户昵称",
    "memberMobile": "13800138000",
    "memberAvatar": "头像URL",
    "memberEmail": "邮箱",
    "gender": 0,
    "isLoginPwd": 0,
    "isPayPwd": 0,
    "isSuper": 0,
    "memberGrowthValue": 0
  },
  "msg": "成功",
  "state": 200,
  "timestamp": 1640995200000
}
```

### 2. 登录状态检查逻辑

#### 核心方法 (`lib/pages/login/login_logic.dart`)
```dart
/// 检查登录状态
static Future<bool> checkLoginStatus() async {
  // 1. 检查本地是否有token
  final token = await Global.getToken();
  if (token == null || token.isEmpty) {
    QLog('本地没有token，需要登录');
    return false;
  }
  
  // 2. 调用获取个人信息接口验证token有效性
  final result = await API.getMemberInfoApi();
  
  if (result.state == 200 && result.data != null) {
    QLog('✅ 登录状态有效');
    await _updateLocalUserInfo(result.data);
    return true;
  } else {
    QLog('❌ 登录状态无效');
    await Global.removeToken();
    return false;
  }
}
```

#### 应用启动处理
```dart
/// 应用启动时的登录状态检查和页面跳转
static Future<void> handleAppStartup() async {
  QLog('=== 应用启动，检查登录状态 ===');
  
  final isLoggedIn = await checkLoginStatus();
  
  if (isLoggedIn) {
    QLog('用户已登录，跳转到首页');
    Get.offAllNamed('/home');
  } else {
    QLog('用户未登录，跳转到登录页面');
    Get.offAllNamed('/login');
  }
}
```

### 3. Splash页面集成

#### 修改 (`lib/pages/splash/splash_logic.dart`)
```dart
Future checkExit() async{
  if(homeFinished && advFinished){
    await videoController?.pause();
    await videoController?.dispose();
    await subscription?.cancel();
    
    // 在退出splash时检查登录状态
    QLog('Splash页面准备退出，检查登录状态...');
    await LoginLogic.handleAppStartup();
  }
}
```

## 🔄 完整流程

### 应用启动流程
```mermaid
graph TD
    A[应用启动] --> B[Splash页面]
    B --> C[广告播放完成]
    C --> D[首页加载完成]
    D --> E[检查登录状态]
    E --> F{本地有token?}
    F -->|无| G[跳转登录页面]
    F -->|有| H[调用获取个人信息API]
    H --> I{API调用成功?}
    I -->|成功| J[更新本地用户信息]
    I -->|失败| K[清除无效token]
    J --> L[跳转首页]
    K --> G
```

### 错误处理流程
```mermaid
graph TD
    A[API调用异常] --> B{异常类型}
    B -->|401/403认证失败| C[清除本地token]
    B -->|网络异常| D[保持登录状态]
    C --> E[跳转登录页面]
    D --> F[跳转首页]
```

## 🧪 测试功能

### 测试按钮
在登录页面（调试模式下）添加了"测试登录状态检查"按钮

### 测试方法
```dart
/// 测试登录状态检查
Future<void> testLoginStatusCheck() async {
  QLog('=== 测试登录状态检查 ===');
  
  final isLoggedIn = await checkLoginStatus();
  
  if (isLoggedIn) {
    QLog('✅ 登录状态检查通过');
    EasyLoading.showSuccess('登录状态有效');
  } else {
    QLog('❌ 登录状态检查失败');
    EasyLoading.showError('登录状态无效，需要重新登录');
  }
}
```

### 测试场景

#### 场景1：有效token
1. 用户已登录，本地有有效token
2. 调用获取个人信息API成功
3. 更新本地用户信息
4. 跳转到首页

#### 场景2：无效token
1. 用户本地有token，但已过期
2. 调用获取个人信息API失败（401/403）
3. 清除本地token
4. 跳转到登录页面

#### 场景3：无token
1. 用户本地没有token
2. 直接跳转到登录页面

#### 场景4：网络异常
1. 用户本地有token
2. 调用API时网络异常
3. 暂时保持登录状态
4. 跳转到首页

## 📋 关键日志

### 成功登录状态
```
=== 应用启动，检查登录状态 ===
本地存在token: eyJhbGciOiJSUzI1NiIsI...
=== 获取个人信息API请求 ===
接口地址: v3/member/front/member/memberInfo
请求方式: GET
需要Authorization: true
=== 获取个人信息API响应 ===
响应数据: {state: 200, data: {...}}
✅ 登录状态有效，用户信息: {...}
本地用户信息已更新
用户已登录，跳转到首页
```

### 无效登录状态
```
=== 应用启动，检查登录状态 ===
本地存在token: eyJhbGciOiJSUzI1NiIsI...
=== 获取个人信息API请求 ===
=== 获取个人信息API异常 ===
异常信息: 401 Unauthorized
❌ 登录状态无效: state=401, msg=未授权
认证失败，清除本地token
用户未登录，跳转到登录页面
```

### 无本地token
```
=== 应用启动，检查登录状态 ===
本地没有token，需要登录
用户未登录，跳转到登录页面
```

## 🔒 安全特性

### 1. Token验证
- 不仅检查本地token存在性
- 通过API调用验证token有效性
- 自动清除无效token

### 2. 错误处理
- 区分认证失败和网络异常
- 认证失败时清除token
- 网络异常时保持登录状态

### 3. 用户信息更新
- 登录状态有效时更新本地用户信息
- 确保用户信息的时效性

## 🚀 使用方法

### 1. 正常使用
应用启动时会自动进行登录状态检查，无需手动调用

### 2. 测试验证
1. 在调试模式下运行应用
2. 进入登录页面
3. 点击"测试登录状态检查"按钮
4. 观察控制台日志和页面跳转

### 3. 手动调用
```dart
// 检查登录状态
final isLoggedIn = await LoginLogic.checkLoginStatus();

// 处理应用启动
await LoginLogic.handleAppStartup();
```

## 📱 支持的页面跳转

- ✅ **登录状态有效** → 跳转到首页 (`/home`)
- ✅ **登录状态无效** → 跳转到登录页面 (`/login`)
- ✅ **使用`Get.offAllNamed`** → 清除所有页面栈

## 🔍 调试信息

### API请求日志
- 请求地址和方式
- Authorization header状态
- 响应数据详情

### 状态检查日志
- Token存在性检查
- API调用结果
- 页面跳转决策

### 异常处理日志
- 具体异常信息
- 异常类型判断
- 处理策略选择

现在您的应用具备了完整的登录状态检查功能，能够在启动时智能判断并跳转到合适的页面！🎯
