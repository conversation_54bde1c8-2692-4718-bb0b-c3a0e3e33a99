# 手机号+验证码登录功能使用指南

## 🔐 功能概述

系统现在支持两种验证码登录模式：
1. **有bindKey的绑定模式** - 用于微信/Apple登录后绑定手机号
2. **无bindKey的直接登录模式** - 用于纯手机号+验证码登录

## 🚀 API接口说明

### 新增接口：手机号+验证码免密登录

**接口地址**: `/v3/member/front/login/freeSecretLogin`
**请求方式**: `POST`
**请求数据类型**: `multipart/form-data`

#### 请求参数
| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 |
|---------|---------|---------|---------|---------|
| mobile | 手机号 | formData | true | string |
| smsCode | 短信验证码 | formData | true | string |
| appType | 会员来源：1、Android；2、IOS | query | false | string |
| clientId | 客户端身份ID | query | false | string |

#### 响应格式
```json
{
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "12345",
      "nickname": "用户名",
      "avatar": "头像URL"
    },
    "expires_in": 7200
  },
  "msg": "登录成功",
  "state": 200,
  "timestamp": 1640995200000
}
```

## 🔄 登录流程

### 流程1：有bindKey的绑定模式
```mermaid
graph TD
    A[微信/Apple登录] --> B[获取授权信息]
    B --> C{检查API响应}
    C -->|有bindKey| D[跳转手机号输入页面]
    D --> E[用户输入手机号]
    E --> F[跳转验证码页面]
    F --> G[调用bindMobile API]
    G --> H[绑定成功，完成登录]
```

### 流程2：无bindKey的直接登录模式
```mermaid
graph TD
    A[用户选择手机号登录] --> B[跳转手机号输入页面]
    B --> C[用户输入手机号]
    C --> D[跳转验证码页面]
    D --> E[调用freeSecretLogin API]
    E --> F[登录成功，跳转主页]
```

## 🔧 技术实现

### 1. API层实现

**文件**: `lib/utils/http/api.dart`

```dart
/// 手机号+验证码免密登录（新接口）
static Future<NetModel> freeSecretLoginApi({
  required String mobile,
  required String smsCode,
  String? appType, // 会员来源：1、Android；2、IOS
  String? clientId,
}) async {
  // 使用multipart/form-data格式
  var params = {
    'mobile': mobile,
    'smsCode': smsCode,
  };

  // 构建查询参数
  var queryParams = <String, String>{};
  if (appType != null && appType.isNotEmpty) {
    queryParams['appType'] = appType;
  }
  if (clientId != null && clientId.isNotEmpty) {
    queryParams['clientId'] = clientId;
  }

  var response = await Request().postFormData(fullUrl, params: params);
  return NetModel.fromJson(response);
}
```

### 2. 验证码页面逻辑

**文件**: `lib/pages/verification/verification_logic.dart`

```dart
/// 验证验证码
Future<void> verifyCode() async {
  // 根据是否有bindKey决定调用哪个API
  if (bindKey.value.isNotEmpty) {
    QLog('检测到bindKey，调用授权登录绑定手机号API');
    await _verifyWithBindKey(code);
  } else {
    QLog('没有bindKey，调用手机号+验证码免密登录API');
    await _verifyWithoutBindKey(code);
  }
}

/// 没有bindKey时的验证逻辑（手机号+验证码免密登录）
Future<void> _verifyWithoutBindKey(String code) async {
  final result = await API.freeSecretLoginApi(
    mobile: phoneNumber.value,
    smsCode: code,
    appType: _getAppType(),
    clientId: await _getClientId(),
  );

  if (result.code == '200' || result.code == 200) {
    EasyLoading.showSuccess('登录成功');
    _handleDirectLoginSuccess(result.data);
  }
}
```

### 3. 登录页面集成

**文件**: `lib/pages/login/login_logic.dart`

```dart
/// 手机号登录
Future<void> login() async {
  // 确定appType
  String appType = GetPlatform.isIOS ? '2' : '1';
  
  // 调用新的手机号+验证码免密登录API
  final result = await API.freeSecretLoginApi(
    mobile: phone,
    smsCode: code,
    appType: appType,
    clientId: clientId,
  );
  
  if (result.code == '200' || result.code == 200) {
    await saveUserInfo(result.data);
    EasyLoading.showSuccess('登录成功');
    Get.back(result: true);
  }
}
```

## 🧪 测试方法

### 1. 测试有bindKey的绑定流程
1. 在调试模式下运行应用
2. 进入登录页面
3. 点击"测试微信登录bindKey检测"按钮
4. 观察完整的绑定流程

### 2. 测试无bindKey的直接登录流程
1. 在调试模式下运行应用
2. 进入登录页面
3. 点击"测试手机号+验证码登录"按钮
4. 观察直接登录流程

### 3. 测试普通手机号登录
1. 在登录页面输入手机号
2. 点击"获取验证码"
3. 输入收到的验证码
4. 点击"登录"按钮

## 📋 关键日志

### 成功日志示例
```
=== 手机号+验证码免密登录API请求 (Multipart Form-Data) ===
接口地址: v3/member/front/login/freeSecretLogin
Content-Type: multipart/form-data
Form参数: {mobile: 13800138000, smsCode: 123456}
Query参数: {appType: 1, clientId: device_12345}
===============================================

没有bindKey，调用手机号+验证码免密登录API
手机号+验证码免密登录API响应: {code: 200, data: {...}}
登录成功
检测到access_token字段: eyJ...
✅ access_token已保存到本地存储
```

### 错误处理
- **验证码错误**: 显示具体错误信息
- **手机号格式错误**: 前端验证提示
- **网络异常**: 显示网络错误提示
- **API异常**: 记录详细错误日志

## 🔍 流程判断逻辑

### 验证码页面的API选择
```dart
// 在verification_logic.dart中
if (bindKey.value.isNotEmpty) {
  // 有bindKey：调用绑定手机号API
  await API.bindMobileApi(bindKey, mobile, smsCode, appType);
} else {
  // 无bindKey：调用免密登录API
  await API.freeSecretLoginApi(mobile, smsCode, appType, clientId);
}
```

### 返回结果处理
```dart
// 有bindKey的情况
Get.back(result: {
  'success': true,
  'bindKey': bindKey.value,
  'phoneNumber': phoneNumber.value,
  'verified': true,
  'userData': data,
});

// 无bindKey的情况
Get.back(result: {
  'success': true,
  'directLogin': true, // 标识直接登录
  'phoneNumber': phoneNumber.value,
  'verified': true,
  'userData': data,
});
```

## 🚀 使用场景

### 场景1：微信登录需要绑定手机号
1. 用户点击微信登录
2. 微信授权成功
3. 后端返回包含bindKey的响应
4. 跳转到手机号输入页面
5. 输入手机号和验证码
6. 调用bindMobile API完成绑定

### 场景2：纯手机号登录
1. 用户选择手机号登录
2. 输入手机号和验证码
3. 调用freeSecretLogin API直接登录
4. 保存access_token和用户信息

### 场景3：从手机号输入页面进入
1. 用户从其他入口进入手机号输入页面
2. 根据是否有bindKey自动选择登录模式
3. 完成相应的登录或绑定流程

## 📱 支持的平台

- ✅ **Android** (appType: "1")
- ✅ **iOS** (appType: "2")
- ✅ 自动检测平台类型
- ✅ 支持clientId传递

## 🔒 安全特性

1. **参数验证**: 前端验证手机号格式和验证码长度
2. **错误处理**: 完善的异常捕获和用户提示
3. **Token管理**: 自动保存和使用access_token
4. **日志记录**: 详细的操作日志便于调试

现在系统支持完整的手机号+验证码登录功能，无论是否有bindKey都能正确处理！🎯
