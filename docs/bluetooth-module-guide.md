# 立马客户仓库蓝牙模块功能流程文档

## 模块概述

立马客户仓库的蓝牙模块基于 Flutter 的 `flutter_blue_plus` 插件构建，提供了完整的蓝牙低功耗（BLE）设备管理功能。该模块主要负责蓝牙设备的扫描、连接、数据传输以及 OTA（Over-The-Air）固件升级等核心功能。

## 模块结构

### 核心文件

- **lib/common/ble/ble_plus.dart**: 蓝牙功能的主要实现类，包含设备扫描、连接管理、数据传输等核心功能
- **lib/common/ble/permissions.dart**: 权限管理模块，处理蓝牙相关权限的检查和申请
- **lib/common/bridge_controller.dart**: 桥接控制器，处理蓝牙数据的拦截、OTA 升级和与前端的通信

### 关键类

- **BlePlus**: 蓝牙功能的单例管理类
- **BluetoothInstance**: 蓝牙设备实例信息类
- **PermissionManager**: 权限管理类

## 功能流程图

### 1. 蓝牙初始化流程

```
应用启动
    ↓
检查蓝牙适配器状态
    ↓
权限检查和申请
    ↓
初始化蓝牙服务
    ↓
准备就绪
```

### 2. 设备搜索和连接流程

```
启动扫描
    ↓
获取扫描结果流
    ↓
缓存扫描到的设备
    ↓
用户选择设备
    ↓
建立连接
    ↓
发现服务和特征值
    ↓
保存读写特征值
    ↓
连接成功
```

### 3. 服务发现流程

```
设备连接成功
    ↓
发现所有服务
    ↓
遍历服务特征值
    ↓
识别读写特征值
    ↓
保存特征值引用
    ↓
注册数据通知
```

### 4. 数据传输流程

```
数据发送:
应用数据 → MTU分割 → 写入特征值 → 设备接收

数据接收:
设备发送 → 特征值通知 → 数据拦截 → 处理分发 → 应用接收
```

### 5. OTA 升级流程

```
检测 OTA 数据
    ↓
解析升级命令
    ↓
计算校验和
    ↓
数据异或处理
    ↓
分包发送
    ↓
监控升级状态
    ↓
完成升级
```

## 关键技术特性

### 权限管理

- **位置权限**: 用于蓝牙设备扫描
- **蓝牙权限**: 包括蓝牙扫描、连接和广播权限
- **动态权限申请**: 根据 Android 版本自动适配权限申请流程
- **权限说明弹窗**: 向用户解释权限用途

### 连接管理

- **连接状态监听**: 实时监控设备连接状态变化
- **自动重连机制**: 支持连接断开后的自动重连
- **多设备管理**: 支持同时管理多个蓝牙设备连接
- **连接缓存**: 保存已连接设备信息，支持快速重连

### 数据传输

- **MTU 自适应**: 根据设备 MTU 自动分割数据包
- **数据拦截**: 对接收到的数据进行预处理和分发
- **异步传输**: 支持异步数据发送和接收
- **错误处理**: 完善的数据传输错误处理机制

### OTA 升级

- **固件升级**: 支持设备固件的无线升级
- **数据校验**: 使用校验和确保数据传输完整性
- **加密处理**: 支持数据异或加密
- **升级监控**: 实时监控升级进度和状态

## 配置参数

### 扫描配置

```dart
// 扫描超时时间
static const Duration SCAN_TIMEOUT = Duration(seconds: 10);

// 扫描模式
static const ScanMode SCAN_MODE = ScanMode.lowLatency;
```

### 连接配置

```dart
// 连接超时时间
static const Duration CONNECTION_TIMEOUT = Duration(seconds: 15);

// 自动连接
static const bool AUTO_CONNECT = true;
```

### 服务 UUID

```dart
// 主要服务 UUID
static const String SERVICE_UUID = "your-service-uuid";

// 读特征值 UUID
static const String READ_CHARACTERISTIC_UUID = "your-read-uuid";

// 写特征值 UUID
static const String WRITE_CHARACTERISTIC_UUID = "your-write-uuid";
```

## 使用场景

### 1. 设备配对

```dart
// 启动扫描
await blePlus.scan();

// 获取扫描结果
Stream<List<ScanResult>> scanResults = blePlus.scanResults;

// 连接设备
await blePlus.connect(device);
```

### 2. 数据发送

```dart
// 发送数据
await blePlus.actionBleWrite(data);

// 批量发送
await blePlus.sendDataWithList(dataList);
```

### 3. 数据接收

```dart
// 注册数据通知
await blePlus.registerDataNotify();

// 监听数据流
blePlus.dataStream.listen((data) {
  // 处理接收到的数据
});
```

### 4. OTA 升级

```dart
// 启动 OTA 升级
await bridgeController.actOTA(firmwareData);

// 监控升级状态
otaStatusStream.listen((status) {
  // 处理升级状态
});
```

## 错误处理

### 常见错误类型

1. **权限错误**: 蓝牙或位置权限未授予
2. **连接错误**: 设备连接失败或超时
3. **服务错误**: 服务发现失败或特征值不可用
4. **传输错误**: 数据发送失败或接收异常
5. **OTA 错误**: 固件升级失败或校验错误

### 错误处理策略

- **重试机制**: 对临时性错误进行自动重试
- **用户提示**: 向用户显示友好的错误信息
- **日志记录**: 详细记录错误信息用于调试
- **降级处理**: 在某些功能不可用时提供替代方案

## 性能优化

### 扫描优化

- **智能扫描**: 根据需要动态调整扫描参数
- **结果缓存**: 缓存扫描结果避免重复扫描
- **过滤机制**: 只扫描目标设备类型

### 连接优化

- **连接池管理**: 合理管理多设备连接
- **心跳检测**: 定期检测连接状态
- **资源释放**: 及时释放不需要的连接资源

### 数据传输优化

- **批量传输**: 合并小数据包减少传输次数
- **压缩算法**: 对大数据进行压缩传输
- **流控制**: 根据设备能力调整传输速率

## 总结

立马客户仓库的蓝牙模块提供了完整的 BLE 设备管理解决方案，具有良好的架构设计和丰富的功能特性。模块支持设备扫描、连接管理、数据传输和 OTA 升级等核心功能，同时具备完善的权限管理和错误处理机制。通过合理的配置和优化，该模块能够满足各种蓝牙应用场景的需求。