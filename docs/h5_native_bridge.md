# H5与原生App交互说明

## 概述

本文档说明H5页面如何与原生Flutter App进行交互，特别是登录功能的调用。

## URL参数

### type=app
所有H5页面都会收到`type=app`参数，用于标识当前运行环境为原生App。

### access_token
当用户已登录时，H5页面会收到`access_token`参数，用于维持登录状态。

## URL示例

### 首页（社区页面）
- 未登录：`https://h5.lima.lightcloudapps.com/#/?type=app`
- 已登录：`https://h5.lima.lightcloudapps.com/#/?type=app&access_token=xxx`

### 我的页面
- 未登录：`https://h5.lima.lightcloudapps.com/#/pages/user/user?type=app`
- 已登录：`https://h5.lima.lightcloudapps.com/#/pages/user/user?type=app&access_token=xxx`

## JavaScript调用原生登录

### 检测环境
```javascript
// 检查是否在App环境中
const urlParams = new URLSearchParams(window.location.search);
const isApp = urlParams.get('type') === 'app';

if (isApp) {
    // 在App环境中，可以调用原生功能
}
```

### 调用登录功能
```javascript
// 当H5页面检测到用户未登录且需要登录时，调用此方法
function callNativeLogin() {
    if (window.jsBridgeHelper) {
        // 发送登录请求到原生App
        window.jsBridgeHelper.sendMessage('receiveMessage', {
            action: 'login'
        }).then(result => {
            console.log('登录结果:', result);
            if (result.success) {
                // 登录成功，页面会自动刷新并带上新的access_token
                console.log('登录成功，页面即将刷新');
            } else {
                console.log('登录失败或取消');
            }
        }).catch(error => {
            console.error('调用登录失败:', error);
        });
    } else {
        console.error('jsBridgeHelper不可用，无法调用原生登录');
    }
}
```

### 完整示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>H5页面示例</title>
</head>
<body>
    <div id="content">
        <h1>欢迎使用LIMA</h1>
        <div id="loginSection">
            <p>您尚未登录</p>
            <button onclick="handleLogin()">登录</button>
        </div>
        <div id="userSection" style="display: none;">
            <p>欢迎回来！</p>
        </div>
    </div>

    <script>
        // 检查登录状态
        function checkLoginStatus() {
            const urlParams = new URLSearchParams(window.location.search);
            const accessToken = urlParams.get('access_token');
            const isApp = urlParams.get('type') === 'app';
            
            if (accessToken) {
                // 已登录
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('userSection').style.display = 'block';
            } else {
                // 未登录
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('userSection').style.display = 'none';
            }
            
            return { isLoggedIn: !!accessToken, isApp: isApp };
        }
        
        // 处理登录
        function handleLogin() {
            const { isApp } = checkLoginStatus();
            
            if (isApp && window.jsBridgeHelper) {
                // 在App环境中，调用原生登录
                window.jsBridgeHelper.sendMessage('receiveMessage', {
                    action: 'login'
                }).then(result => {
                    console.log('登录结果:', result);
                    // 登录成功后页面会自动刷新
                }).catch(error => {
                    console.error('登录失败:', error);
                    alert('登录失败，请重试');
                });
            } else {
                // 在浏览器环境中，使用H5登录流程
                alert('请使用H5登录流程');
            }
        }
        
        // 页面加载时检查登录状态
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>
```

## 原生App处理流程

1. **接收登录请求**：当H5页面调用`jsBridgeHelper.sendMessage`时，原生App会收到包含`action: 'login'`的消息
2. **跳转登录页**：App跳转到原生登录页面
3. **登录完成**：用户完成登录后，App会重新构建URL（包含新的access_token）
4. **刷新页面**：App使用新的URL刷新H5页面，H5页面会收到access_token参数

## 注意事项

1. **环境检测**：H5页面应该先检查`type=app`参数来确认是否在App环境中
2. **降级处理**：如果不在App环境中，应该使用H5的登录流程
3. **错误处理**：调用原生功能时应该有适当的错误处理
4. **用户体验**：登录过程中可以显示加载状态，提升用户体验

## 扩展功能

除了登录功能，还可以通过相同的方式扩展其他原生功能：

```javascript
// 示例：调用其他原生功能
window.jsBridgeHelper.sendMessage('receiveMessage', {
    action: 'share',
    data: {
        title: '分享标题',
        content: '分享内容',
        url: 'https://example.com'
    }
});
```
