# 缓存管理API使用指南

## 概述

本文档介绍如何在H5页面中使用原生App的缓存管理功能，包括获取缓存信息和清除缓存操作。

## 功能特性

### 支持的缓存类型

1. **WebView缓存** (`webview`) - WebView页面实例缓存
2. **Token缓存** (`token`) - 用户登录Token和相关认证信息
3. **广告缓存** (`advertisement`) - 广告图片和视频文件缓存
4. **用户数据缓存** (`userdata`) - 用户设置和应用数据
5. **临时文件缓存** (`tempfiles`) - 系统临时文件
6. **所有缓存** (`all`) - 上述所有类型的缓存

### 主要API

- `getCacheInfo` - 获取缓存信息
- `clearCache` - 清除指定类型的缓存

## API详细说明

### 1. 获取缓存信息 (getCacheInfo)

#### 语法
```javascript
window.jsBridgeHelper.sendMessage('getCacheInfo', {
    type: 'cacheType'
}).then(result => {
    console.log('缓存信息:', result);
});
```

#### 参数
- `type` (string): 缓存类型
  - `'all'` - 获取所有缓存信息
  - `'webview'` - WebView缓存
  - `'token'` - Token缓存
  - `'advertisement'` - 广告缓存
  - `'userdata'` - 用户数据缓存
  - `'tempfiles'` - 临时文件缓存

#### 返回值
```javascript
{
    "success": true,
    "data": {
        // 单个缓存类型时
        "type": "webview",
        "sizeBytes": 5242880,
        "sizeMB": "5.00",
        "count": 3,
        "lastModified": 1640995200000,
        "description": "WebView页面缓存"
        
        // 或者所有缓存类型时
        "caches": [
            {
                "type": "webview",
                "sizeBytes": 5242880,
                "sizeMB": "5.00",
                "count": 3,
                "description": "WebView页面缓存"
            },
            // ... 其他缓存类型
        ],
        "summary": {
            "totalSizeBytes": 15728640,
            "totalSizeMB": "15.00",
            "totalCount": 25,
            "lastUpdated": 1640995200000
        }
    }
}
```

#### 示例
```javascript
// 获取所有缓存信息
async function getAllCacheInfo() {
    try {
        const result = await window.jsBridgeHelper.sendMessage('getCacheInfo', {
            type: 'all'
        });
        
        if (result.success) {
            console.log('总缓存大小:', result.data.summary.totalSizeMB + ' MB');
            result.data.caches.forEach(cache => {
                console.log(`${cache.description}: ${cache.sizeMB} MB`);
            });
        }
    } catch (error) {
        console.error('获取缓存信息失败:', error);
    }
}

// 获取特定类型缓存信息
async function getWebViewCacheInfo() {
    try {
        const result = await window.jsBridgeHelper.sendMessage('getCacheInfo', {
            type: 'webview'
        });
        
        if (result.success) {
            console.log('WebView缓存:', result.data);
        }
    } catch (error) {
        console.error('获取WebView缓存信息失败:', error);
    }
}
```

### 2. 清除缓存 (clearCache)

#### 语法
```javascript
window.jsBridgeHelper.sendMessage('clearCache', {
    type: 'cacheType'
}).then(result => {
    console.log('清除结果:', result);
});
```

#### 参数
- `type` (string): 要清除的缓存类型（同getCacheInfo）

#### 返回值
```javascript
{
    "success": true,
    "message": "WebView缓存清除成功"
    
    // 清除所有缓存时会包含详细结果
    "details": {
        "webview": true,
        "token": true,
        "advertisement": false,
        "userdata": true,
        "tempfiles": true
    }
}
```

#### 示例
```javascript
// 清除WebView缓存
async function clearWebViewCache() {
    try {
        const result = await window.jsBridgeHelper.sendMessage('clearCache', {
            type: 'webview'
        });
        
        if (result.success) {
            console.log('清除成功:', result.message);
        } else {
            console.error('清除失败:', result.error);
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
    }
}

// 清除所有缓存（谨慎使用）
async function clearAllCache() {
    if (confirm('确定要清除所有缓存吗？此操作不可撤销。')) {
        try {
            const result = await window.jsBridgeHelper.sendMessage('clearCache', {
                type: 'all'
            });
            
            if (result.success) {
                console.log('清除完成:', result.message);
                if (result.details) {
                    Object.entries(result.details).forEach(([type, success]) => {
                        console.log(`${type}: ${success ? '成功' : '失败'}`);
                    });
                }
            }
        } catch (error) {
            console.error('清除所有缓存失败:', error);
        }
    }
}
```

## 完整示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>缓存管理示例</title>
</head>
<body>
    <h1>缓存管理</h1>
    
    <div>
        <h3>获取缓存信息</h3>
        <button onclick="getCacheInfo('all')">获取所有缓存</button>
        <button onclick="getCacheInfo('webview')">获取WebView缓存</button>
        <div id="cacheInfo"></div>
    </div>
    
    <div>
        <h3>清除缓存</h3>
        <button onclick="clearCache('webview')">清除WebView缓存</button>
        <button onclick="clearCache('token')">清除Token缓存</button>
        <div id="clearResult"></div>
    </div>

    <script>
        // 检查环境
        function checkEnvironment() {
            const urlParams = new URLSearchParams(window.location.search);
            const isApp = urlParams.get('type') === 'app';
            const hasBridge = window.jsBridgeHelper !== undefined;
            
            if (!isApp || !hasBridge) {
                alert('请在App环境中使用此功能');
                return false;
            }
            return true;
        }

        // 获取缓存信息
        async function getCacheInfo(type) {
            if (!checkEnvironment()) return;
            
            try {
                const result = await window.jsBridgeHelper.sendMessage('getCacheInfo', {
                    type: type
                });
                
                document.getElementById('cacheInfo').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('获取缓存信息失败:', error);
            }
        }

        // 清除缓存
        async function clearCache(type) {
            if (!checkEnvironment()) return;
            
            if (!confirm(`确定要清除${type}缓存吗？`)) return;
            
            try {
                const result = await window.jsBridgeHelper.sendMessage('clearCache', {
                    type: type
                });
                
                document.getElementById('clearResult').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('清除缓存失败:', error);
            }
        }
    </script>
</body>
</html>
```

## 注意事项

1. **环境检测**: 使用前请确保在App环境中（URL包含`type=app`参数）
2. **权限确认**: 清除缓存操作建议添加用户确认对话框
3. **错误处理**: 所有API调用都应该包含适当的错误处理
4. **性能考虑**: 获取缓存信息可能需要一些时间，建议显示加载状态
5. **数据安全**: 清除Token缓存会导致用户需要重新登录

## 测试页面

项目中提供了完整的测试页面：`assets/cache_api_example.html`

可以在App中打开此页面来测试所有缓存管理功能。

## 错误码说明

- `success: true` - 操作成功
- `success: false` - 操作失败，查看`error`字段获取详细错误信息

常见错误：
- "不支持的缓存类型" - 传入了无效的缓存类型参数
- "获取缓存信息失败" - 系统级别的错误，可能是权限或文件系统问题
- "清除缓存失败" - 清除操作失败，可能是文件被占用或权限不足
