# 缓存管理API测试指南

## 测试环境准备

1. **确保应用已编译成功** ✅
2. **在设备上运行应用**
3. **准备测试页面**

## 测试方法

### 方法1：使用内置测试页面

1. 在App中打开WebView页面
2. 导航到测试页面：`assets/cache_api_example.html`
3. 页面会自动检测环境并显示缓存管理界面

### 方法2：在现有H5页面中测试

在任何H5页面的控制台中执行以下代码：

```javascript
// 测试获取所有缓存信息
window.jsBridgeHelper.sendMessage('getCacheInfo', {type: 'all'})
  .then(result => {
    console.log('缓存信息:', result);
  })
  .catch(error => {
    console.error('获取失败:', error);
  });

// 测试获取WebView缓存信息
window.jsBridgeHelper.sendMessage('getCacheInfo', {type: 'webview'})
  .then(result => {
    console.log('WebView缓存:', result);
  });

// 测试清除WebView缓存
window.jsBridgeHelper.sendMessage('clearCache', {type: 'webview'})
  .then(result => {
    console.log('清除结果:', result);
  });
```

### 方法3：创建简单测试页面

创建一个简单的HTML文件进行测试：

```html
<!DOCTYPE html>
<html>
<head>
    <title>缓存API测试</title>
</head>
<body>
    <h1>缓存管理测试</h1>
    
    <button onclick="testGetCache()">获取缓存信息</button>
    <button onclick="testClearCache()">清除WebView缓存</button>
    
    <div id="result"></div>

    <script>
        async function testGetCache() {
            try {
                const result = await window.jsBridgeHelper.sendMessage('getCacheInfo', {
                    type: 'all'
                });
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('测试失败:', error);
                alert('测试失败: ' + error.message);
            }
        }

        async function testClearCache() {
            try {
                const result = await window.jsBridgeHelper.sendMessage('clearCache', {
                    type: 'webview'
                });
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('测试失败:', error);
                alert('测试失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
```

## 预期结果

### 获取缓存信息 (getCacheInfo)

**成功响应示例：**
```json
{
  "success": true,
  "data": {
    "caches": [
      {
        "type": "webview",
        "sizeBytes": 5242880,
        "sizeMB": "5.00",
        "count": 3,
        "description": "WebView页面缓存"
      },
      {
        "type": "token",
        "sizeBytes": 1024,
        "sizeMB": "0.00",
        "count": 2,
        "description": "用户Token缓存"
      }
    ],
    "summary": {
      "totalSizeBytes": 15728640,
      "totalSizeMB": "15.00",
      "totalCount": 25,
      "lastUpdated": 1640995200000
    }
  }
}
```

### 清除缓存 (clearCache)

**成功响应示例：**
```json
{
  "success": true,
  "message": "WebView缓存清除成功"
}
```

**清除所有缓存的响应：**
```json
{
  "success": true,
  "message": "缓存清除完成: 5/5 成功",
  "details": {
    "webview": true,
    "token": true,
    "advertisement": true,
    "userdata": true,
    "tempfiles": true
  }
}
```

## 错误处理测试

### 测试无效参数
```javascript
// 测试无效的缓存类型
window.jsBridgeHelper.sendMessage('getCacheInfo', {type: 'invalid'})
  .then(result => {
    console.log('应该返回错误:', result);
    // 预期: {"success": false, "error": "不支持的缓存类型: invalid"}
  });
```

### 测试环境检测
```javascript
// 检查是否在App环境中
const urlParams = new URLSearchParams(window.location.search);
const isApp = urlParams.get('type') === 'app';
const hasBridge = window.jsBridgeHelper !== undefined;

console.log('App环境:', isApp);
console.log('Bridge可用:', hasBridge);

if (!isApp || !hasBridge) {
    console.log('⚠️ 当前环境不支持缓存API');
}
```

## 日志检查

在原生App的日志中查找以下关键词：
- `JsBridgeController: 获取缓存信息请求`
- `JsBridgeController: 缓存信息获取完成`
- `JsBridgeController: 清除缓存请求`
- `CacheManager: 开始获取所有缓存信息`
- `WebViewCacheManager: 清理缓存`

## 常见问题排查

### 1. 方法未定义错误
- 确保编译成功
- 检查方法是否正确添加到JsBridgeController类中
- 重新清理并编译项目

### 2. Bridge不可用
- 确保在App环境中测试（URL包含`type=app`）
- 检查`window.jsBridgeHelper`是否存在
- 确保WebView正确初始化了JavaScript Bridge

### 3. 权限错误
- 检查文件系统权限
- 确保应用有访问存储的权限

### 4. 缓存信息不准确
- 某些缓存大小是估算值
- 临时文件可能随时变化
- WebView缓存大小基于实例数量估算

## 性能注意事项

1. **获取缓存信息可能需要时间**，特别是计算文件大小时
2. **清除缓存操作是异步的**，大文件清除可能需要更长时间
3. **频繁调用可能影响性能**，建议适当限制调用频率

## 安全考虑

1. **清除Token缓存会导致用户需要重新登录**
2. **清除所有缓存是危险操作**，建议添加二次确认
3. **用户数据缓存清除可能丢失用户设置**

## 测试清单

- [ ] 获取所有缓存信息
- [ ] 获取单个类型缓存信息
- [ ] 清除WebView缓存
- [ ] 清除Token缓存
- [ ] 清除广告缓存
- [ ] 清除用户数据缓存
- [ ] 清除临时文件缓存
- [ ] 清除所有缓存
- [ ] 错误参数处理
- [ ] 环境检测
- [ ] 日志输出正常
- [ ] 性能表现良好
