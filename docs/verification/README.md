# 验证码页面使用说明

## 📱 功能概述

验证码页面用于处理微信登录成功后需要验证码验证的场景。当微信登录API返回包含`bindKey`的响应时，系统会自动跳转到此页面进行验证码验证。

## 🔄 工作流程

```mermaid
graph TD
    A[微信登录API调用] --> B{检查响应}
    B -->|有bindKey| C[跳转验证码页面]
    B -->|无bindKey| D[直接登录成功]
    C --> E[自动发送验证码到手机]
    E --> F[用户输入验证码]
    F --> G[调用绑定手机号API]
    G -->|成功| H[完成手机号绑定和登录]
    G -->|失败| I[显示错误信息]
    H --> J[返回登录结果]
    I --> F
```

## 🚀 API接口

### 1. 获取验证码接口
- **接口地址**: `/v3/msg/front/commons/smsCode`
- **请求方法**: GET
- **请求参数**:
  - `mobile`: 手机号

### 2. 授权登录绑定手机号接口
- **接口地址**: `/v3/member/front/login/wechat/bindMobile`
- **请求方法**: POST
- **请求数据类型**: `multipart/form-data`
- **请求参数**:
  - `bindKey`: 用户信息code（必须）
  - `mobile`: 将绑定的手机号（必须）
  - `smsCode`: 将绑定的手机号验证码（可选）
  - `aliCode`: 阿里一键获取手机号CODE（可选）
  - `appType`: app类型，1-android，2-ios（可选）
  - `clientId`: 客户端身份ID app传参（可选）

### 3. API响应格式
```json
{
  "data": "结果对象",
  "msg": "消息",
  "state": 200, // 200==成功，255==失败，267==需要特殊处理，400==请求无效，403==禁止访问，404==请求地址不存在，503==服务器异常
  "timestamp": 1750226588226
}
```

## 📋 使用方法

### 1. 基本使用
```dart
// 跳转到验证码页面
final result = await Get.toNamed('/verification', arguments: {
  'bindKey': 'a4291165-0141-449f-85ff-534ee25f9271',
  'phoneNumber': '13800138000',
  'loginType': 'wechat',
  'originalData': userData,
});

// 处理返回结果
if (result != null && result['success'] == true) {
  print('验证成功');
} else {
  print('验证失败');
}
```

### 2. 微信登录集成
当微信登录API返回以下响应时：
```json
{
  "code": 200,
  "state": 200,
  "msg": "成功",
  "data": {
    "redirect": false,
    "bindKey": "a4291165-0141-449f-85ff-534ee25f9271"
  },
  "timestamp": 1750226588226
}
```

系统会自动检测`bindKey`并跳转到验证码页面。

## 🎨 页面特性

### UI特性
- ✅ 现代化圆角设计
- ✅ 6位数字输入框，自动居中显示
- ✅ 60秒倒计时重新发送功能
- ✅ 手机号脱敏显示（138****8000）
- ✅ 自动验证（输入6位数字后自动触发）
- ✅ 优雅的错误提示和加载状态

### 功能特性
- ✅ 自动发送验证码（页面初始化时）
- ✅ 重新发送验证码（60秒倒计时）
- ✅ 实时输入验证
- ✅ 完整的错误处理
- ✅ 详细的日志记录

## 🧪 测试方法

### 1. 使用测试页面
```dart
// 跳转到测试页面
Get.toNamed('/verification-test');
```

### 2. 直接测试
```dart
// 测试有bindKey的情况
Get.toNamed('/verification', arguments: {
  'bindKey': 'test-bind-key-123',
  'phoneNumber': '13800138000',
  'loginType': 'wechat',
});
```

## 📁 文件结构

```
lib/pages/verification/
├── verification_logic.dart    # GetX控制器
├── verification_view.dart     # 页面UI
├── verification_test.dart     # 测试页面
└── README.md                  # 使用说明
```

## 🔧 配置说明

### 路由配置
在 `lib/routers/app_router.dart` 中已添加：
```dart
static const String verification = '/verification';

GetPage(
  name: verification,
  page: () => const VerificationPage(),
  transition: Transition.rightToLeft,
  transitionDuration: const Duration(milliseconds: 300),
)
```

### API配置
在 `lib/utils/http/api.dart` 中已添加：
```dart
static String sendVerifyCode = 'v3/msg/front/commons/sendVerifyCode';
static String verifyCode = 'v3/msg/front/commons/verifyCode';
```

## 🚨 注意事项

1. **手机号格式**: 确保传入的手机号是完整的11位数字
2. **bindKey**: 必须是有效的bindKey，否则验证会失败
3. **网络环境**: 确保设备能正常访问验证码发送接口
4. **倒计时**: 60秒内不能重复发送验证码
5. **验证码有效期**: 验证码有效期为5分钟

## 🔍 调试信息

页面会输出详细的调试日志，包括：
- 页面初始化参数
- API请求和响应
- 用户操作记录
- 错误信息详情

查看日志可以帮助快速定位问题。

## 📋 API调用示例

```dart
// 获取验证码
final result = await API.getSmsCodeApi(
  mobile: '13800138000',  // 手机号
);

// 绑定手机号
final result = await API.bindMobileApi(
  bindKey: 'a4291165-0141-449f-85ff-534ee25f9271',
  mobile: '13800138000',
  smsCode: '123456',
  appType: '2', // 1-android，2-ios
);
```

## 🔄 完整流程示例

```dart
// 1. 微信登录成功，检测到bindKey
final wechatResponse = {
  "code": 200,
  "state": 200,
  "msg": "成功",
  "data": {
    "redirect": false,
    "bindKey": "a4291165-0141-449f-85ff-534ee25f9271"
  }
};

// 2. 跳转到验证码页面
final result = await Get.toNamed('/verification', arguments: {
  'bindKey': wechatResponse['data']['bindKey'],
  'phoneNumber': '13800138000', // 用户输入的手机号
  'loginType': 'wechat',
});

// 3. 处理验证结果
if (result != null && result['success'] == true) {
  print('手机号绑定成功，登录完成');
  // 跳转到主页面
} else {
  print('手机号绑定失败');
  // 处理失败逻辑
}
```
