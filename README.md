# Lima Flutter 移动应用

> 基于 Flutter 开发的跨平台移动应用，集成多种功能模块

## 🚀 快速开始

### 环境要求
- Flutter 3.27.0+
- Dart SDK 3.0.0+
- JDK 17
- Android SDK 35.0.0

### 安装和运行
```bash
# 克隆项目
git clone http://git.xemestudio.com/almondmeng/lima-app-frontend.git
cd lima-app-frontend

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

## 📱 核心功能

- **🔐 多种登录方式**: 手机验证码、微信、苹果、运营商一键登录
- **📡 蓝牙设备管理**: BLE设备连接、数据传输、OTA升级
- **🗺️ 地图定位**: 高德地图集成、实时定位
- **📢 消息推送**: 个推SDK、本地通知
- **💳 支付集成**: 支付宝、微信支付
- **📷 媒体功能**: 拍照、扫码、视频播放

## 🏗️ 项目架构

```
lima/
├── lib/
│   ├── common/          # 公共模块
│   ├── pages/           # 页面模块  
│   ├── utils/           # 工具类
│   └── res/             # 资源文件
├── docs/                # 📚 项目文档
├── android/             # Android配置
└── ios/                 # iOS配置
```

## 📚 文档

完整的技术文档请查看 [docs/](./docs/) 目录：

- [📋 项目总览](./docs/README.md) - 项目详细介绍和架构说明
- [🔧 Flutter升级指南](./docs/flutter-upgrade-guide.md) - 版本升级和依赖更新
- [🔐 登录功能说明](./docs/login-feature-guide.md) - 多种登录方式实现
- [📶 阿里云DYPNS集成](./docs/alicloud-dypns-integration.md) - 运营商一键登录
- [📱 iOS性能优化](./docs/ios-performance-optimization.md) - 启动性能分析和优化
- [🔵 蓝牙模块文档](./docs/bluetooth-module-guide.md) - BLE设备管理和通信

## 🛠️ 开发

### 构建命令
```bash
# Android构建
flutter build apk --release

# iOS构建  
flutter build ios --release

# 清理项目
flutter clean
```

### 部署脚本
- `deploy_simple.sh` - 简单部署
- `deploy_testflight.sh` - TestFlight部署

## 🔧 技术栈

- **框架**: Flutter 3.27.0
- **状态管理**: GetX
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **UI适配**: flutter_screenutil
- **权限管理**: permission_handler

## 📄 许可证

本项目采用私有许可证，仅供内部开发使用。

---

**版本**: 2.0.13+28  
**最后更新**: 2025年1月15日