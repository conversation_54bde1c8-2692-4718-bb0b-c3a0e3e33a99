#!/bin/bash

echo "🔍 测试Gradle代理设置..."
echo "=================================="

# 测试代理连接
echo "1. 测试代理服务器连接..."
if curl -s --proxy http://127.0.0.1:7890 --connect-timeout 5 https://www.google.com > /dev/null; then
    echo "✅ 代理服务器正常"
else
    echo "❌ 代理服务器连接失败"
fi

# 测试Maven仓库连接
echo ""
echo "2. 测试Maven仓库连接..."

echo "   📦 Google Maven (通过代理)..."
if curl -s --proxy http://127.0.0.1:7890 --connect-timeout 10 -I https://maven.google.com/ | grep -q "200\|301"; then
    echo "   ✅ Google Maven可达"
else
    echo "   ❌ Google Maven不可达"
fi

echo "   📦 阿里云Maven (直连)..."
if curl -s --connect-timeout 5 -I https://maven.aliyun.com/repository/central/ | grep -q "200\|404"; then
    echo "   ✅ 阿里云Maven可达"
else
    echo "   ❌ 阿里云Maven不可达"
fi

echo "   📦 Maven Central (通过代理)..."
if curl -s --proxy http://127.0.0.1:7890 --connect-timeout 10 -I https://repo1.maven.org/maven2/ | grep -q "200"; then
    echo "   ✅ Maven Central可达"
else
    echo "   ❌ Maven Central不可达"
fi

echo ""
echo "3. 当前Gradle配置..."
echo "   全局配置: ~/.gradle/gradle.properties"
echo "   项目配置: android/gradle.properties"

echo ""
echo "4. 建议的优化..."
echo "   ⚡ 内存已优化至6GB(全局)/4GB(项目)"
echo "   🚀 并行构建已启用"
echo "   💾 构建缓存已启用"
echo "   🌐 代理设置已优化"
echo "   ⏱️  连接超时已延长至5分钟"

echo ""
echo "🎯 预期效果:"
echo "   首次编译: 3-5分钟"
echo "   增量编译: 30秒-2分钟"

echo ""
echo "🔧 如果还是很慢，请检查:"
echo "   1. 代理软件是否正常运行在7890端口"
echo "   2. 网络连接是否稳定"
echo "   3. 防病毒软件是否影响构建"