import UIKit
import Flutter
import AMapLocation<PERSON>it


@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
      initAmapConfig()
    
      if #available(iOS 10.0, *) {
          UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
      }
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
    func initAmapConfig(){
        AMapLocationManager.updatePrivacyShow(.didShow, privacyInfo: .didContain)
        AMapLocationManager.updatePrivacyAgree(.didAgree)
        
        if Bundle.main.bundleIdentifier == "Senthink.LIMAVehicleStore"{
            AMapServices.shared().apiKey = "57ebb71c0005bdf5129d46df70faef23";
        }else{
            AMapServices.shared().apiKey = "27946bc6c3863cead8f1bbaa50dcadd6";
        }
    }
}
