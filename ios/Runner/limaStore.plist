<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>立马科技</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>lima</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxd92e6f7ab4454e9d</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>limavehicle</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>alipay2018091961462479</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb3493392610</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>3</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>iosamap</string>
		<string>qqmap</string>
		<string>baidumap</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>sinaweibosso</string>
		<string>weibosdk</string>
		<string>weibosdk2.5</string>
		<string>tbopen</string>
		<string>tmall</string>
		<string>mqqopensdkminiapp</string>
		<string>mqzone</string>
		<string>mqqopensdklaunchminiapp</string>
		<string>mqqopensdkapiV2</string>
		<string>tim</string>
		<string>mqqapi</string>
		<string>mqq</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqopensdkapiV4</string>
		<string>tel</string>
		<string>alipay</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MOBAppKey</key>
	<string>37736904df830</string>
	<key>MOBAppSecret</key>
	<string>c2b9dcbefceaaea70437d70c8ee8f4c9</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>applinks:a5fdentitlements977ed5a03f35d04b8ecccff8e8b0.share2dlink.com/</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>使用您的蓝牙进行设备连接</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>App使用您的蓝牙进行设备连接</string>
	<key>NSCameraUsageDescription</key>
	<string>需要您的授权使用相机扫描二维码</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>立马科技会在获取附近服务商,定位地图等服务中使用您的位置信息</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>立马科技会在获取附近服务商,定位地图等服务中使用您的位置信息</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>立马科技会在获取附近服务商,定位地图等服务中使用您的位置信息</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Flutter requires access to microphone.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>立马科技需要使用您的相册获取二维码图片</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
