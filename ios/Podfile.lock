PODS:
  - ali_auth (1.3.0):
    - Flutter
    - MBProgressHUD
    - MJExtension
    - SDWebImage
  - alipay_kit_ios (6.0.1):
    - alipay_kit_ios/utdid (= 6.0.1)
    - alipay_kit_ios/vendor (= 6.0.1)
    - Flutter
  - alipay_kit_ios/utdid (6.0.1):
    - Flutter
  - alipay_kit_ios/vendor (6.0.1):
    - Flutter
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_image_gallery_saver (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - getuiflut (0.0.1):
    - Flutter
    - GTSDK
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (4.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 3.0.0)
  - GoogleMLKit/MLKitCore (4.0.0):
    - MLKitCommon (~> 9.0.0)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTCommonSDK (3.1.9.0):
    - ZXSDK
  - GTMSessionFetcher/Core (2.3.0)
  - GTSDK (3.0.10.0):
    - GTCommonSDK (> 3.0.9.0)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - install_plugin (2.0.0):
    - Flutter
  - MBProgressHUD (1.2.0)
  - MJExtension (3.4.2)
  - MLImage (1.0.0-beta4)
  - MLKitBarcodeScanning (3.0.0):
    - MLKitCommon (~> 9.0)
    - MLKitVision (~> 5.0)
  - MLKitCommon (9.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - MLKitVision (5.0.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
    - MLImage (= 1.0.0-beta4)
    - MLKitCommon (~> 9.0)
  - mobile_scanner (3.5.6):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 4.0.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - wechat_kit (6.0.2):
    - Flutter
    - wechat_kit/pay (= 6.0.2)
    - wechat_kit/vendor (= 6.0.2)
  - wechat_kit/pay (6.0.2):
    - Flutter
  - wechat_kit/vendor (6.0.2):
    - Flutter
  - YYModel (1.0.4)
  - ZXSDK (3.3.2)

DEPENDENCIES:
  - ali_auth (from `.symlinks/plugins/ali_auth/ios`)
  - alipay_kit_ios (from `.symlinks/plugins/alipay_kit_ios/ios`)
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_image_gallery_saver (from `.symlinks/plugins/flutter_image_gallery_saver/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - getuiflut (from `.symlinks/plugins/getuiflut/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - install_plugin (from `.symlinks/plugins/install_plugin/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - wechat_kit (from `.symlinks/plugins/wechat_kit/ios`)
  - YYModel

SPEC REPOS:
  trunk:
    - AMapFoundation
    - AMapLocation
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTCommonSDK
    - GTMSessionFetcher
    - GTSDK
    - MBProgressHUD
    - MJExtension
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - YYModel
    - ZXSDK

EXTERNAL SOURCES:
  ali_auth:
    :path: ".symlinks/plugins/ali_auth/ios"
  alipay_kit_ios:
    :path: ".symlinks/plugins/alipay_kit_ios/ios"
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  flutter_image_gallery_saver:
    :path: ".symlinks/plugins/flutter_image_gallery_saver/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  getuiflut:
    :path: ".symlinks/plugins/getuiflut/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  install_plugin:
    :path: ".symlinks/plugins/install_plugin/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  wechat_kit:
    :path: ".symlinks/plugins/wechat_kit/ios"

SPEC CHECKSUMS:
  ali_auth: 1dd7c172d5812e1c2936dbde17ace3d91128c82d
  alipay_kit_ios: ab72c7997f4d3a2d4cd34d25f4b995a281882526
  amap_flutter_location: f033c983c2d4319203ff7b523775579534d0d557
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus_darwin: 20a08bfeaa0f7804d524858d3d8744bcc1b6dbc3
  flutter_image_gallery_saver: 0453c83412e9691abef94c04c8d180724f5083a8
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  getuiflut: ea9b200062f474810671171f9f36985ff5d431d0
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 2bd0dc6253c4d4f227aad460f69215a504b2980e
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTCommonSDK: 5f23150656349e33595ddc544e89312085234cb7
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  GTSDK: 1a2e62b50cf265046c2e48cdb88cd3d8e62bd7ea
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  install_plugin: e17e38d6f504857748a3ec1299d8a2bbeeeea854
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MLImage: 7bb7c4264164ade9bf64f679b40fb29c8f33ee9b
  MLKitBarcodeScanning: 04e264482c5f3810cb89ebc134ef6b61e67db505
  MLKitCommon: c1b791c3e667091918d91bda4bba69a91011e390
  MLKitVision: 8baa5f46ee3352614169b85250574fde38c36f49
  mobile_scanner: b67191637a5ea7ba15652ca208069d2bae1900ab
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  wechat_kit: a2e39552cffaece4d470a83821bb3c9eaa3fcaed
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  ZXSDK: 786338c0a18e98e03eda00699c3bfd2700b97117

PODFILE CHECKSUM: 67d99cc5b2d8321ff8021d6308e2df76ccd16824

COCOAPODS: 1.16.2
