# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :custom_lane do
    # add actions here: https://docs.fastlane.tools/actions
  end

  desc "Build and upload to TestFlight"
  lane :testflight do
    # 增加构建号
    increment_build_number(xcodeproj: "Runner.xcodeproj")

    # 构建应用
    gym(
      scheme: 'Runner',
      clean: true,
      output_name: 'lima.ipa',
      configuration: 'Release',
      export_method: 'app-store',
      export_xcargs: '-allowProvisioningUpdates',
      output_directory: 'build',
    )

    # 上传到TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      notify_external_testers: false
    )

    # 通知打包完成
    notification(
      title: "LIMA iOS",
      subtitle: "TestFlight上传成功",
      message: "应用已上传到TestFlight，等待审核..."
    )
  end
    lane :deployPgyer do
      gym(
      scheme: 'Runner',
      #每次打包之前clean一下
      clean: true,
      output_name: 'lima.ipa',
      # 默认 Release，Release or Debug
      configuration: 'Release',
      # 打包导出方式，包含 app-store, validation, ad-hoc, package, enterprise, development, developer-id and mac-application
      export_method: 'enterprise',
      # 这个设置是为了设置 xcode 自动配置证书和配置文件，当然也可以手动配置，可以参考文档
      export_xcargs: '-allowProvisioningUpdates',
      output_directory:'build',
      )
      # mac上的通知弹窗，通知打包完毕
      notification(app_icon: "./fastlane/icon.png", title: "LIMA", subtitle: "iOS打包成功", message: "准备发布到蒲公英……")
      pgyer(api_key:"",update_description:"测试环境",ipa:"/Users/<USER>/Documents/projects/flutter_project/limaflutter/ios/build/lima.ipa")
    end
end
