<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度链接测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #C70E2D;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #C70E2D;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background-color: #a00b26;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 深度链接测试页面</h1>
        
        <div class="test-section">
            <h3>📱 基础导航测试</h3>
            <div class="description">测试基本的应用内导航功能</div>
            
            <a href="limavehicle://home" class="test-link">🏠 导航到首页</a>
            <a href="limavehicle://vehicle" class="test-link">🚗 导航到车辆页面</a>
            <a href="limavehicle://share" class="test-link">📤 分享功能</a>
        </div>

        <div class="test-section">
            <h3>🔗 MobLink 深度链接测试</h3>
            <div class="description">测试 MobLink 生成的加密深度链接</div>
            
            <a href="limavehicle://com.lima.scooter/11101859070967394304?params=QH5p7f%2BqzF06TMh5w9mAHPVNmmdd5Lx90BNKC848%2BNv7FGkbM64Or%2BptbgUib3u6%2FIhWh3vJTurPaXVlRhlibtGvESuR4TbTsQVKwmLYYoKfqTXCvG9G1MtrseIj%2FTHjoWEmDAcEaOuXZEG%2BkwY9JBpxsEDv1Qa9wwsX6N61LPZqjlhO5x57gwZcxOPkU2OYGUruAgn0HEHqebk6nNBKrhNgfwlP3aPS4Eyz1x3CoXott73w0A%2B8TjQZYfVVxBjasIZFDf3MAcabhWJqwT7LbxJwhHd8NEiphJx7YNqptQdFR%2F4mfz6gC492%2BXk7PC23s6Sjkju9UN7P1yuIugJBhWgYIEUwdY%2FsCxPHryqMmnzGTTPytrgauwTyBXXeZnTyq5erH9AMIpi3CyNuAQ5X2x%2FQzSM3JHAq8IhPsfG1ew2vY%2F3QvyqyhrS2GONF6HzZ" class="test-link">🔐 MobLink 加密链接</a>
            
            <div class="code">
                limavehicle://com.lima.scooter/11101859070967394304?params=QH5p7f%2BqzF06TMh5w9mAHPVNmmdd5Lx90BNKC848%2BNv7FGkbM64Or%2BptbgUib3u6%2FIhWh3vJTurPaXVlRhlibtGvESuR4TbTsQVKwmLYYoKfqTXCvG9G1MtrseIj%2FTHjoWEmDAcEaOuXZEG%2BkwY9JBpxsEDv1Qa9wwsX6N61LPZqjlhO5x57gwZcxOPkU2OYGUruAgn0HEHqebk6nNBKrhNgfwlP3aPS4Eyz1x3CoXott73w0A%2B8TjQZYfVVxBjasIZFDf3MAcabhWJqwT7LbxJwhHd8NEiphJx7YNqptQdFR%2F4mfz6gC492%2BXk7PC23s6Sjkju9UN7P1yuIugJBhWgYIEUwdY%2FsCxPHryqMmnzGTTPytrgauwTyBXXeZnTyq5erH9AMIpi3CyNuAQ5X2x%2FQzSM3JHAq8IhPsfG1ew2vY%2F3QvyqyhrS2GONF6HzZ
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 参数化测试</h3>
            <div class="description">测试带参数的深度链接</div>
            
            <a href="limavehicle://vehicle?vehicleId=12345&type=electric" class="test-link">🚗 车辆详情 (ID: 12345)</a>
            <a href="limavehicle://share?type=link&content=https://shanghailima.com" class="test-link">📤 分享链接</a>
        </div>

        <div class="test-section">
            <h3>📋 JavaScript 测试</h3>
            <div class="description">通过 JavaScript 触发深度链接</div>
            
            <button onclick="testDeepLink('limavehicle://home')" class="test-link">JS 导航到首页</button>
            <button onclick="testDeepLink('limavehicle://vehicle?vehicleId=99999')" class="test-link">JS 车辆页面</button>
        </div>

        <div class="test-section">
            <h3>📝 测试说明</h3>
            <div class="description">
                <p><strong>测试步骤：</strong></p>
                <ol>
                    <li>点击上方的测试链接</li>
                    <li>观察应用的响应和导航行为</li>
                    <li>检查控制台日志输出</li>
                    <li>验证是否正确处理了自定义 scheme</li>
                </ol>
                
                <p><strong>预期结果：</strong></p>
                <ul>
                    <li>✅ 不再出现 "UNSUPPORTED_SCHEME" 错误</li>
                    <li>✅ 正确解析和处理深度链接</li>
                    <li>✅ 根据链接内容进行相应的导航</li>
                    <li>✅ 在日志中看到处理过程</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testDeepLink(url) {
            console.log('JavaScript 测试深度链接:', url);
            
            // 尝试直接跳转
            try {
                window.location.href = url;
            } catch (e) {
                console.error('JavaScript 跳转失败:', e);
                alert('JavaScript 跳转失败: ' + e.message);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('深度链接测试页面加载完成');
            
            // 添加点击事件监听
            document.querySelectorAll('a[href^="limavehicle://"]').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    console.log('点击深度链接:', this.href);
                });
            });
        });
    </script>
</body>
</html>
