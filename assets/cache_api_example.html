<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存管理API示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: #007AFF;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056CC;
        }
        button.danger {
            background-color: #FF3B30;
        }
        button.danger:hover {
            background-color: #D70015;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
            border-left: 4px solid #007AFF;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #FF3B30;
            background-color: #fff5f5;
            color: #d63031;
        }
        .success {
            border-left-color: #34C759;
            background-color: #f0fff4;
            color: #00b894;
        }
        .cache-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .cache-item {
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .cache-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .cache-item p {
            margin: 2px 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ 缓存管理API示例</h1>
        
        <div class="section">
            <h3>📊 获取缓存信息</h3>
            <div class="button-group">
                <button onclick="getCacheInfo('all')">获取所有缓存</button>
                <button onclick="getCacheInfo('webview')">WebView缓存</button>
                <button onclick="getCacheInfo('token')">Token缓存</button>
                <button onclick="getCacheInfo('advertisement')">广告缓存</button>
                <button onclick="getCacheInfo('userdata')">用户数据</button>
                <button onclick="getCacheInfo('tempfiles')">临时文件</button>
            </div>
            <div id="cacheInfoResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>🗑️ 清除缓存</h3>
            <div class="button-group">
                <button class="danger" onclick="clearCache('webview')">清除WebView缓存</button>
                <button class="danger" onclick="clearCache('token')">清除Token缓存</button>
                <button class="danger" onclick="clearCache('advertisement')">清除广告缓存</button>
                <button class="danger" onclick="clearCache('userdata')">清除用户数据</button>
                <button class="danger" onclick="clearCache('tempfiles')">清除临时文件</button>
                <button class="danger" onclick="clearCache('all')" style="background-color: #FF6B6B;">⚠️ 清除所有缓存</button>
            </div>
            <div id="clearCacheResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>📱 环境检测</h3>
            <div id="envInfo">
                <p><strong>当前环境:</strong> <span id="currentEnv">检测中...</span></p>
                <p><strong>Bridge状态:</strong> <span id="bridgeStatus">检测中...</span></p>
                <p><strong>页面URL:</strong> <span id="pageUrl"></span></p>
            </div>
        </div>
    </div>

    <script>
        // 检测环境
        function checkEnvironment() {
            const urlParams = new URLSearchParams(window.location.search);
            const isApp = urlParams.get('type') === 'app';
            const hasBridge = window.jsBridgeHelper !== undefined;
            
            document.getElementById('currentEnv').textContent = isApp ? 'App环境' : '浏览器环境';
            document.getElementById('bridgeStatus').textContent = hasBridge ? '可用' : '不可用';
            document.getElementById('pageUrl').textContent = window.location.href;
            
            if (!isApp || !hasBridge) {
                showResult('envInfo', '⚠️ 当前环境不支持原生API调用，请在App中打开此页面', 'error');
            }
        }

        // 获取缓存信息
        async function getCacheInfo(type) {
            if (!checkBridgeAvailable()) return;
            
            showResult('cacheInfoResult', '正在获取缓存信息...', 'info');
            
            try {
                const result = await window.jsBridgeHelper.sendMessage('getCacheInfo', { type: type });
                console.log('获取缓存信息结果:', result);
                
                if (result && result.success) {
                    let displayText = `✅ 缓存信息获取成功\n\n`;
                    
                    if (type === 'all' && result.data.caches) {
                        // 显示所有缓存的汇总信息
                        displayText += `📊 总览:\n`;
                        displayText += `总大小: ${result.data.summary.totalSizeMB} MB\n`;
                        displayText += `总数量: ${result.data.summary.totalCount} 项\n\n`;
                        
                        // 显示各类缓存详情
                        result.data.caches.forEach(cache => {
                            displayText += `📁 ${cache.description}:\n`;
                            displayText += `  大小: ${cache.sizeMB} MB (${cache.sizeBytes} bytes)\n`;
                            displayText += `  数量: ${cache.count} 项\n`;
                            if (cache.lastModified) {
                                displayText += `  最后修改: ${new Date(cache.lastModified).toLocaleString()}\n`;
                            }
                            displayText += `\n`;
                        });
                    } else if (result.data.type) {
                        // 显示单个缓存类型的信息
                        displayText += `📁 ${result.data.description}:\n`;
                        displayText += `大小: ${result.data.sizeMB} MB (${result.data.sizeBytes} bytes)\n`;
                        displayText += `数量: ${result.data.count} 项\n`;
                        if (result.data.lastModified) {
                            displayText += `最后修改: ${new Date(result.data.lastModified).toLocaleString()}\n`;
                        }
                    }
                    
                    showResult('cacheInfoResult', displayText, 'success');
                } else {
                    showResult('cacheInfoResult', `❌ 获取缓存信息失败: ${result?.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('获取缓存信息失败:', error);
                showResult('cacheInfoResult', `❌ 获取缓存信息失败: ${error.message}`, 'error');
            }
        }

        // 清除缓存
        async function clearCache(type) {
            if (!checkBridgeAvailable()) return;
            
            // 确认对话框
            const typeNames = {
                'webview': 'WebView缓存',
                'token': 'Token缓存',
                'advertisement': '广告缓存',
                'userdata': '用户数据缓存',
                'tempfiles': '临时文件缓存',
                'all': '所有缓存'
            };
            
            const typeName = typeNames[type] || type;
            if (!confirm(`确定要清除${typeName}吗？此操作不可撤销。`)) {
                return;
            }
            
            showResult('clearCacheResult', `正在清除${typeName}...`, 'info');
            
            try {
                const result = await window.jsBridgeHelper.sendMessage('clearCache', { type: type });
                console.log('清除缓存结果:', result);
                
                if (result && result.success) {
                    let displayText = `✅ ${result.message}\n\n`;
                    
                    if (result.details) {
                        displayText += `详细结果:\n`;
                        Object.entries(result.details).forEach(([key, success]) => {
                            displayText += `  ${typeNames[key] || key}: ${success ? '✅ 成功' : '❌ 失败'}\n`;
                        });
                    }
                    
                    showResult('clearCacheResult', displayText, 'success');
                } else {
                    showResult('clearCacheResult', `❌ 清除缓存失败: ${result?.error || result?.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('清除缓存失败:', error);
                showResult('clearCacheResult', `❌ 清除缓存失败: ${error.message}`, 'error');
            }
        }

        // 检查Bridge是否可用
        function checkBridgeAvailable() {
            const urlParams = new URLSearchParams(window.location.search);
            const isApp = urlParams.get('type') === 'app';
            const hasBridge = window.jsBridgeHelper !== undefined;
            
            if (!isApp || !hasBridge) {
                alert('当前环境不支持原生API调用，请在App中打开此页面');
                return false;
            }
            return true;
        }

        // 显示结果
        function showResult(elementId, text, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.style.display = 'block';
            
            // 清除之前的样式类
            element.classList.remove('error', 'success');
            
            // 添加新的样式类
            if (type === 'error') {
                element.classList.add('error');
            } else if (type === 'success') {
                element.classList.add('success');
            }
        }

        // 页面加载时检测环境
        window.onload = function() {
            checkEnvironment();
            
            // 如果在App环境中，自动获取一次缓存信息
            setTimeout(() => {
                if (checkBridgeAvailable()) {
                    getCacheInfo('all');
                }
            }, 1000);
        };
    </script>
</body>
</html>
