# Lima iOS 打包流程

> 完整的 iOS 应用打包和上传流程指南

## 📋 环境要求

- macOS 系统
- Xcode 已安装
- Flutter SDK 已配置
- CocoaPods 已安装
- 有效的 Apple Developer 账号

## 🚀 打包流程

### 步骤 1: 清理项目

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/yingka/lima-app-frontend

# 清理 Flutter 项目
flutter clean
```

### 步骤 2: 清理 iOS 缓存和依赖

```bash
# 删除 iOS 构建文件和依赖
rm -rf ios/build
rm -rf ios/.symlinks
rm -rf ios/Pods
rm -rf ios/Podfile.lock
```

### 步骤 3: 配置网络代理（解决 GitHub 连接问题）

```bash
# 设置环境变量代理
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890

# 设置 Git 代理
git config --global http.proxy http://127.0.0.1:7890
git config --global https.proxy http://127.0.0.1:7890
git config --global http.sslVerify false
```

> **注意**: 请根据你的代理软件调整端口号（7890）

### 步骤 4: 获取 Flutter 依赖

```bash
# 获取 Flutter 项目依赖
flutter pub get
```

### 步骤 5: 清理 CocoaPods 缓存

```bash
# 清理 CocoaPods 缓存
pod cache clean --all
```

### 步骤 6: 安装 iOS 依赖

```bash
# 进入 iOS 目录
cd ios

# 安装 CocoaPods 依赖
pod install --verbose
```

**如果上面的命令失败，尝试使用国内镜像：**

```bash
# 移除默认仓库
pod repo remove trunk

# 添加国内镜像
pod repo add trunk https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git

# 重新安装
pod install --verbose
```

**如果仍然失败，尝试跳过更新：**

```bash
# 跳过仓库更新直接安装
pod install --no-repo-update --verbose
```

### 步骤 7: 构建 Flutter iOS

```bash
# 返回项目根目录
cd ..

# 构建 iOS Release 版本
flutter build ios --release --no-codesign
```

### 步骤 8: 创建 Xcode Archive

```bash
# 进入 iOS 目录
cd ios

# 创建 Archive
xcodebuild -workspace Runner.xcworkspace \
           -scheme Runner \
           -configuration Release \
           -destination generic/platform=iOS \
           -archivePath build/Runner.xcarchive \
           archive
```

### 步骤 9: 导出 IPA 文件

```bash
# 使用 ExportOptions.plist 导出 IPA
xcodebuild -exportArchive \
           -archivePath build/Runner.xcarchive \
           -exportPath build/AppStore \
           -exportOptionsPlist ExportOptions.plist
```

### 步骤 10: 验证输出文件

```bash
# 查看导出的文件
ls -la build/AppStore/

# 检查 IPA 文件
find build/AppStore -name "*.ipa"
```

## 📤 上传到 App Store Connect

### 方法 1: 使用 Transporter 应用（推荐）

```bash
# 自动打开 Transporter 应用
open -a "Transporter" build/AppStore/*.ipa
```

然后在 Transporter 中：
1. 拖拽 IPA 文件到应用窗口
2. 点击"交付"按钮
3. 等待上传完成

### 方法 2: 使用命令行上传

```bash
# 使用 xcrun altool 上传（需要配置 Apple ID）
xcrun altool --upload-app \
             --type ios \
             --file build/AppStore/*.ipa \
             --username "<EMAIL>" \
             --password "@keychain:AC_PASSWORD"
```

> **注意**: 需要先在 Keychain 中保存 App-specific 密码

## 🧹 清理代理设置

完成打包后，清理代理配置：

```bash
# 清理 Git 代理设置
git config --global --unset http.proxy
git config --global --unset https.proxy

# 清理环境变量
unset https_proxy
unset http_proxy
```

## 📁 文件输出位置

成功完成后，文件位置：

- **Archive 文件**: `ios/build/Runner.xcarchive`
- **IPA 文件**: `ios/build/AppStore/Runner.ipa`
- **导出日志**: `ios/build/AppStore/Packaging.log`

## 🛠️ 故障排除

### Pod Install 失败

**问题**: 网络连接错误或依赖下载失败

**解决方案**:
```bash
# 1. 检查网络连接
ping github.com

# 2. 检查代理设置
echo $https_proxy

# 3. 使用手机热点
# 4. 尝试不同的镜像源
```

### Archive 创建失败

**问题**: 签名错误或配置问题

**解决方案**:
```bash
# 1. 清理 Xcode 缓存
rm -rf ~/Library/Developer/Xcode/DerivedData

# 2. 检查证书状态
security find-identity -v -p codesigning

# 3. 在 Xcode 中检查签名配置
```

### IPA 导出失败

**问题**: ExportOptions.plist 配置错误

**解决方案**:
```bash
# 1. 检查 ExportOptions.plist 文件
cat ios/ExportOptions.plist

# 2. 验证 Team ID 和 Bundle ID
# 3. 确认 Provisioning Profile 状态
```

### 上传失败

**问题**: 认证错误或网络问题

**解决方案**:
```bash
# 1. 验证 Apple ID 和密码
# 2. 检查应用状态在 App Store Connect
# 3. 确认应用版本号和构建号
```

## 📋 检查清单

打包前确认：

- [ ] 项目版本号已更新（pubspec.yaml）
- [ ] iOS 版本号已更新（ios/Runner/Info.plist）
- [ ] 证书和 Provisioning Profile 有效
- [ ] ExportOptions.plist 配置正确
- [ ] 网络连接正常
- [ ] 代理设置正确（如需要）

打包后确认：

- [ ] Archive 文件存在且完整
- [ ] IPA 文件生成成功
- [ ] 文件大小合理（通常 50-200MB）
- [ ] 上传到 App Store Connect 成功
- [ ] 在 App Store Connect 中可见构建

## 🔗 相关链接

- [App Store Connect](https://appstoreconnect.apple.com)
- [Apple Developer Portal](https://developer.apple.com)
- [Transporter 应用下载](https://apps.apple.com/app/transporter/id1450874784)
- [Flutter iOS 部署文档](https://docs.flutter.dev/deployment/ios)

## 📞 技术支持

如果遇到问题：

1. **检查日志**: 查看 `ios/build/AppStore/Packaging.log`
2. **验证配置**: 确认所有配置文件正确
3. **网络诊断**: 测试网络连接和代理设置
4. **联系支持**: 提供详细的错误信息和日志

---

**项目**: Lima iOS App  
**Bundle ID**: com.iot.lima.store  
**Team ID**: K8V69CSLJ6  
**最后更新**: 2025年1月15日