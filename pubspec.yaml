name: lima
description: Lima flutter project
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.13+28

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # getx - 更新到最新版本
  get: ^4.6.6
  # loading - 更新版本
  flutter_easyloading: ^3.0.5
  # http - 更新到最新版本
  dio: ^5.8.0
  cookie_jar: ^4.0.8
  dio_cookie_manager: ^3.2.0
  # 屏幕适配 - 更新版本
  flutter_screenutil: ^5.9.3
  # 权限
  permission_handler: ^11.4.0

  rxdart: ^0.28.0

  flutter_inappwebview: ^6.1.5

  url_launcher: ^6.3.1

  shared_preferences: ^2.3.5

  # 拍照/图片 相关 - 更新版本
  image_picker: ^1.1.2

  # 二维码扫码组件
#  qr_code_scanner: ^1.0.1
  mobile_scanner: ^3.5.7

  #高德地图
  amap_flutter_location: ^3.0.0

  # mqtt - 更新版本
  mqtt_client: ^10.5.0

  device_info_plus: ^11.4.0

  # 个推消息推送
  getuiflut: 0.2.23

  connectivity_plus: ^6.1.4



  #三方登录
  # sharesdk_plugin: ^1.3.10  # 暂时注释，改用阿里云DYPNS

  # 阿里云号码认证服务 (DYPNS) - 运营商一键登录
  # 官方文档：https://www.aliyun.com/product/dypns
  # 官方文档：https://pub.dev/documentation/ali_auth/latest/ali_auth/
  ali_auth:
    path: packages/ali_auth_custom

  # 本地文件系统 - 更新版本
  path_provider: ^2.1.5

  # 视频播放器 - 更新版本
  video_player: ^2.9.2
  #支付宝
  alipay_kit: ^6.0.0
  alipay_kit_ios: ^6.0.0
  #微信登录，分享，支付 - 更新版本
#  fluwx: ^4.4.9
  wechat_kit: ^6.0.2
  flutter_local_notifications: ^19.2.1
  
  image_gallery_saver:
    git:
      url: https://github.com/knottx/image_gallery_saver.git
      ref: knottx-latest
  # image_gallery_saver: ^2.0.3

  
  # tdesign_flutter: ^0.1.6
  package_info_plus: ^8.0.2
  install_plugin: ^2.1.0
  # install_plugin:
  #   path: ../install_plugin

  # 苹果登录
  sign_in_with_apple: ^7.0.1

  # MOB SDK - mobLink 已移除
  # moblink:

  # 蓝牙
  #  flutter_ble:
  #    git:
  #      url: ********************:appBaseGroup/senthink-flutter-ble.git
#  flutter_ble:
#    path: ../../third_project/senthink-flutter-ble

  flutter_blue_plus: ^1.35.3
  encrypt: ^5.0.3
  wakelock_plus: ^1.2.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/
    - assets/images/
    - assets/tab-bar/
    - assets/jsBridgeHelper.js

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
alipay_kit:
  scheme: alipay2018091961462479


wechat_kit:
  app_id: wxd92e6f7ab4454e9d
  universal_link: applinks:a5fd977ed5a03f35d04b8ecccff8e8b0.share2dlink.com/

