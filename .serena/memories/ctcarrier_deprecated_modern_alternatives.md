# CTCarrier废弃问题的现代替代方案

## 问题背景
`CTCarrier`在iOS 16+中已被Apple废弃，需要使用现代的替代API来获取运营商信息。

## Apple的废弃说明
- **废弃版本**: iOS 16.0+
- **原因**: 隐私保护和安全考虑
- **影响**: `CTCarrier`的所有属性都将返回空值或默认值

## 现代替代方案

### 1. Network Framework (iOS 12+)
**用途**: 检测网络连接类型和状态

```swift
import Network

@available(iOS 12.0, *)
private func detectCarrierAlternative(simInfo: inout [String: Any]) {
    let monitor = NWPathMonitor()
    let queue = DispatchQueue(label: "NetworkMonitor")
    
    monitor.pathUpdateHandler = { path in
        if path.usesInterfaceType(.cellular) {
            print("检测到蜂窝网络连接")
            simInfo["hasSimCard"] = true
            simInfo["networkType"] = "cellular"
        }
    }
    
    monitor.start(queue: queue)
    
    // 给一点时间让监控器工作
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        monitor.cancel()
    }
}
```

**优势**:
- ✅ 现代API，不会被废弃
- ✅ 可以检测网络类型（蜂窝/WiFi/有线）
- ✅ 实时网络状态监控

**限制**:
- ❌ 无法获取具体的运营商名称
- ❌ 无法获取MCC+MNC代码

### 2. SystemConfiguration Framework
**用途**: 网络可达性检测

```swift
import SystemConfiguration

private func detectNetworkStatus(simInfo: inout [String: Any]) {
    var flags = SCNetworkReachabilityFlags()
    var zeroAddress = sockaddr_in()
    zeroAddress.sin_len = UInt8(MemoryLayout<sockaddr_in>.size)
    zeroAddress.sin_family = sa_family_t(AF_INET)
    
    guard let defaultRouteReachability = withUnsafePointer(to: &zeroAddress, {
        $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
            SCNetworkReachabilityCreateWithAddress(nil, $0)
        }
    }) else {
        return
    }
    
    if SCNetworkReachabilityGetFlags(defaultRouteReachability, &flags) {
        let isWWAN = flags.contains(.isWWAN)
        
        if isWWAN {
            simInfo["hasSimCard"] = true
            simInfo["networkType"] = "cellular"
        }
    }
}
```

**优势**:
- ✅ 可以检测蜂窝网络连接
- ✅ 系统级API，稳定可靠

**限制**:
- ❌ 无法获取运营商详细信息
- ❌ API相对底层，使用复杂

### 3. 设备特征推断
**用途**: 通过设备信息推断用户地区和可能的运营商

```swift
private func detectCarrierByDeviceInfo(simInfo: inout [String: Any]) {
    let deviceName = UIDevice.current.name
    
    // 检查设备名称中是否包含中文字符
    if self.containsChinese(text: deviceName) {
        simInfo["likelyChineseUser"] = true
        
        if simInfo["carrierName"] as? String == "unknown" {
            simInfo["carrierName"] = "中国运营商"
            simInfo["mobileCountryCode"] = "460"
        }
    }
    
    // 检查地区设置
    if let locale = Locale.current.regionCode, locale == "CN" {
        simInfo["regionCode"] = "CN"
        simInfo["likelyChineseUser"] = true
    }
}

private func containsChinese(text: String) -> Bool {
    let chineseRange = text.range(of: "\\p{Han}", options: .regularExpression)
    return chineseRange != nil
}
```

**优势**:
- ✅ 可以推断用户地区
- ✅ 不依赖废弃的API

**限制**:
- ❌ 只能推断，不能精确检测
- ❌ 依赖用户设置，可能不准确

## 分层检测策略

### 1. 版本适配策略
```swift
private func detectCarrierInfoModern(simInfo: inout [String: Any]) {
    if #available(iOS 16.0, *) {
        // iOS 16+: 使用现代替代方案
        self.detectCarrierAlternative(simInfo: &simInfo)
    } else {
        // iOS 16以下: 仍可使用CTTelephonyNetworkInfo
        self.detectCarrierLegacy(simInfo: &simInfo)
    }
    
    // 补充检测方法
    self.detectNetworkStatus(simInfo: &simInfo)
    self.detectCarrierByDeviceInfo(simInfo: &simInfo)
}
```

### 2. 多重检测方法
1. **主要检测**: Network Framework或CTTelephonyNetworkInfo
2. **补充检测**: SystemConfiguration网络状态
3. **推断检测**: 设备特征和地区信息

### 3. 优雅降级
```swift
// 如果无法获取精确的运营商信息，提供合理的默认值
if simInfo["carrierName"] as? String == "unknown" {
    if let regionCode = Locale.current.regionCode, regionCode == "CN" {
        simInfo["carrierName"] = "中国运营商"
        simInfo["mobileCountryCode"] = "460"
    }
}
```

## Flutter层适配

### 1. 错误处理增强
```dart
/// 检查iOS权限和设备支持
Future<Map<String, dynamic>> checkIOSPermissions() async {
  try {
    const platform = MethodChannel('lima.carrier_detector/sim_card');
    final result = await platform.invokeMethod('checkSimCardStatus');
    
    if (result is Map) {
      // 检查是否为iOS 16+的限制
      if (result.containsKey('isIOS16Plus') && result['isIOS16Plus'] == true) {
        QLog('📱 iOS 16+: CTCarrier已废弃，使用替代方案');
      }
      
      final hasError = result.containsKey('error');
      final errorMessage = result['error'] as String? ?? '';
      
      return {
        'hasPermission': !hasError,
        'error': errorMessage,
        'isModernAPI': result['isIOS16Plus'] ?? false,
        'rawResult': result,
      };
    }
    
    return {'hasPermission': true, 'error': ''};
  } catch (e) {
    return {'hasPermission': false, 'error': e.toString()};
  }
}
```

### 2. 运营商检测逻辑调整
```dart
/// 检查运营商名称是否为支持的运营商
bool _isCarrierNameSupported(String carrierName) {
  if (carrierName.isEmpty || carrierName == 'unknown') {
    return false;
  }
  
  final name = carrierName.toLowerCase();
  
  // 添加对"中国运营商"的支持（iOS 16+推断结果）
  if (name.contains('中国运营商') || name.contains('china carrier')) {
    QLog('检测到推断的中国运营商');
    return true;
  }
  
  // 原有的运营商名称检测逻辑...
  return false;
}
```

## 最佳实践建议

### 1. 版本兼容性
- **iOS 16+**: 主要使用Network Framework和推断方法
- **iOS 16以下**: 继续使用CTTelephonyNetworkInfo
- **所有版本**: 提供多重检测和优雅降级

### 2. 用户体验
- **透明处理**: 用户无需知道API变化
- **功能保持**: 一键登录功能继续可用
- **错误处理**: 提供清晰的错误信息和解决建议

### 3. 隐私合规
- **最小权限**: 只获取必要的网络状态信息
- **用户同意**: 在需要时说明为什么需要网络信息
- **数据保护**: 不存储敏感的运营商信息

### 4. 测试策略
- **真机测试**: 在不同iOS版本的真机上测试
- **网络环境**: 测试不同的网络连接状态
- **边界情况**: 测试无SIM卡、飞行模式等情况

## 迁移时间表

### 短期 (立即)
- ✅ 添加iOS版本检测
- ✅ 实现Network Framework检测
- ✅ 添加设备特征推断

### 中期 (1-3个月)
- 🔄 收集用户反馈和检测准确性数据
- 🔄 优化推断算法
- 🔄 完善错误处理

### 长期 (6个月+)
- 📋 完全移除CTCarrier依赖
- 📋 基于用户数据优化检测逻辑
- 📋 探索新的运营商检测方法

## 相关文件
- `ios/Runner/AppDelegate.swift` - 主要实现文件
- `lib/utils/carrier_detector.dart` - Flutter检测逻辑
- `lib/pages/login/login_logic.dart` - 登录逻辑适配
- `ios/Runner/Info.plist` - 权限配置