# iOS一键登录"请确认用户协议"Toast修复

## 问题描述
iOS一键登录时会弹出"请确认用户协议"的toast提示，影响用户体验。

## 问题原因
在阿里云DYPNS SDK配置中，有两个配置导致了这个问题：
1. `logBtnToastHidden: false` - 允许显示登录按钮相关的toast
2. `privacyState: false` - 协议默认未勾选状态，与隐藏的复选框状态不一致

## 修复方案
修改`lib/services/carrier_login_service.dart`文件中的阿里云DYPNS配置：

### 修复1：隐藏登录按钮Toast
```dart
// 修复前
logBtnToastHidden: false,

// 修复后  
logBtnToastHidden: true,
```

### 修复2：设置协议默认勾选状态
```dart
// 修复前
privacyState: false,

// 修复后
privacyState: true,
```

## 配置说明
- `logBtnToastHidden: true` - 隐藏登录按钮相关的toast提示
- `privacyState: true` - 设置协议默认为已勾选状态
- `checkboxHidden: true` - 隐藏协议复选框（已有配置）
- `isHideToast: true` - 隐藏其他toast（已有配置）

## 相关配置一致性
- `_isAliAuthChecked = true` - 代码中的协议状态变量
- `privacyState: true` - SDK配置中的协议状态
- `checkboxHidden: true` - 隐藏复选框，用户无需手动勾选

## 测试验证
1. iOS设备上点击一键登录按钮
2. 确认不再弹出"请确认用户协议"的toast
3. 确认登录流程正常进行

## 影响范围
- ✅ iOS一键登录：不再显示协议toast
- ✅ Android一键登录：同样受益于此修复
- ✅ 第三方登录：不受影响，正常工作

## 相关文件
- `lib/services/carrier_login_service.dart` - 主要修复文件（第695行和第731行）