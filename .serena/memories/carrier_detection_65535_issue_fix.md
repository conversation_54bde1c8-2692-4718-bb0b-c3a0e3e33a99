# 运营商检测65535问题修复

## 问题描述
用户的中国电信SIM卡被检测为65535，这是一个异常值，表示运营商检测失败。

## 问题分析

### 65535的含义
- **数值含义**：65535是16位无符号整数的最大值（2^16 - 1）
- **在电信API中**：通常用作错误标识或无效值
- **常见原因**：网络未注册、SIM卡读取失败、权限不足

### 可能的原因
1. **网络状态问题**：
   - 设备未注册到网络
   - 网络信号弱或不稳定
   - 漫游状态下的网络问题

2. **SIM卡问题**：
   - SIM卡读取失败
   - SIM卡损坏或接触不良
   - SIM卡未激活

3. **权限问题**：
   - 缺少READ_PHONE_STATE权限
   - Android系统权限限制

4. **API问题**：
   - TelephonyManager API返回无效值
   - 设备不支持该API

## 解决方案

### 1. Android原生代码修复

#### 添加运营商代码验证
```java
/**
 * 验证和清理运营商代码
 * 过滤掉无效值如65535等
 */
private String validateAndCleanOperatorCode(String operatorCode) {
    if (operatorCode == null || operatorCode.isEmpty()) {
        return "";
    }
    
    // 移除空格和特殊字符
    String cleaned = operatorCode.trim();
    
    // 检查是否为无效值
    if (cleaned.equals("65535") || 
        cleaned.equals("0") || 
        cleaned.equals("00000") ||
        cleaned.equals("000000") ||
        cleaned.length() < 5 ||
        cleaned.length() > 6) {
        System.out.println("⚠️ 检测到无效的运营商代码: " + cleaned);
        return "";
    }
    
    // 检查是否为纯数字
    if (!cleaned.matches("\\d+")) {
        System.out.println("⚠️ 运营商代码包含非数字字符: " + cleaned);
        return "";
    }
    
    // 检查是否为中国的MCC代码（460）
    if (!cleaned.startsWith("460")) {
        System.out.println("⚠️ 非中国运营商代码: " + cleaned);
        return "";
    }
    
    System.out.println("✅ 有效的运营商代码: " + cleaned);
    return cleaned;
}
```

#### 增强的运营商代码获取逻辑
```java
// 获取网络运营商代码 (MCC+MNC)
networkOperator = telephonyManager.getNetworkOperator();
if (networkOperator == null) networkOperator = "";
System.out.println("📶 网络运营商代码: " + networkOperator);

// 获取SIM卡运营商代码 (MCC+MNC)
simOperator = telephonyManager.getSimOperator();
if (simOperator == null) simOperator = "";
System.out.println("📱 SIM卡运营商代码: " + simOperator);

// 验证和清理运营商代码
String validNetworkOperator = validateAndCleanOperatorCode(networkOperator);
String validSimOperator = validateAndCleanOperatorCode(simOperator);

// 优先使用有效的网络运营商代码，如果没有则使用SIM卡运营商代码
if (!validNetworkOperator.isEmpty()) {
    mccMnc = validNetworkOperator;
    System.out.println("🎯 使用网络运营商代码: " + mccMnc);
} else if (!validSimOperator.isEmpty()) {
    mccMnc = validSimOperator;
    System.out.println("🎯 使用SIM卡运营商代码: " + mccMnc);
} else {
    mccMnc = "";
    System.out.println("❌ 无有效的运营商代码");
}
```

### 2. Flutter代码增强

#### 扩展支持的运营商代码
```dart
static const List<String> _supportedCarriers = [
  // 中国移动
  '46000', '46002', '46004', '46007', '46008', '46020',
  // 中国联通  
  '46001', '46006', '46009',
  // 中国电信
  '46003', '46005', '46011', '46012',  // 添加46012
];
```

#### 调试方法
```dart
/// 调试运营商检测问题
Future<Map<String, dynamic>> debugCarrierDetection() async {
  try {
    const platform = MethodChannel('lima.carrier_detector/sim_card');
    final result = await platform.invokeMethod('checkSimCardStatus');
    
    if (result is Map) {
      final mccMnc = result['mccMnc'] as String? ?? '';
      final carrierName = result['carrierName'] as String? ?? 'unknown';
      final networkOperator = result['networkOperator'] as String? ?? '';
      final simOperator = result['simOperator'] as String? ?? '';
      
      // 检查是否为65535等无效值
      if (mccMnc == '65535' || networkOperator == '65535' || simOperator == '65535') {
        QLog('❌ 检测到无效值65535，这通常表示:');
        QLog('   1. 网络未注册或信号弱');
        QLog('   2. SIM卡读取失败');
        QLog('   3. 权限不足');
        QLog('   4. 设备不支持该API');
      }
      
      return {
        'mccMnc': mccMnc,
        'carrierName': carrierName,
        'networkOperator': networkOperator,
        'simOperator': simOperator,
        'isSupported': _supportedCarriers.contains(mccMnc),
        'nameSupported': _isCarrierNameSupported(carrierName),
        'rawResult': result,
      };
    }
    
    return {'error': '无法获取检测结果'};
  } catch (e) {
    return {'error': e.toString()};
  }
}
```

### 3. 权限检查

#### Android权限确认
确保`android/app/src/main/AndroidManifest.xml`包含：
```xml
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 4. 中国电信特殊情况处理

#### 中国电信的MCC+MNC代码
- **46003**：中国电信主要代码
- **46005**：中国电信CDMA
- **46011**：中国电信FDD-LTE
- **46012**：中国电信（新增）

#### 运营商名称检测增强
```dart
// 中国电信的各种名称
if (name.contains('电信') || 
    name.contains('telecom') || 
    name.contains('china telecom') ||
    name.contains('ctcc') ||
    name.contains('ct') ||  // 新增
    name.contains('中国电信')) {  // 新增
  QLog('检测到中国电信: $carrierName');
  return true;
}
```

## 调试步骤

### 1. 使用调试方法
在登录页面调用：
```dart
await loginLogic.debugCarrierDetection();
```

### 2. 查看详细日志
```
📊 调试结果:
  mccMnc: 65535
  carrierName: 中国电信
  networkOperator: 65535
  simOperator: 46003
  isSupported: false
  nameSupported: true
```

### 3. 分析结果
- 如果`mccMnc`为65535但`carrierName`正确，说明名称检测可以作为备用
- 如果`simOperator`有效但`networkOperator`为65535，说明网络注册有问题
- 如果两者都为65535，可能是权限或硬件问题

## 常见解决方案

### 1. 网络问题
- 重启设备
- 切换飞行模式
- 检查网络信号
- 尝试不同位置

### 2. SIM卡问题
- 重新插拔SIM卡
- 清洁SIM卡触点
- 尝试其他设备

### 3. 权限问题
- 检查应用权限设置
- 重新安装应用
- 手动授予电话权限

### 4. 代码层面
- 使用运营商名称作为备用检测
- 添加更多中国电信的MCC+MNC代码
- 实现更宽松的检测逻辑

## 最终备用方案

如果所有检测都失败，可以：
1. **使用运营商名称检测**：通过`carrierName`判断
2. **用户手动选择**：让用户选择自己的运营商
3. **默认允许**：对于中国设备默认允许一键登录
4. **网络检测**：通过网络连接类型推断

## 相关文件
- `android/app/src/main/java/com/lima/scooter/MainActivity.java` - Android原生检测
- `lib/utils/carrier_detector.dart` - Flutter检测逻辑
- `lib/pages/login/login_logic.dart` - 调试方法
- `android/app/src/main/AndroidManifest.xml` - 权限配置