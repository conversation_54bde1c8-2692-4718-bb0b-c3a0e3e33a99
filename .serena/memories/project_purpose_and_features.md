# Lima App Frontend - 项目目的和功能特性

## 项目目的
Lima App Frontend是立马科技开发的移动应用前端，主要用于：
- **车辆管理**: 智能电动车的远程控制和监控
- **用户服务**: 提供完整的用户服务体验
- **社区互动**: 用户社区和内容分享
- **商业服务**: 相关商业服务和功能

## 核心功能模块

### 1. 用户认证系统
- **多种登录方式**:
  - 手机号+验证码登录
  - 微信登录
  - 苹果登录（iOS）
  - 运营商一键登录（阿里云DYPNS）
- **安全特性**:
  - Token自动管理
  - 用户信息加密存储
  - 登录状态持久化

### 2. 车辆控制模块
- **远程控制功能**:
  - 车辆解锁/上锁
  - 引擎启动/关闭
  - 寻车铃响
  - 感应解锁
  - 坐桶盖控制
  - 助力推行
- **车辆状态监控**:
  - 电池电量显示
  - 剩余续航里程
  - 连接状态监控
  - 里程统计

### 3. 蓝牙通信模块
- **设备连接**:
  - 蓝牙设备扫描和连接
  - 多设备管理
  - 连接状态监控
- **数据传输**:
  - 实时数据传输
  - 命令发送和响应
  - OTA固件升级
- **安全通信**:
  - 数据加密传输
  - 设备配对验证

### 4. WebView混合开发
- **H5页面集成**:
  - 社区页面（H5）
  - 个人中心（H5）
  - 服务页面
- **原生桥接**:
  - JavaScript Bridge通信
  - 原生功能调用
  - 数据双向传递
- **页面管理**:
  - 页面缓存和刷新
  - 导航状态管理
  - Hash路由支持

### 5. 第三方服务集成
- **支付服务**:
  - 支付宝支付
  - 微信支付
- **地图服务**:
  - 高德地图定位
  - 导航功能
- **推送服务**:
  - 个推消息推送
  - 本地通知
- **分享功能**:
  - 微信分享
  - 其他社交平台分享

## 技术特性

### 1. 跨平台开发
- **Flutter框架**: 一套代码支持iOS和Android
- **原生性能**: 接近原生应用的性能表现
- **统一UI**: 跨平台一致的用户体验

### 2. 状态管理
- **GetX框架**: 轻量级状态管理
- **响应式编程**: 数据变化自动更新UI
- **依赖注入**: 便于测试和维护

### 3. 网络通信
- **Dio框架**: 强大的HTTP客户端
- **拦截器**: 统一的请求和响应处理
- **错误处理**: 完善的网络错误处理机制

### 4. 本地存储
- **SharedPreferences**: 轻量级键值存储
- **Token管理**: 安全的认证信息存储
- **缓存机制**: 提升应用性能

## 业务特色

### 1. 智能车联网
- 专为电动车设计的控制界面
- 实时车辆状态监控
- 智能化的车辆管理功能

### 2. 混合开发架构
- 原生性能与H5灵活性结合
- 快速迭代的业务功能
- 统一的用户体验

### 3. 多端数据同步
- 云端数据同步
- 离线数据缓存
- 实时状态更新

### 4. 安全可靠
- 多层次的安全验证
- 加密数据传输
- 隐私保护机制

## 目标用户
- **电动车用户**: 需要远程控制和监控车辆的用户
- **科技爱好者**: 喜欢智能化产品的用户群体
- **城市通勤者**: 日常使用电动车通勤的用户
- **年轻用户**: 对移动应用有较高要求的年轻群体