# Lima App Frontend - 代码风格和约定

## 代码风格配置
项目使用`package:flutter_lints/flutter.yaml`作为基础lint规则，配置在`analysis_options.yaml`中。

## 命名约定

### 文件命名
- **页面文件**: `page_name_view.dart` (UI) + `page_name_logic.dart` (逻辑)
- **组件文件**: `component_name.dart`
- **工具类**: `utility_name.dart`
- **服务类**: `service_name_service.dart`

### 类命名
- **页面类**: `PageNamePage` (StatefulWidget)
- **逻辑类**: `PageNameLogic` (GetxController)
- **组件类**: `ComponentName` (Widget)
- **服务类**: `ServiceNameService`

### 变量命名
- **私有变量**: `_variableName`
- **常量**: `CONSTANT_NAME` (全大写)
- **配置常量**: `configName` (驼峰命名)

## 项目架构模式

### GetX架构
```dart
// 页面结构
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<LoginLogic>(
      init: LoginLogic(),
      builder: (logic) => Scaffold(...)
    );
  }
}

// 逻辑控制器
class LoginLogic extends GetxController {
  @override
  void onInit() {
    super.onInit();
    // 初始化逻辑
  }
}
```

### 状态管理
- 使用GetX进行状态管理
- 页面逻辑与UI分离
- 响应式变量使用`.obs`后缀

## 代码组织

### 导入顺序
1. Dart核心库
2. Flutter框架库
3. 第三方包
4. 项目内部文件

```dart
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../common/config.dart';
import '../utils/q_log.dart';
```

### 类结构顺序
1. 静态常量
2. 实例变量
3. 构造函数
4. 生命周期方法
5. 公共方法
6. 私有方法

## 注释规范

### 类注释
```dart
/// 用户登录页面
/// 
/// 支持多种登录方式：
/// - 手机号+验证码
/// - 微信登录
/// - 苹果登录
class LoginPage extends StatelessWidget {
```

### 方法注释
```dart
/// 发送验证码
/// 
/// [phone] 手机号码
/// [type] 验证码类型，默认为'login'
/// 
/// 返回发送结果
Future<bool> sendVerificationCode(String phone, {String type = 'login'}) async {
```

### 行内注释
```dart
// TODO: 需要优化网络请求性能
// FIXME: 修复iOS上的显示问题
// NOTE: 这里使用了特殊的处理逻辑
```

## 错误处理

### 网络请求错误
```dart
try {
  final response = await API.login(phone, code);
  // 处理成功响应
} catch (e) {
  QLog('登录失败: $e');
  EasyLoading.showError('登录失败，请重试');
}
```

### 空值检查
```dart
// 使用空值检查
final token = await Global.getToken();
if (token?.isNotEmpty == true) {
  // 处理有效token
}
```

## 资源管理

### 颜色定义
```dart
// 在 res/colors.dart 中定义
class AppColors {
  static const Color primary = Color(0xFFC70E2D);
  static const Color background = Color(0xFFF8F8FA);
}
```

### 图标定义
```dart
// 在 res/icons.dart 中定义
class AppIcons {
  static const String wechat = 'assets/images/wechat.png';
  static const String apple = 'assets/images/apple.png';
}
```

## 性能优化

### Widget优化
- 使用`const`构造函数
- 避免在`build`方法中创建复杂对象
- 合理使用`GetBuilder`和`Obx`

### 内存管理
- 及时释放控制器和订阅
- 在`onClose`中清理资源

```dart
@override
void onClose() {
  _timer?.cancel();
  _subscription?.cancel();
  super.onClose();
}
```