# SIM卡检测功能实现

## 功能描述
添加SIM卡检测功能，当未检测到SIM卡时隐藏一键登录按钮，提升用户体验。

## 实现方案

### 1. Flutter层修改
**文件**: `lib/utils/carrier_detector.dart`

#### 新增方法
- `_checkSimCardStatus()` - 检查SIM卡状态的主方法
- `_checkAndroidSimCard()` - Android平台SIM卡检测
- `_checkIOSSimCard()` - iOS平台SIM卡检测  
- `_checkSimCardFallback()` - 备用检测方法（使用网络状态推断）

#### 修改的方法
- `isCarrierLoginSupported()` - 在运营商检测前先检查SIM卡状态

#### 新增依赖
```dart
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
```

### 2. Android原生实现
**文件**: `android/app/src/main/java/com/lima/scooter/MainActivity.java`

#### 功能特性
- 使用`TelephonyManager`检测SIM卡状态
- 支持多种SIM卡状态检测（READY、ABSENT、PIN_REQUIRED等）
- 获取运营商名称信息
- 方法通道：`lima.carrier_detector/sim_card`

#### 返回数据格式
```json
{
  "hasSimCard": true/false,
  "simState": "READY/ABSENT/NOT_READY/...",
  "carrierName": "中国移动/中国联通/..."
}
```

#### 所需权限
- `READ_PHONE_STATE` - 已在AndroidManifest.xml中配置

### 3. iOS原生实现
**文件**: `ios/Runner/AppDelegate.swift`

#### 功能特性
- 使用`CoreTelephony`框架检测SIM卡状态
- 支持iOS 12+的多SIM卡检测
- 向下兼容iOS 12以下版本
- 获取运营商名称、MCC、MNC信息

#### 返回数据格式
```json
{
  "hasSimCard": true/false,
  "carrierName": "中国移动/China Mobile/...",
  "mobileCountryCode": "460",
  "mobileNetworkCode": "00"
}
```

#### 新增框架依赖
- `CoreTelephony` - 已在AppDelegate.swift中导入

## 检测逻辑

### 检测流程
1. **平台检查** - 确认是Android或iOS平台
2. **SIM卡检测** - 调用原生方法检测SIM卡状态
3. **运营商检测** - 检查是否为支持的运营商
4. **最终判断** - 综合判断是否显示一键登录按钮

### 检测条件
- **Android**: SIM卡状态必须为`READY`
- **iOS**: 必须检测到有效的运营商信息
- **备用方案**: 通过移动网络连接状态推断

### 错误处理
- 原生方法调用失败时使用备用检测方法
- 备用方法失败时默认认为有SIM卡（避免误判）
- 详细的日志记录便于调试

## 用户体验改进

### 显示逻辑
- ✅ **有SIM卡 + 支持的运营商**: 显示一键登录按钮
- ❌ **无SIM卡**: 隐藏一键登录按钮
- ❌ **有SIM卡但不支持的运营商**: 隐藏一键登录按钮

### 日志输出
- 详细的SIM卡状态信息
- 运营商检测结果
- 错误信息和备用方案执行情况

## 测试场景

### 正常场景
1. 有SIM卡且为三大运营商 - 显示按钮
2. 有SIM卡但非三大运营商 - 隐藏按钮
3. 无SIM卡 - 隐藏按钮

### 异常场景
1. SIM卡状态异常（PIN锁定等）- 隐藏按钮
2. 权限被拒绝 - 使用备用方案
3. 原生方法调用失败 - 使用备用方案

## 相关文件
- `lib/utils/carrier_detector.dart` - 主要逻辑
- `android/app/src/main/java/com/lima/scooter/MainActivity.java` - Android实现
- `ios/Runner/AppDelegate.swift` - iOS实现
- `android/app/src/main/AndroidManifest.xml` - Android权限配置