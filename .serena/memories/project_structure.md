# Lima App Frontend - 项目结构

## 项目概述
Lima App Frontend是一个基于Flutter的移动应用项目，主要用于立马科技的车辆管理和用户服务。

## 技术栈
- **框架**: Flutter 3.0+ (Dart SDK >=3.0.0 <4.0.0)
- **状态管理**: GetX (get: ^4.6.6)
- **网络请求**: Dio (dio: ^5.8.0)
- **UI组件**: Material Design + 自定义组件
- **屏幕适配**: flutter_screenutil: ^5.9.3
- **加载提示**: flutter_easyloading: ^3.0.5

## 核心目录结构

```
lib/
├── main.dart                    # 应用入口
├── global.dart                  # 全局配置和初始化
├── routers/                     # 路由配置
│   └── app_router.dart
├── pages/                       # 页面模块
│   ├── login/                   # 登录模块
│   ├── main_tab/               # 主Tab页面
│   ├── vehicle/                # 车辆控制
│   ├── profile/                # 个人中心
│   ├── community/              # 社区
│   ├── service/                # 服务
│   ├── verification/           # 验证码页面
│   └── webview/                # WebView页面
├── common/                      # 公共模块
│   ├── config.dart             # 配置文件
│   ├── jsapi.dart              # JS桥接API
│   ├── bridge_controller.dart   # 桥接控制器
│   └── ble/                    # 蓝牙模块
├── components/                  # 通用组件
├── utils/                       # 工具类
│   ├── http/                   # 网络请求
│   ├── jsbridge/               # JS桥接
│   └── q_log.dart              # 日志工具
├── res/                        # 资源文件
│   ├── colors.dart             # 颜色定义
│   └── icons.dart              # 图标定义
└── services/                   # 服务层
    ├── carrier_login_service.dart
    ├── third_party_login_service.dart
    └── api_service.dart
```

## 特殊目录
- **android/**: Android平台配置
- **ios/**: iOS平台配置
- **assets/**: 静态资源文件
- **docs/**: 项目文档
- **test/**: 测试文件