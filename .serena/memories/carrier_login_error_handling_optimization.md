# 运营商登录错误处理优化

## 问题背景
用户在使用运营商一键登录时遇到错误码`600008`，提示"蜂窝网络未开启！用户开启移动网络后重试"，需要优化错误处理，提供更友好的用户体验。

## 错误码分析

### 常见运营商登录错误码
- **600008**: 蜂窝网络未开启 - 用户需要开启移动数据
- **600009**: 当前运营商不支持 - 运营商不在支持列表中
- **600010**: 网络连接异常 - 网络问题
- **600011**: 获取运营商配置失败 - 配置或网络问题
- **600012**: 预登录失败 - SDK预登录阶段失败
- **600013**: 用户取消登录 - 用户主动取消
- **600014**: 运营商授权失败 - 授权过程失败
- **600015**: 获取手机号失败 - 无法获取手机号
- **600016**: 登录超时 - 操作超时
- **600017**: SDK初始化失败 - SDK初始化问题

## 优化方案

### 1. 服务层错误处理
**文件**: `lib/services/carrier_login_service.dart`

#### 新增错误处理方法
```dart
/// 处理运营商登录错误
void _handleCarrierLoginError(String? code, String? msg) {
  String userFriendlyMessage = '';
  String actionSuggestion = '';
  
  switch (code) {
    case '600008':
      userFriendlyMessage = '需要开启移动网络';
      actionSuggestion = '请开启手机的移动数据网络后重试，或使用其他登录方式';
      break;
    case '600009':
      userFriendlyMessage = '当前运营商不支持';
      actionSuggestion = '您的运营商暂不支持一键登录，请使用手机号验证码登录';
      break;
    // ... 其他错误码处理
  }
  
  // 显示用户友好的错误提示
  if (code != '600013') { // 用户取消不显示错误
    EasyLoading.showError(
      '$userFriendlyMessage\n$actionSuggestion',
      duration: Duration(seconds: 4),
    );
  }
}
```

#### 集成到监听器
```dart
} else if (code?.startsWith('6') == true && code != '600001') {
  // 其他错误（600001是唤起授权页成功，不是错误）
  QLog('运营商登录错误，错误码: $code, 错误信息: $msg');
  
  // 针对特定错误码提供友好的用户提示
  _handleCarrierLoginError(code, msg);
  
  if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
    _loginCompleter!.complete(eventMap);
  }
}
```

### 2. 控制层错误处理
**文件**: `lib/pages/login/login_logic.dart`

#### 增强的运营商登录方法
```dart
/// 运营商一键登录（简化版本）
Future<void> carrierLogin() async {
  try {
    QLog('开始运营商一键登录...');
    EasyLoading.show(status: '正在初始化...');

    // 1. 检查运营商登录可用性
    final availability = await CarrierLoginService.instance.checkAvailability();
    if (!availability.isAvailable) {
      EasyLoading.dismiss();
      _handleCarrierLoginUnavailable(availability);
      return;
    }

    // 2. 初始化阿里云DYPNS SDK
    final initSuccess = await CarrierLoginService.instance.initialize();
    if (!initSuccess) {
      EasyLoading.dismiss();
      _showCarrierLoginFailedDialog('初始化失败', '请检查网络连接后重试');
      return;
    }

    QLog('✅ 运营商一键登录初始化成功');
    EasyLoading.dismiss();

  } catch (e) {
    QLog('运营商一键登录失败: $e');
    EasyLoading.dismiss();
    _showCarrierLoginFailedDialog('一键登录失败', '网络异常，请重试或使用其他登录方式');
  }
}
```

#### 智能错误处理
```dart
/// 处理运营商登录不可用的情况
void _handleCarrierLoginUnavailable(CarrierAvailability availability) {
  String title = '一键登录不可用';
  String message = '';
  
  switch (availability.errorCode) {
    case 'CONFIG_INVALID':
      message = '配置异常，请联系客服';
      break;
    case 'INIT_FAILED':
      message = '初始化失败，请重试或使用手机号登录';
      break;
    case 'CHECK_FAILED':
      message = '检查失败，请使用手机号验证码登录';
      break;
    default:
      message = availability.errorMessage ?? '当前环境不支持一键登录';
      break;
  }
  
  _showCarrierLoginFailedDialog(title, message);
}
```

#### 用户友好的对话框
```dart
/// 显示运营商登录失败对话框
void _showCarrierLoginFailedDialog(String title, String message) {
  Get.dialog(
    AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () {
            Get.back();
            // 自动滚动到手机号登录区域
            _scrollToPhoneLogin();
          },
          child: Text('使用手机号登录'),
        ),
        TextButton(
          onPressed: () => Get.back(),
          child: Text('知道了'),
        ),
      ],
    ),
  );
}
```

## 错误处理策略

### 1. 分层处理
- **服务层**: 捕获和分类错误，提供统一的错误信息
- **控制层**: 处理业务逻辑错误，提供用户交互
- **视图层**: 显示用户友好的错误提示

### 2. 用户体验优化
- **即时反馈**: 立即显示错误原因和解决方案
- **操作引导**: 提供明确的下一步操作建议
- **备选方案**: 自动引导到其他可用的登录方式

### 3. 错误信息本地化
- **简化术语**: 将技术错误转换为用户易懂的语言
- **操作建议**: 提供具体的解决步骤
- **情感化表达**: 使用友好的语调减少用户挫败感

## 具体错误场景处理

### 1. 蜂窝网络未开启 (600008)
**用户看到的信息**:
```
需要开启移动网络
请开启手机的移动数据网络后重试，或使用其他登录方式
```

**处理流程**:
1. 检测到600008错误码
2. 显示友好的错误提示
3. 提供操作建议（开启移动网络）
4. 提供备选方案（其他登录方式）

### 2. 运营商不支持 (600009)
**用户看到的信息**:
```
当前运营商不支持
您的运营商暂不支持一键登录，请使用手机号验证码登录
```

**处理流程**:
1. 检测到600009错误码
2. 说明运营商限制
3. 直接引导到手机号登录

### 3. 用户取消 (600013)
**处理方式**:
- 不显示错误提示
- 静默处理，用户可以选择其他登录方式

### 4. 网络异常 (600010, 600011, 600016)
**用户看到的信息**:
```
网络连接异常
请检查网络连接后重试
```

**处理流程**:
1. 检测网络相关错误
2. 提示检查网络
3. 提供重试选项

## 用户引导流程

### 1. 错误发生时
```
运营商登录失败
    ↓
显示具体错误原因
    ↓
提供解决方案
    ↓
引导到备选登录方式
```

### 2. 对话框交互
```
[错误标题]
[错误描述和建议]

[使用手机号登录] [知道了]
```

### 3. 自动引导
- 点击"使用手机号登录"自动滚动到相应区域
- 减少用户寻找的时间

## 测试场景

### 1. 网络环境测试
- ✅ WiFi环境下的运营商登录
- ✅ 移动网络关闭时的错误处理
- ✅ 网络信号弱时的超时处理
- ✅ 网络切换时的异常处理

### 2. 运营商兼容性测试
- ✅ 中国移动用户
- ✅ 中国联通用户
- ✅ 中国电信用户
- ✅ 虚拟运营商用户
- ✅ 国外运营商用户

### 3. 用户操作测试
- ✅ 用户主动取消登录
- ✅ 登录过程中切换应用
- ✅ 登录超时处理
- ✅ 重复点击登录按钮

### 4. 错误恢复测试
- ✅ 错误后重试机制
- ✅ 切换到备选登录方式
- ✅ 网络恢复后的重试
- ✅ 应用重启后的状态恢复

## 监控和分析

### 1. 错误统计
```dart
// 记录错误码分布
QLog('运营商登录错误统计:');
QLog('  错误码: $code');
QLog('  错误频率: ${errorCount[code]}');
QLog('  用户操作: $userAction');
```

### 2. 用户行为分析
- 错误发生后的用户选择
- 备选登录方式的使用率
- 重试成功率
- 用户流失率

### 3. 优化建议
- 根据错误分布优化提示文案
- 根据用户行为调整引导流程
- 根据成功率优化技术方案

## 相关文件
- `lib/services/carrier_login_service.dart` - 服务层错误处理
- `lib/pages/login/login_logic.dart` - 控制层错误处理
- `lib/pages/login/login_view.dart` - 视图层错误显示

## 注意事项
1. **错误信息一致性**: 确保不同层级的错误信息保持一致
2. **用户体验**: 避免技术术语，使用用户友好的语言
3. **操作引导**: 提供明确的下一步操作建议
4. **性能考虑**: 错误处理不应影响应用性能
5. **测试覆盖**: 确保所有错误场景都有对应的测试用例