# iOS运营商信息访问权限配置

## 问题描述
iOS需要特定的权限和配置才能访问运营商信息（CTTelephonyNetworkInfo），否则会返回nil或空值。

## iOS权限要求

### 1. Info.plist权限描述
**文件**: `ios/Runner/Info.plist`

```xml
<key>NSCellularDataUsageDescription</key>
<string>立马科技需要访问蜂窝网络信息以提供一键登录服务</string>
<key>NSNetworkUsageDescription</key>
<string>立马科技需要访问网络信息以检测运营商并提供一键登录服务</string>
```

### 2. Entitlements配置
**文件**: `ios/Runner/Runner.entitlements`

```xml
<key>com.apple.developer.networking.networkextension</key>
<array>
    <string>packet-tunnel-provider</string>
</array>
```

### 3. 权限检查代码
**文件**: `ios/Runner/AppDelegate.swift`

```swift
/// 检查运营商信息访问权限
private func checkCarrierInfoPermissions() -> Bool {
    // 检查是否在模拟器中运行
    #if targetEnvironment(simulator)
    print("⚠️ 在模拟器中运行，运营商信息可能不可用")
    return false
    #else
    
    // 检查CTTelephonyNetworkInfo是否可用
    let networkInfo = CTTelephonyNetworkInfo()
    
    if #available(iOS 12.0, *) {
        // iOS 12+ 检查多SIM卡支持
        let carriers = networkInfo.serviceSubscriberCellularProviders
        if carriers == nil {
            print("❌ serviceSubscriberCellularProviders 不可用")
            return false
        }
        print("✅ serviceSubscriberCellularProviders 可用")
    } else {
        // iOS 12以下检查单SIM卡支持
        let carrier = networkInfo.subscriberCellularProvider
        if carrier == nil {
            print("❌ subscriberCellularProvider 不可用")
            return false
        }
        print("✅ subscriberCellularProvider 可用")
    }
    
    return true
    #endif
}
```

## iOS限制和特殊情况

### 1. 模拟器限制
- **问题**: iOS模拟器无法访问真实的运营商信息
- **表现**: `CTTelephonyNetworkInfo`返回nil
- **解决**: 在真实设备上测试

### 2. 设备类型限制
- **WiFi版iPad**: 不支持蜂窝网络，无运营商信息
- **iPod Touch**: 不支持蜂窝网络
- **iPhone**: 支持运营商信息访问

### 3. iOS版本差异
- **iOS 12+**: 使用`serviceSubscriberCellularProviders`（支持多SIM卡）
- **iOS 12以下**: 使用`subscriberCellularProvider`（单SIM卡）

### 4. 权限时机
- **应用启动**: 权限在应用启动时自动检查
- **无需用户授权**: 运营商信息访问不需要用户明确授权
- **系统限制**: 某些iOS版本可能有额外限制

## Flutter层权限检查

### 1. 权限检查方法
```dart
/// 检查iOS权限和设备支持
Future<Map<String, dynamic>> checkIOSPermissions() async {
  try {
    QLog('🔍 检查iOS运营商信息访问权限...');
    const platform = MethodChannel('lima.carrier_detector/sim_card');
    final result = await platform.invokeMethod('checkSimCardStatus');
    
    if (result is Map) {
      final hasError = result.containsKey('error');
      final errorMessage = result['error'] as String? ?? '';
      
      return {
        'hasPermission': !hasError,
        'error': errorMessage,
        'rawResult': result,
      };
    }
    
    return {'hasPermission': true, 'error': ''};
  } catch (e) {
    QLog('❌ iOS权限检查失败: $e');
    return {'hasPermission': false, 'error': e.toString()};
  }
}
```

### 2. 登录逻辑中的权限检查
```dart
// 如果是iOS，检查权限
if (Platform.isIOS) {
  QLog('📱 检查iOS运营商信息访问权限...');
  final permissionResult = await CarrierDetector.instance.checkIOSPermissions();
  
  if (!permissionResult['hasPermission']) {
    QLog('❌ iOS权限检查失败: ${permissionResult['error']}');
    QLog('💡 可能的原因:');
    QLog('   1. 在模拟器中运行（模拟器不支持运营商信息）');
    QLog('   2. 设备不支持蜂窝网络（如WiFi版iPad）');
    QLog('   3. 缺少必要的权限配置');
    QLog('   4. iOS系统限制');
    
    // iOS权限失败时，仍然显示按钮，让用户可以尝试
    isCarrierLoginSupported.value = true;
    return;
  } else {
    QLog('✅ iOS权限检查通过');
  }
}
```

## 常见问题和解决方案

### 1. 模拟器中测试
**问题**: 模拟器中运营商信息始终为nil
**解决方案**:
- 在真实设备上测试
- 模拟器中使用模拟数据
- 添加模拟器检测逻辑

### 2. WiFi版iPad
**问题**: WiFi版iPad没有蜂窝网络功能
**解决方案**:
- 检测设备型号
- 对WiFi版设备隐藏运营商登录
- 提供替代登录方式

### 3. 权限被拒绝
**问题**: 系统拒绝访问运营商信息
**解决方案**:
- 检查Info.plist权限描述
- 确认entitlements配置
- 重新安装应用

### 4. iOS版本兼容性
**问题**: 不同iOS版本API差异
**解决方案**:
- 使用`@available`检查
- 为不同版本提供不同实现
- 向后兼容处理

## 调试方法

### 1. 检查设备信息
```swift
/// 获取详细的设备和网络信息（用于调试）
private func getDetailedDeviceInfo() -> [String: Any] {
    var deviceInfo: [String: Any] = [:]
    
    // 设备基本信息
    deviceInfo["deviceModel"] = UIDevice.current.model
    deviceInfo["systemName"] = UIDevice.current.systemName
    deviceInfo["systemVersion"] = UIDevice.current.systemVersion
    deviceInfo["deviceName"] = UIDevice.current.name
    
    // 检查是否为模拟器
    #if targetEnvironment(simulator)
    deviceInfo["isSimulator"] = true
    #else
    deviceInfo["isSimulator"] = false
    #endif
    
    return deviceInfo
}
```

### 2. 查看日志输出
```
📱 检查iOS运营商信息访问权限...
⚠️ 在模拟器中运行，运营商信息可能不可用
❌ iOS权限检查失败: 缺少运营商信息访问权限
💡 可能的原因:
   1. 在模拟器中运行（模拟器不支持运营商信息）
   2. 设备不支持蜂窝网络（如WiFi版iPad）
   3. 缺少必要的权限配置
   4. iOS系统限制
```

### 3. 真实设备测试
- 使用iPhone真机测试
- 确保SIM卡已插入并激活
- 检查网络连接状态
- 查看Xcode控制台日志

## 最佳实践

### 1. 权限处理策略
- **优雅降级**: 权限失败时提供替代方案
- **用户友好**: 清晰的错误提示和解决建议
- **兼容性**: 支持不同设备和iOS版本

### 2. 错误处理
- **详细日志**: 记录权限检查的详细过程
- **分类处理**: 区分不同类型的权限问题
- **用户提示**: 向用户说明权限要求

### 3. 测试策略
- **真机测试**: 在真实设备上验证功能
- **多设备测试**: 测试不同型号的设备
- **版本测试**: 在不同iOS版本上测试

## 相关文件
- `ios/Runner/Info.plist` - 权限描述
- `ios/Runner/Runner.entitlements` - 权限配置
- `ios/Runner/AppDelegate.swift` - 权限检查实现
- `lib/utils/carrier_detector.dart` - Flutter权限检查
- `lib/pages/login/login_logic.dart` - 登录逻辑权限处理