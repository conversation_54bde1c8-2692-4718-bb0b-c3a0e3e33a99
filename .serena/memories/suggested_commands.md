# Lima App Frontend - 建议命令

## 开发环境设置
```bash
# 检查Flutter环境
flutter doctor

# 获取依赖
flutter pub get

# 清理项目
flutter clean

# 更新依赖
flutter pub upgrade
```

## 代码质量检查
```bash
# 代码分析
flutter analyze

# 代码格式化
dart format lib/

# 检查代码风格
flutter analyze --no-fatal-infos
```

## 构建和运行
```bash
# 运行调试版本
flutter run

# 运行发布版本
flutter run --release

# 构建APK
flutter build apk --release

# 构建iOS
flutter build ios --release

# 构建AAB (Android App Bundle)
flutter build appbundle --release
```

## 平台特定命令

### Android
```bash
# 构建APK
flutter build apk --release

# 安装到设备
flutter install

# 查看连接的设备
flutter devices
```

### iOS
```bash
# 构建iOS
flutter build ios --release --no-codesign

# 使用fastlane部署到TestFlight
cd ios && fastlane testflight

# 简化部署脚本
./deploy_simple.sh

# 完整部署脚本
./deploy_testflight.sh
```

## 测试命令
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/widget_test.dart

# 运行集成测试
flutter drive --target=test_driver/app.dart
```

## 依赖管理
```bash
# 添加新依赖
flutter pub add package_name

# 移除依赖
flutter pub remove package_name

# 查看过时的依赖
flutter pub outdated

# 获取依赖树
flutter pub deps
```

## 调试和日志
```bash
# 查看日志
flutter logs

# 连接到调试器
flutter attach

# 性能分析
flutter run --profile
```

## 系统工具 (macOS)
```bash
# 文件操作
ls -la          # 列出文件
find . -name "*.dart"  # 查找Dart文件
grep -r "keyword" lib/ # 搜索关键词

# Git操作
git status      # 查看状态
git add .       # 添加所有更改
git commit -m "message"  # 提交
git push        # 推送

# 进程管理
ps aux | grep flutter    # 查看Flutter进程
kill -9 PID             # 终止进程
```

## 项目特定命令
```bash
# 运行登录测试
dart run test_login.dart

# 检查蓝牙功能
# (需要在真机上测试)

# 清理缓存
flutter clean && flutter pub get
```