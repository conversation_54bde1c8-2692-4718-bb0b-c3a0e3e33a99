# Lima App Frontend - 开发指南和最佳实践

## 开发环境要求

### 系统要求
- **macOS**: 用于iOS开发和测试
- **Flutter SDK**: 3.0.0或更高版本
- **Dart SDK**: 3.0.0或更高版本
- **Xcode**: 最新版本（iOS开发）
- **Android Studio**: 最新版本（Android开发）

### 开发工具推荐
- **IDE**: VS Code + Flutter插件 或 Android Studio
- **调试工具**: Flutter Inspector, Dart DevTools
- **版本控制**: Git
- **API测试**: Postman 或 类似工具

## 架构设计原则

### 1. 分层架构
```
Presentation Layer (UI)
    ↓
Business Logic Layer (Controllers)
    ↓
Data Layer (Services/APIs)
    ↓
Infrastructure Layer (Utils/Config)
```

### 2. 模块化设计
- **页面模块**: 独立的功能页面
- **组件模块**: 可复用的UI组件
- **服务模块**: 业务逻辑和数据处理
- **工具模块**: 通用工具和帮助类

### 3. 依赖注入
```dart
// 使用GetX进行依赖注入
Get.put(LoginLogic());
Get.lazyPut(() => ApiService());
```

## 代码组织规范

### 1. 文件结构规范
```
lib/pages/feature_name/
├── feature_name_view.dart      # UI层
├── feature_name_logic.dart     # 业务逻辑层
└── feature_name_binding.dart   # 依赖绑定（可选）
```

### 2. 导入规范
```dart
// 1. Dart核心库
import 'dart:async';
import 'dart:convert';

// 2. Flutter框架
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. 第三方包（按字母顺序）
import 'package:dio/dio.dart';
import 'package:get/get.dart';

// 4. 项目内部文件（按层级顺序）
import '../../common/config.dart';
import '../../utils/q_log.dart';
import '../widgets/custom_button.dart';
```

### 3. 类组织规范
```dart
class ExampleLogic extends GetxController {
  // 1. 静态常量
  static const String TAG = 'ExampleLogic';
  
  // 2. 实例变量
  final RxBool isLoading = false.obs;
  final TextEditingController textController = TextEditingController();
  
  // 3. 构造函数
  ExampleLogic();
  
  // 4. 生命周期方法
  @override
  void onInit() {
    super.onInit();
  }
  
  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }
  
  // 5. 公共方法
  void publicMethod() {}
  
  // 6. 私有方法
  void _privateMethod() {}
}
```

## 状态管理最佳实践

### 1. GetX使用规范
```dart
// 响应式变量
final RxString userName = ''.obs;
final RxBool isLoggedIn = false.obs;

// 更新UI
userName.value = 'New Name';

// 监听变化
ever(isLoggedIn, (bool value) {
  if (value) {
    // 登录成功处理
  }
});
```

### 2. 页面状态管理
```dart
// 使用GetBuilder进行局部更新
GetBuilder<LoginLogic>(
  id: 'login_form',
  builder: (logic) => Form(...)
);

// 使用Obx进行响应式更新
Obx(() => Text(logic.userName.value));
```

## 网络请求规范

### 1. API接口定义
```dart
class API {
  static const String login = 'auth/login';
  static const String userInfo = 'user/info';
  
  static Future<NetModel> loginApi({
    required String phone,
    required String code,
  }) async {
    final response = await Request().post(
      login,
      params: {'phone': phone, 'code': code},
    );
    return NetModel.fromJson(response);
  }
}
```

### 2. 错误处理
```dart
try {
  final result = await API.loginApi(phone: phone, code: code);
  if (result.state == 200) {
    // 成功处理
  } else {
    EasyLoading.showError(result.msg ?? '请求失败');
  }
} catch (e) {
  QLog('登录失败: $e');
  EasyLoading.showError('网络错误，请重试');
}
```

## UI开发规范

### 1. 响应式设计
```dart
// 使用ScreenUtil进行屏幕适配
Container(
  width: 200.w,
  height: 100.h,
  padding: EdgeInsets.all(16.w),
)

// 字体大小适配
Text(
  'Hello',
  style: TextStyle(fontSize: 16.sp),
)
```

### 2. 主题和样式
```dart
// 使用统一的颜色定义
Container(
  color: AppColors.primary,
  child: Text(
    'Title',
    style: TextStyle(color: AppColors.textPrimary),
  ),
)
```

### 3. 组件复用
```dart
// 创建可复用组件
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  
  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      child: Text(text),
    );
  }
}
```

## 性能优化指南

### 1. Widget优化
- 使用`const`构造函数
- 避免在`build`方法中创建复杂对象
- 合理使用`ListView.builder`

### 2. 内存管理
```dart
@override
void onClose() {
  // 释放控制器
  textController.dispose();
  
  // 取消订阅
  subscription?.cancel();
  
  // 停止定时器
  timer?.cancel();
  
  super.onClose();
}
```

### 3. 图片优化
- 使用合适的图片格式和大小
- 实现图片缓存机制
- 使用`CachedNetworkImage`

## 测试策略

### 1. 单元测试
```dart
void main() {
  group('LoginLogic Tests', () {
    late LoginLogic logic;
    
    setUp(() {
      logic = LoginLogic();
    });
    
    test('should validate phone number', () {
      expect(logic.validatePhone('13800138000'), true);
      expect(logic.validatePhone('123'), false);
    });
  });
}
```

### 2. Widget测试
```dart
void main() {
  testWidgets('Login page should display correctly', (tester) async {
    await tester.pumpWidget(MyApp());
    await tester.pumpAndSettle();
    
    expect(find.text('登录'), findsOneWidget);
    expect(find.byType(TextField), findsWidgets);
  });
}
```

## 安全考虑

### 1. 数据安全
- 敏感数据加密存储
- 网络传输使用HTTPS
- Token安全管理

### 2. 代码安全
- 避免硬编码敏感信息
- 使用混淆保护代码
- 定期更新依赖包

## 部署和发布

### 1. 版本管理
- 遵循语义化版本规范
- 维护详细的更新日志
- 使用Git标签管理版本

### 2. 构建配置
- 区分开发、测试、生产环境
- 配置不同的API地址和参数
- 使用环境变量管理配置