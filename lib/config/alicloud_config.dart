import 'package:lima/common/config.dart';
/// 阿里云服务配置
/// 包含阿里云号码认证服务 (DYPNS) 的配置信息
class AlicloudConfig {
  /// 阿里云AccessKey ID
  /// 在阿里云控制台 -> 访问控制 -> 用户管理中创建
  /// 文档：https://help.aliyun.com/document_detail/53045.html
  static const String accessKeyId = 'LTAI5tHd5XvHCVV22km3Dtvg';
  
  /// 阿里云AccessKey Secret
  /// 与AccessKey ID配对使用，用于API签名验证
  static const String accessKeySecret = '******************************';
  
  /// 自定义Scheme
  /// 用于阿里云DYPNS SDK的回调，需要在应用中唯一
  static const String scheme = 'lima_auth';
  
  /// 环境配置
  /// 'production' - 生产环境
  /// 'sandbox' - 沙箱环境（测试用）
  static const String environment = 'production';
  
  /// 阿里云DYPNS服务地域
  /// 可选值：cn-hangzhou, cn-beijing, cn-shanghai, cn-shenzhen
  static const String region = 'cn-hangzhou';
  
  /// 超时配置
  static const int initTimeout = 5000; // 初始化超时时间（毫秒）
  static const int preLoginTimeout = 5000; // 预取号超时时间（毫秒）
  static const int loginTimeout = 30000; // 一键登录超时时间（毫秒）
  
  /// UI配置
  static Map<String, dynamic> get authUIConfig => {
    // 导航栏配置
    'navTitle': '立马科技',
    'navColor': '#2196F3',
    'navTextColor': '#FFFFFF',
    'navReturnBtnImage': 'nav_return',

    // 背景配置
    'authBGColor': '#FFFFFF',
    'authBGImage': 'auth_bg',

    // Logo配置
    'logoImage': 'app_logo',
    'logoWidth': 80,
    'logoHeight': 80,
    'logoOffsetY': 100,

    // 手机号码显示配置
    'numberColor': '#333333',
    'numberSize': 18,
    'numberOffsetY': 200,

    // 登录按钮配置
    'loginBtnText': '本机号码一键登录',
    'loginBtnTextColor': '#FFFFFF',
    'loginBtnBGColor': '#4CAF50',
    'loginBtnWidth': 300,
    'loginBtnHeight': 50,
    'loginBtnOffsetY': 280,
    'loginBtnCornerRadius': 25,

    // 切换账号按钮配置
    'changeBtnText': '切换账号',
    'changeBtnTextColor': '#2196F3',
    'changeBtnOffsetY': 350,

    // 隐私条款配置
    'privacyTextColor': '#666666',
    'privacyProtocolColor': '#2196F3',
    'privacyOffsetY': 450,
    'privacyTextSize': 12,
    'privacyBefore': '登录即同意',
    'privacyEnd': '并使用本机号码登录',

    // 隐私协议
    'privacyOne': ['《用户协议》', '${Config.BASE_WEBSITE}/me/about/agreement?nav=false'],
    'privacyTwo': ['《隐私政策》', Config.USER_PROTOCOL],

    // 其他配置
    'sloganText': '立马科技 - 让出行更简单',
    'sloganTextColor': '#999999',
    'sloganOffsetY': 500,
    'sloganTextSize': 14,
  };
  
  /// 获取完整的初始化配置
  static Map<String, dynamic> getInitConfig() {
    return {
      'accessKeyId': accessKeyId,
      'accessKeySecret': accessKeySecret,
      'scheme': scheme,
      'environment': environment,
      'region': region,
      'timeout': initTimeout,
    };
  }
  
  /// 获取预取号配置
  static Map<String, dynamic> getPreLoginConfig() {
    return {
      'timeout': preLoginTimeout,
    };
  }
  
  /// 获取一键登录配置
  static Map<String, dynamic> getLoginConfig() {
    return {
      'timeout': loginTimeout,
      'authUIConfig': authUIConfig,
    };
  }
  
  /// 验证配置是否完整
  static bool isConfigValid() {
    return accessKeyId.isNotEmpty && 
           accessKeyId != 'YOUR_ACCESS_KEY_ID' &&
           accessKeySecret.isNotEmpty && 
           accessKeySecret != 'YOUR_ACCESS_KEY_SECRET';
  }
  
  /// 获取配置错误信息
  static String getConfigError() {
    if (accessKeyId.isEmpty || accessKeyId == 'YOUR_ACCESS_KEY_ID') {
      return '请配置阿里云AccessKey ID';
    }
    if (accessKeySecret.isEmpty || accessKeySecret == 'YOUR_ACCESS_KEY_SECRET') {
      return '请配置阿里云AccessKey Secret';
    }
    return '';
  }
}

/// 阿里云DYPNS错误码映射
class AlicloudErrorCode {
  static const Map<String, String> errorMessages = {
    // 初始化错误
    'INIT_FAILED': '初始化失败',
    'INVALID_PARAMS': '参数无效',
    'NETWORK_ERROR': '网络错误',
    
    // 环境检查错误
    'WIFI_CONNECTED': '请关闭WiFi，使用移动网络',
    'OPERATOR_NOT_SUPPORTED': '当前运营商不支持一键登录',
    'SIM_NOT_READY': 'SIM卡未准备就绪',
    
    // 预取号错误
    'PRELOGIN_FAILED': '预取号失败',
    'PRELOGIN_TIMEOUT': '预取号超时',
    
    // 登录错误
    'LOGIN_FAILED': '登录失败',
    'LOGIN_TIMEOUT': '登录超时',
    'USER_CANCELED': '用户取消登录',
    'AUTH_FAILED': '授权失败',
    
    // Token验证错误
    'TOKEN_INVALID': 'Token无效',
    'TOKEN_EXPIRED': 'Token已过期',
    'VERIFY_FAILED': '验证失败',
    
    // 服务端错误
    'SERVER_ERROR': '服务器错误',
    'QUOTA_EXCEEDED': '调用次数超限',
    'PERMISSION_DENIED': '权限不足',
  };
  
  /// 获取错误信息
  static String getErrorMessage(String errorCode) {
    return errorMessages[errorCode] ?? '未知错误';
  }
}
