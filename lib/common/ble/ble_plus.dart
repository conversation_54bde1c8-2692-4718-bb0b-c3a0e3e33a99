import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:lima/common/ble/permissions.dart';

import 'package:permission_handler/permission_handler.dart';

import '../../utils/q_log.dart';
import '../../utils/utils.dart';

///
class BlePlus {
  static final BlePlus instance = BlePlus._();

  factory BlePlus() {
    return instance;
  }

  BlePlus._();

  /// 蓝牙连接状态
  Stream get connectState => _connectStream.stream;

  final StreamController<BluetoothConnectionState> _connectStream =
      StreamController.broadcast();

  ///上报的蓝牙数据
  final StreamController _respBleDataController =
      StreamController.broadcast(sync: true);

  Stream get bleDataStream => _respBleDataController.stream;

  /// 蓝牙适配器状态
  Stream<BluetoothAdapterState> blueAdapterState() {
    return FlutterBluePlus.adapterState;
  }

  bool get isScanning => FlutterBluePlus.isScanningNow;

  ///蓝牙连接实例集合
  List<BluetoothInstance> bleMap = [];

  /// 权限检查
  /// 1. 检查手机蓝牙按钮是否开启
  /// 2. 检查蓝牙所需权限是否允许
  Future<bool> permissionCheck({bool? needRequest = false}) async {
    bool f = true;

    bool adapterSupported = await FlutterBluePlus.isSupported;

    /// 对于iOS应用, [adapterState] 一打开默认为[unkown],必须做一段时间的等待才能获取到正确的状态
    Future adapterFuture = FlutterBluePlus.adapterState
        .where((event) => event == BluetoothAdapterState.on)
        .first;

    int delayTime = GetPlatform.isAndroid ? 1 : 3;

    await adapterFuture.timeout(
      Duration(seconds: delayTime),
      onTimeout: () {
        QLog('获取蓝牙状态超时');
        return BluetoothAdapterState.unknown;
      },
    ).catchError((e) {
      QLog('获取蓝牙状态失败,$e');
    });

    /// 对于IOS应用,需要先使用蓝牙才能开始获取蓝牙权限
    bool available = adapterSupported &&
        (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.on);

    if (GetPlatform.isIOS) {
      var p = await Permission.bluetooth.request();
      if (!p.isGranted || !available) {
        // EasyLoading.showToast('请前往设置允许应用使用蓝牙');
        return false;
      }
    } else {
      var perList = [
        Permission.bluetoothConnect,
        Permission.location,
        Permission.bluetoothScan,
        Permission.bluetoothAdvertise,
      ];
      if (await PermissionManager.hasPermissionGranted(perList) == false ||
          !available) {
        f = false;
      }
      if (!f) {
        if (needRequest == true) {
          await PermissionManager.requestPermission(perList);

          ///重新检查权限
          if (await PermissionManager.hasPermissionGranted(perList) == false ||
              !available) {
            f = false;
          }
        }
      }
    }

    return f;
  }

  Stream<List<ScanResult>> scanResults() {
    return FlutterBluePlus.onScanResults;
  }

  Stream<List<ScanResult>> cachedScanResults() {
    return FlutterBluePlus.scanResults;
  }

  Future scan({
    List<Guid> withServices = const [],
    List<String> withRemoteIds = const [],
    List<String> withNames = const [],
    List<String> withKeywords = const [],
    List<MsdFilter> withMsd = const [],
    List<ServiceDataFilter> withServiceData = const [],
    Duration? timeout,
    Duration? removeIfGone,
    bool continuousUpdates = false,
    int continuousDivisor = 1,
    bool oneByOne = false,
    AndroidScanMode androidScanMode = AndroidScanMode.lowLatency,
    bool androidUsesFineLocation = false,
    bool permissionNeedRequest = false,
  }) async {
    bool r = await permissionCheck(needRequest: permissionNeedRequest);
    if (!r) {
      return false;
    }
    await FlutterBluePlus.startScan(
        withServices: withServices,
        withRemoteIds: withRemoteIds,
        withNames: withNames,
        withKeywords: withKeywords,
        withMsd: withMsd,
        withServiceData: withServiceData,
        timeout: timeout,
        removeIfGone: removeIfGone,
        continuousUpdates: continuousUpdates,
        continuousDivisor: continuousDivisor,
        oneByOne: oneByOne,
        androidScanMode: androidScanMode,
        androidUsesFineLocation: androidUsesFineLocation);
  }

  /// 连接蓝牙
  /// 需要先监听device的[BluetoothDevice.connectionState]
  Future connect(
    BluetoothInstance bleInstance, {
    Duration timeout = const Duration(seconds: 15),
    int? mtu,
    bool autoConnect = false,
  }) async {
    Mutex mtx = MutexFactory.getMutexForKey('connble');
    await mtx.take();
    try {
      if (FlutterBluePlus.isScanningNow) {
        await FlutterBluePlus.stopScan();
      }

      /// 将设备缓存到[BlePlus]的已连接列表中
      StreamSubscription connStateSub =
          bleInstance.device.connectionState.listen((event) async {
        if (event == BluetoothConnectionState.connected) {
          saveConnectedDevice(device: bleInstance);
        }
        if (event == BluetoothConnectionState.disconnected) {
          bleMap.removeWhere(
              (element) => Utils.StringEqual(bleInstance.mac, element.mac));
        }
      });

      await bleInstance.device
          .connect(timeout: timeout, mtu: mtu, autoConnect: autoConnect);

      bleInstance.device
          .cancelWhenDisconnected(connStateSub, delayed: true, next: false);
    } finally {
      mtx.give();
    }
  }

  Future saveReadAndWrite({
    required BluetoothInstance bluetoothInstance,
  }) async {
    List<BluetoothService> services =
        await bluetoothInstance.device.discoverServices();
    for (BluetoothService s in services) {
      String serviceValue = s.serviceUuid.toString().toUpperCase();
      QLog('获取到的uuid$s');
      if (serviceValue.contains(bluetoothInstance.service_uid.toUpperCase())) {
        QLog('获取到服务${s.uuid}');
        for (BluetoothCharacteristic c in s.characteristics) {
          // QLog('获取到的特征值${c.uuid}');
          var uuid = c.uuid.toString().toUpperCase();
          if (uuid.contains(bluetoothInstance.read_uid.toUpperCase()) & c.properties.notify) {
            //带有读权限特征值
            bluetoothInstance.read_c = c;
            QLog('获取到读特征值${c.uuid}');
          }
          if (uuid.contains(bluetoothInstance.write_uid.toUpperCase()) & (c.properties.write)) {
            //带有写权限特征值
            bluetoothInstance.write_c = c;
            QLog('获取到写特征值${c.uuid}');
          }
        }
        break;
      }
    }
  }

  /// 注册notify
  Stream<List<int>>? registerDataNotify({
    required BluetoothInstance bluetoothInstance,
  }) {
    bluetoothInstance.read_c?.setNotifyValue(true);
    return bluetoothInstance.read_c?.onValueReceived;
    // _readSpt = _readCha?.onValueReceived.listen((event) {
    //   if (event.isEmpty) {
    //     return;
    //   }
    //   QLog('接收蓝牙数据[hexString]-> ${hexEncode(event)}');
    //   _respBleDataController.add(event);
    // });
  }

  /// 停止搜索蓝牙
  Future stopScan() async {
    await FlutterBluePlus.stopScan();
  }

  bool hasDeviceConnected({required BluetoothDevice device}) {
    return device.isConnected;
  }

  Future disconnect({required BluetoothDevice device}) async {
    return device.disconnect();
  }

  Future actionBleWrite({
    required List<int> data,
    required BluetoothCharacteristic? cha,
    bool withoutResponse = false,
    bool splitWithMtu = false,
  }) async {
    QLog('BLE下发数据->${hexEncode(data)}');
     await Future.delayed(const Duration(milliseconds: 20));
    int mtu = cha?.device.mtuNow ?? 16;
    // QLog('蓝牙MTU->$mtu');
    if (splitWithMtu) {
      if ((mtu - 4) >= data.length) {
        await cha?.write(data,
            withoutResponse: withoutResponse, allowLongWrite: false);
        return;
      }
      List<List<int>> list = splitByMtu(data,mtu - 4);
      for (List<int> l in list) {
        await Future.delayed(const Duration(milliseconds: 20));
        await cha?.write(l,
            withoutResponse: withoutResponse, allowLongWrite: false);
      }
      return;
    }

    await cha?.write(data,
        withoutResponse: withoutResponse, allowLongWrite: true);
  }

  List<List<int>> splitByMtu(List<int> data, int mtu) {
    List<List<int>> list = [];
    for (int i = 0; i < data.length; i += mtu) {
      int end = i + mtu;
      if (end > data.length) {
        end = data.length;
      }
      List<int> subList = data.sublist(i, end);
      list.add(subList);
    }
    return list;
  }

  /// 根据mac查询已经连接的蓝牙设备
  BluetoothInstance? getConnectedDevice({
    required String mac,
  }) {
    /// 更新[bleMap] 中的数据,和BLE连接列表保持更新
    List<BluetoothDevice> list = FlutterBluePlus.connectedDevices;
    for (BluetoothInstance d in bleMap) {
      if (!list.any(
          (element) => Utils.StringEqual(element.advName, d.device.advName))) {
        /// 断开本地的连接
        disconnect(device: d.device);
        bleMap.remove(d);
      }
    }
    BluetoothInstance? d = bleMap
        .firstWhereOrNull((element) => Utils.StringEqual(element.mac, mac));
    return d;
  }

  void saveConnectedDevice({
    required BluetoothInstance device,
  }) {
    int idx = bleMap
        .indexWhere((element) => Utils.StringEqual(element.mac, device.mac));
    if (idx == -1) {
      bleMap.add(device);
    } else {
      bleMap[idx] = device;
    }

    /// 更新[bleMap] 中的数据,和BLE连接列表保持更新
    List<BluetoothDevice> list = FlutterBluePlus.connectedDevices;
    for (BluetoothInstance d in bleMap) {
      if (!list.any(
          (element) => Utils.StringEqual(element.advName, d.device.advName))) {
        /// 断开本地的连接
        disconnect(device: d.device);
        bleMap.remove(d);
      }
    }
  }

  /// 重置所有连接
  Future resetAllConnects() async {
    for (BluetoothInstance d in bleMap) {
      await disconnect(device: d.device);
      bleMap.remove(d);
    }
  }

  /// 蓝牙配对
  Future createBond({required BluetoothInstance bluetoothInstance}) async {
    await bluetoothInstance.device.createBond();
  }

  /// 获取已经在系统中连接的设备
  Future<List<BluetoothDevice>> getSysConnectedDevice() async {
    return await FlutterBluePlus.systemDevices([]);
  }

  Future<List<BluetoothDevice>> getBondedDevice() async {
    return await FlutterBluePlus.bondedDevices;
  }

  ///
  ///  Utils
  ///
  String intToHex(int num) {
    String hexString = num.toRadixString(16).padLeft(4, '0');
    return hexString;
  }

  String hexEncode(List<int> numbers) {
    return numbers
        .map((n) => (n & 0xFF).toRadixString(16).padLeft(2, '0'))
        .join();
  }

  List<int> hexDecode(String hex) {
    List<int> numbers = [];
    for (int i = 0; i < hex.length; i += 2) {
      String hexPart = hex.substring(i, i + 2);
      int num = int.parse(hexPart, radix: 16);
      numbers.add(num);
    }
    return numbers;
  }
}

/// 蓝牙实例, 保存蓝牙基本信息
class BluetoothInstance {
  final BluetoothDevice device; // 设备
  int? rssi; // 信号强度
  final String mac; // mac地址
  final String write_uid; // 写入特征值
  final String read_uid; // 读取特征值
  final String service_uid; // 服务特征值
  BluetoothCharacteristic? write_c;
  BluetoothCharacteristic? read_c;

  BluetoothInstance({
    required this.device,
    required this.write_uid,
    required this.read_uid,
    required this.service_uid,
    required this.mac,
    this.write_c,
    this.read_c,
    this.rssi,
  });
}
