import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/utils.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../components/q_alert.dart';
import '../../pages/webview/webview_view.dart';
import '../../res/colors.dart';
import '../config.dart';

typedef VoidFutureCallBack = Future<void> Function();
typedef BoolFutureCallBack = Future Function();

class PermissionManager {
  ///检查权限的申请情况
  ///https://stackoverflow.com/questions/42467663/how-to-async-await-in-list-foreach-in-dart
  static Future<bool> hasPermissionGranted(List<Permission> p) async {
    Completer<bool> c = Completer();
    bool granted = true;
    for (var element in p) {
      granted &= await element.isGranted;
    }
    // await Future.forEach(p, (element) async {
    //   element as Permission;
    //   granted &= await element.isGranted;
    // });
    c.complete(granted);

    return c.future;
  }

  ///申请权限
  static Future<Map<Permission, PermissionStatus>> requestPermission(
          List<Permission> p) =>
      p.request();

  ///各类权限说明弹窗
  ///必传参数：msge文案说明； onConfirm确定按钮点击回调； onCancel取消按钮点击回调
  /// 返回类型Future，点击确认或者取消后完成异步流程。
  static Future questPermsInfoDialog({
    required String title,
    required String msg,
    BoolFutureCallBack? onConfirm,
    BoolFutureCallBack? onCancel,
  }) async {
    Mutex _mtx = MutexFactory.getMutexForKey('permDialog');
    await _mtx.take();

    Completer c = Completer();
    try {
      await Get.bottomSheet(
        _infoBottomSheetWidget(
          title: title,
          msg: msg,
          c: c,
          onConfirm: onConfirm,
          onCancel: onCancel,
        ),
        isDismissible: false,
      ).whenComplete(() {
        //
        QLog('bottomSheet dismissed');
        if (!c.isCompleted) {
          c.complete(false);
        }
      });
    } finally {
      _mtx.give();
      if (!c.isCompleted) {
        c.complete(false);
      }
    }
    return c.future;
  }

  static Widget _infoBottomSheetWidget(
      {required String title,
      required String msg,
      required Completer c,
      BoolFutureCallBack? onConfirm,
      BoolFutureCallBack? onCancel}) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.color_FFFFFFFF,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 25.h),
            child: Center(
              child: Text(title,
                  style: TextStyle(
                    color: AppColors.color_FF1D1D1D,
                    fontSize: 17.sp,
                  )),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(20.h),
            child: RichText(
              text: TextSpan(
                  style: TextStyle(
                    color: AppColors.color_FF848484,
                    fontSize: 14.sp,
                  ),
                  children: [
                    TextSpan(
                      text: msg,
                    ),
                    const TextSpan(
                      text: '\n如您关闭或拒绝权限调取，将导致上述功能无法正常使用，但不影响使用本应用的基本功能。具体请参阅',
                    ),
                    TextSpan(
                      text: '《隐私政策》',
                      style: TextStyle(
                        color: AppColors.color_FF2B6BFF,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.to(() => WebviewPage(), arguments: {
                            'url': Config.USER_PROTOCOL, //地址
                            'navBar': false, // 网页是否自带导航栏
                            'title': '隐私政策', //网页标题
                          });
                        },
                    ),
                  ]),
            ),
          ),
          const Divider(),
          Padding(
            padding: EdgeInsets.only(right: 20.h, left: 20.w),
            child: Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () async {
                      Get.back();
                      await onCancel?.call();
                    },
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all(EdgeInsets.all(12.h)),
                      overlayColor:
                          MaterialStateProperty.all(AppColors.color_332B6BFF),
                      backgroundColor:
                          MaterialStateProperty.resolveWith((states) {
                        if (states.contains(MaterialState.pressed)) {
                          return AppColors.color_FFFFFFFF;
                        }
                        return AppColors.color_FFFFFFFF;
                      }),
                      animationDuration: Duration(milliseconds: 1),
                    ),
                    child: Container(
                      constraints: BoxConstraints(maxHeight: 62.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Center(
                        child: Text(
                          '取消',
                          style: TextStyle(
                              color: AppColors.color_FF848484, fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ),
                ),
                Divider(),
                Expanded(
                  child: TextButton(
                    onPressed: () async {
                      Get.back();
                      await onConfirm?.call();
                    },
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all(EdgeInsets.all(12.h)),
                      overlayColor:
                          MaterialStateProperty.all(AppColors.color_332B6BFF),
                      backgroundColor:
                          MaterialStateProperty.resolveWith((states) {
                        if (states.contains(MaterialState.pressed)) {
                          return AppColors.color_FFFFFFFF;
                        }
                        return AppColors.color_FFFFFFFF;
                      }),
                      animationDuration: Duration(milliseconds: 1),
                    ),
                    child: Container(
                      constraints: BoxConstraints(maxHeight: 62.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Center(
                        child: Text(
                          '确定',
                          style: TextStyle(
                              color: AppColors.color_FF2B6BFF, fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).marginOnly(bottom: 20.h);
  }

  ///需要跳转到设置进行手动设置的弹窗
  static void sysSettingDialog(
      {required VoidCallback onConfirm,
      required String title,
      String? confirmTitle,
      VoidCallback? onCancel}) {
    Get.dialog(
      QAlert(
        title: '${title}',
        contentStr: '${confirmTitle}',
        onConfim: onConfirm,
      ),
    );
  }
}
