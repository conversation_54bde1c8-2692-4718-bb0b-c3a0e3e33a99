// import 'dart:async';
// import 'dart:convert';
// import 'dart:typed_data';
//
// import 'package:flutter_ble/flutter_ble.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:get/get.dart';
// import 'package:get/get_utils/src/platform/platform.dart';
// import 'package:lima/common/ble/permissions.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:rxdart/rxdart.dart';
//
// import '../../utils/q_log.dart';
//
// class Ble {
//   static final Ble _instance = Ble._();
//
//   factory Ble() => _instance;
//
//   final blue = FlutterBle();
//
//   /// 蓝牙是否连接上
//   BehaviorSubject<BluetoothDeviceState> connectState =
//       BehaviorSubject.seeded(BluetoothDeviceState.disconnecting);
//
//   //
//   // StreamController<BluetoothDeviceState> connectState =
//   // StreamController<BluetoothDeviceState>.broadcast(sync: false)..add(BluetoothDeviceState.disconnected);
//
//   ///蓝牙扫描时的数据流监听
//   StreamSubscription? scanStream;
//
//   ///连接上的设备
//   BluetoothDevice? mDevice;
//
//   /// 读写特征值,OTA写特征值
//   BluetoothCharacteristic? _readCha, _writeCha;
//
//   /// stream数据流的监听句柄(读特征值, 蓝牙状态), 用作蓝牙disconnect时候的销户操作
//   StreamSubscription? _readSpt, _stateSpt;
//
//   ///上报的蓝牙数据
//   final StreamController _respBleDataController =
//       StreamController.broadcast(sync: true);
//
//   Stream get bleDataStream => _respBleDataController.stream;
//
//   ///上报的蓝牙数据-缓存
//   List<int> mData = [];
//
//   ///connectBehavior
//   BehaviorSubject _connectBehavior = BehaviorSubject();
//
//   String SERVICE_UID = "1802";
//   String WRITE_UID = "2B06";
//   String READ_UID = "2B06";
//
//   Ble._() {
//     ///监听iOS设备蓝牙开关关闭
//     blue.state.listen((event) {
//       if (event == BluetoothState.off) {
//         if (GetPlatform.isIOS) {
//           disconnectBle();
//           connectState.add(BluetoothDeviceState.disconnected);
//           // reset();
//         }
//       }
//
//       ///安卓设备关闭蓝牙会直接断开gatt的连接，并且销毁系统服务的gatt实例，如果我们此时不执行实例销毁的操作，会再次拿到被ipc服务销毁的gatt去尝试连接，从而产生报错。
//       if (event == BluetoothState.turningOff) {
//         if (GetPlatform.isAndroid) {
//           disconnectBle();
//         }
//       }
//     });
//
//     _connectBehavior.debounceTime(Duration(milliseconds: 80)).listen((event) {
//       connectState.add(event);
//     });
//   }
//
//   ///搜索附近蓝牙
//   Stream<ScanResult> search({int? duration}) async* {
//     ///每次开始搜索蓝牙前，先检查权限是否充分
//     bool b = await permissionCheck();
//     QLog('蓝牙权限: $b');
//     if (!b) {
//       throw Exception('蓝牙权限不足');
//     }
//     await stopScan();
//     if (GetPlatform.isAndroid) {
//       await Future.delayed(Duration(milliseconds: 200));
//     }
//     QLog('开始搜索蓝牙');
//
//     yield* blue.scan(timeout: Duration(seconds: duration ?? 10));
//   }
//
//   Future connectWithName(String deviceId) async {
//     bool searched = false;
//     Completer c = Completer<bool>();
//
//     QLog('搜索蓝牙设备: $deviceId');
//     bool b = await permissionCheck();
//     if (!b) {
//       c.complete(false);
//       return c.future;
//     }
//
//     ///在每一次搜索前调用`scanStream?.cancel()`,防止出现以下情况：
//     /// * 在上一次Scan还没结束时开启下一个Scan搜索，如果不cancel数据流，则会将上一次scan结果返回到本次数据流。
//     scanStream?.cancel();
//     await blue.stopScan();
//     if (GetPlatform.isAndroid)
//       await Future.delayed(Duration(milliseconds: 300));
//
//     var st = await blue.scan(timeout: Duration(seconds: 10));
//     scanStream = st.listen((event) {
//       // Log.i('搜索蓝牙');
//       // Log.i(event);
//       if (event.device.name.isNotEmpty) {
//         event.device.localName = event.advertisementData.localName;
//       }
//       if (event.advertisementData.localName == deviceId) {
//         searched = true;
//         blue.stopScan();
//
//         ///安卓连接蓝牙时，Gatt status = 133 概率很大，暂时没有看到完善的解决方案，讨论地址： https://github.com/android/connectivity-samples/issues/18
//         connectBle(event.device);
//       }
//     }, onDone: () {
//       QLog('--搜索蓝牙设备执行结束--,结果-$searched');
//       c.complete(searched);
//       scanStream?.cancel();
//     });
//     return c.future;
//   }
//
//   /// deviceId: 设备Mac地址
//   Future connectBle(BluetoothDevice device,{String? deviceId}) async {
//     Completer completer = Completer();
//     mDevice = device;
//     mDevice?.macAddress = deviceId ?? '';
//
//     QLog('准备连接蓝牙 ${device.name}');
//     await device
//         .connect(autoConnect: false, timeout: const Duration(seconds: 10))
//         .then((value) {
//       ///1. 注册蓝牙状态监听
//       registerBleState();
//       completer.complete(true);
//     }, onError: (e) {
//       QLog('蓝牙连接过程error, ${e.toString()}');
//       connectState.add(BluetoothDeviceState.disconnected);
//       completer.complete(false);
//     });
//
//     return completer.future;
//   }
//
//   /// 注册蓝牙状态监听
//   void registerBleState() {
//     _stateSpt?.cancel();
//     _stateSpt = mDevice?.state.listen((event) {
//       _connectBehavior.add(event);
//       if (event == BluetoothDeviceState.connecting) {
//         QLog('蓝牙连接状态: 连接中');
//       }
//       if (event == BluetoothDeviceState.connected) {
//         QLog('蓝牙连接状态: 已连接');
//         // saveReadAndWrite();
//       }
//       if (event == BluetoothDeviceState.disconnected) {
//         QLog('蓝牙连接状态: 已断开');
//         reset();
//       }
//     }, onError: (e) {
//       _stateSpt?.cancel();
//     });
//   }
//
//   ///检查服务是否已经被发现过
//
//   Future<bool> hasServiceDiscovered() async {
//     var f = await mDevice?.services.first;
//     return f?.isNotEmpty ?? false;
//   }
//
//   ///蓝牙连接成功以后，保存特征值
//   Future saveReadAndWrite() async {
//     ///保存读写特征值
//
//     List<BluetoothService>? services;
//     if (GetPlatform.isAndroid) {
//       await Future.delayed(Duration(milliseconds: 400));
//     }
//     QLog('扫描蓝牙service');
//     services = await mDevice?.discoverServices();
//
//     // QLog(services?.toString());
//     if (services == null) {
//       QLog('蓝牙service为空');
//       return;
//     }
//     for (BluetoothService service in services) {
//       var serviceValue = service.uuid.toString().toUpperCase();
//       QLog(serviceValue);
//
//       if (serviceValue.contains(SERVICE_UID.toUpperCase())) {
//         QLog('读取到服务 $serviceValue');
//         var characteristics = service.characteristics;
//         // QLog(characteristics);
//         for (BluetoothCharacteristic c in characteristics) {
//
//           var uuid = c.uuid.toString().toUpperCase();
//           // uuid = uuid.substring(0, 8).substring(4);
//
//           if (uuid.contains(WRITE_UID.toUpperCase())) {
//             QLog('读取到写UUID $uuid');
//             //带有读权限特征值
//             _writeCha = c;
//
//             // BleConsoleManager.addMessage(
//             //     "\nfe62-write: ${_readCha?.properties.write}\nfe62-writeWithoutResponse: ${_readCha?.properties.writeWithoutResponse}\nfe62-read: ${_readCha?.properties.read}\nfe62-notify: ${_readCha?.properties.notify}");
//           }
//           if (uuid.contains(READ_UID.toUpperCase())) {
//             QLog('读取到读UUID $uuid');
//             //带有写权限特征值
//             _readCha = c;
//             // BleConsoleManager.addMessage(
//             //     "\nfe61-write: ${_writeCha?.properties.write}\nfe61-writeWithoutResponse: ${_writeCha?.properties.writeWithoutResponse}\nfe61-read: ${_writeCha?.properties.read}\nfe61-notify: ${_writeCha?.properties.notify}");
//           }
//         }
//         break;
//       }
//     }
//
//     /// 2. 注册读notify
//     await registerDataResponse();
//   }
//
//   /// 注册数据监听
//   Future registerDataResponse() async {
//     await _readCha?.setNotifyValue(true);
//     await _readSpt?.cancel();
//     _readSpt = null;
//     _readSpt = _readCha?.value.listen((v) {
//       var dXor = Uint8List.fromList(v);
//       if (dXor.isEmpty) return;
//       // Log.i('收到消息${dXor}');
//       //设备交互数据
//       QLog('接收蓝牙数据->[hexString]-> ${uint8ToHex(dXor)}');
//
//       /// 2. 转发数据
//       // receiveData.value = dXor.toList();
//       _respBleDataController.add(uint8ToHex(dXor));
//     });
//   }
//
//   /// 停止搜索蓝牙
//   Future stopScan() async {
//     await blue.stopScan();
//   }
//
//   Future createBond() async {
//     return blue.createBond(mDevice?.id.id ?? '');
//   }
//
//   Future<bool> hasDeviceConnected() async {
//     BluetoothDeviceState state =
//         await mDevice?.state.first ?? BluetoothDeviceState.disconnected;
//     if (mDevice != null && state == BluetoothDeviceState.connected) return true;
//     connectState.add(state);
//     return false;
//   }
//
//   /// 断开蓝牙连接
//   Future disconnectBle() async {
//     await mDevice?.disconnect();
//     _readSpt?.cancel();
//   }
//
//   ///发送消息
//   void actionBleWrite(List<int> data) async {
//     // QLog(_writeCha?.uuid);
//     // QLog(_readCha?.uuid);
//     QLog('发送蓝牙数据->${uint8ToHex(Uint8List.fromList(data))}');
//     // var mtu = await mDevice?.mtu.first;
//     // // Log.i('mtu: $mtu');
//     // if (mtu != null && mtu < data.length) {
//     //   for (int i = 0; i < data.length; i += mtu - 4) {
//     //     if (i + mtu - 4 < data.length) {
//     //       await _doWrite(data.sublist(i, i + mtu - 4));
//     //       // print('----${data.sublist(i, i + mtu - 4)}');
//     //     } else {
//     //       await _doWrite(data.sublist(i));
//     //       // print('----${data.sublist(i)}');
//     //     }
//     //   }
//     // }else {
//     //   _doWrite(data);
//     // }
//     _doWrite(data);
//   }
//
//   Future _doWrite(List<int> data) async {
//     await Future.delayed(const Duration(milliseconds: 100));
//     _writeCha?.write(data).then((value) {
//       QLog('蓝牙写出数据: $value');
//     }, onError: (e) {
//       QLog('蓝牙写出异常: $e');
//
//       ///触发写异常，返回0xFFFF
//       _respBleDataController.add([0xff, 0xff]);
//     });
//   }
//
//   /// 权限检查,含弹窗
//   /// 1. 检查手机蓝牙按钮是否开启
//   /// 2. 检查蓝牙所需权限是否允许
//   Future<bool> permissionCheck() async {
//     Completer<bool> c = Completer();
//     bool f = true;
//
//     ///先判断权限，再判断蓝牙开关
//
//     if (GetPlatform.isIOS) {
//       var p = await Permission.bluetooth.request();
//       if (!p.isGranted) {
//         EasyLoading.showToast('蓝牙被拒绝使用');
//         c.complete(false);
//       }
//     } else {
//       var perList = [
//         Permission.bluetoothConnect,
//         Permission.location,
//         Permission.bluetoothScan,
//         Permission.bluetoothAdvertise
//       ];
//       var b = await PermissionManager.hasPermissionGranted(perList);
//       if (!b) {
//         await PermissionManager.questPermsInfoDialog(
//             title: '开启蓝牙权限',
//             msg: '''
//             正在请求使用您的 蓝牙 权限，以便您通过蓝牙与设备进行交互。
//             如您关闭或拒绝权限调取，将导致上述功能无法正常使用，但不影响使用本应用的基本功能。具体请参阅''',
//             onConfirm: () async {
//               return PermissionManager.requestPermission(perList).then((p) {
//                 var title = '蓝牙权限被拒绝,请打开蓝牙后重试';
//                 for (var p in p.entries) {
//                   f &= p.value.isGranted;
//                   if (p.key == Permission.location && !p.value.isGranted) {
//                     title = '手机定位权限未开启，请前往【设置】开启定位权限';
//                   }
//                 }
//                 if (!f) {
//                   PermissionManager.sysSettingDialog(
//                       onConfirm: () => {
//                             //todo
//                           },
//                       title: title);
//                   c.complete(false);
//                 }
//               });
//             },
//             onCancel: () async=> {c.complete(false)});
//       }
//     }
//
//     if (c.isCompleted) {
//       return c.future;
//     }
//
//     // Log.i('蓝牙权限开');
//     f = true;
//
//     await Future.wait<bool>([blue.isOn, blue.isAvailable]).then((value) {
//       for (bool b in value) {
//         f &= b;
//       }
//     });
//     if (!f) {
//       EasyLoading.showToast('蓝牙功能未打开');
//
//       ///蓝牙开关没有打开，提醒用户跳转至设置页面开启蓝牙
//       // if (GetPlatform.isAndroid) {
//       //   await PermissionManager.questPermsInfoDialog(
//       //       title: str.permissionBluetoothTitle.tr,
//       //       msg: str.permissionBluetoothHint
//       //           .trParams({'name': str.appName.tr}),
//       //       onConfirm: () async {
//       //         PermissionManager.sysSettingDialog(
//       //           onConfirm: () => AppSettings.openBluetoothSettings(),
//       //           title: str.androidBluetoothSettingHint.tr,
//       //         );
//       //         c.complete(false);
//       //       },
//       //       onCancel: () => {c.complete(false)});
//       // } else {
//       //   c.complete(false);
//       // }
//       c.complete(false);
//     }
//
//     if (!c.isCompleted) {
//       c.complete(f);
//     }
//     return c.future;
//   }
//
//   /// 单纯检查蓝牙权限是否满足
//   Future<bool> blePermGranted() async {
//     Completer<bool> c = Completer();
//     if (GetPlatform.isIOS) {
//       var b =
//           await PermissionManager.hasPermissionGranted([Permission.bluetooth]);
//       c.complete(b);
//     } else {
//       var perList = [
//         Permission.bluetoothConnect,
//         Permission.location,
//         Permission.bluetoothScan,
//         Permission.bluetoothAdvertise
//       ];
//       var b = await PermissionManager.hasPermissionGranted(perList);
//
//       await Future.wait<bool>([blue.isOn, blue.isAvailable]).then((value) {
//         for (bool bl in value) {
//           b &= bl;
//         }
//       });
//       c.complete(b);
//     }
//     return c.future;
//   }
//
//   Future<List<BluetoothDevice>> connectDeviceList() async{
//     return await blue.connectedDevices;
//   }
//
//   ///断开连接后,注销资源
//   void reset() async {
//     await _stateSpt?.cancel();
//     _stateSpt = null;
//     await _readSpt?.cancel();
//     _readSpt = null;
//     mDevice = null;
//     _readCha = null;
//     _writeCha = null;
//   }
//
//   ///Uint8List 转 hexString
//   String uint8ToHex(Uint8List byteArr) {
//     if (byteArr.length == 0) {
//       return "";
//     }
//     Uint8List result = Uint8List(byteArr.length << 1);
//     var hexTable = [
//       '0',
//       '1',
//       '2',
//       '3',
//       '4',
//       '5',
//       '6',
//       '7',
//       '8',
//       '9',
//       'A',
//       'B',
//       'C',
//       'D',
//       'E',
//       'F'
//     ]; //16进制字符表
//     for (var i = 0; i < byteArr.length; i++) {
//       var bit = byteArr[i]; //取传入的byteArr的每一位
//       var index = bit >> 4 & 15; //右移4位,取剩下四位
//       var i2 = i << 1; //byteArr的每一位对应结果的两位,所以对于结果的操作位数要乘2
//       result[i2] = hexTable[index].codeUnitAt(0); //左边的值取字符表,转为Unicode放进resut数组
//       index = bit & 15; //取右边四位
//       result[i2 + 1] =
//           hexTable[index].codeUnitAt(0); //右边的值取字符表,转为Unicode放进resut数组
//     }
//     return String.fromCharCodes(result); //Unicode转回为对应字符,生成字符串返回
//   }
//
//   Uint8List hexToUint8(String input) {
//     assert(input.length % 2 == 0, 'Input needs to be an even length.');
//
//     return Uint8List.fromList(
//       List.generate(
//         input.length ~/ 2,
//         (i) => int.parse(input.substring(i * 2, (i * 2) + 2), radix: 16),
//       ).toList(),
//     );
//   }
//
//   Uint8List stringToUint8(String s) {
//     var encodedString = utf8.encode(s);
//     var encodedLength = encodedString.length;
//     var data = ByteData(encodedLength);
//     data.setUint32(0, encodedLength, Endian.big);
//     var bytes = data.buffer.asUint8List();
//     bytes.setRange(0, encodedLength, encodedString);
//     return bytes;
//   }
//
//   //int ---> hex
//   String intToHex(int num) {
//     String hexString = num.toRadixString(16).padLeft(4, '0');
//     return hexString;
//   }
// }
//
// /// BluetoothDevice 的拓展方法
// extension advertise on BluetoothDevice {
//   static final _name = Expando<String>();
//   static final _mac = Expando<String>();
//
//   String get localName => _name[this] ?? '';
//
//   set localName(String v) => _name[this] = v;
//
//   String get macAddress => _mac[this] ?? '';
//
//   set macAddress(String v) => _mac[this] = v;
// }
//
// ///网关数据解析
// class DataParser {
//   String DataToJson(Uint8List data) {
//     return utf8.decode(data.toList());
//   }
//
//   JsonToU8List(String data) {
//     return Uint8List.fromList(utf8.encode(data));
//   }
// }
