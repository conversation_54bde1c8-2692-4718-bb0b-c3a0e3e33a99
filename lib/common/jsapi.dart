
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:lima/common/ble/permissions.dart';
import 'package:lima/common/bridge_controller.dart';
import 'package:lima/common/config.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/utils/q_log.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../global.dart';
import '../utils/jsbridge/message.dart';

/// JsBridge api
class JSApi {
  JSApi._();

  ///https://lierda.feishu.cn/docx/GhTFdVPBuoGzbsxVCpxcJ6Lfnnh
  static const String helloApp = 'helloApp';
  static const String chooseImage = 'chooseImage'; //选择图片
  static const String qrCode = 'qrCode';//二维码
  static const String token = 'token';
  static const String location = 'queryLocation';//定位
  static const String bleScan = 'scan';//蓝牙扫描
  static const String bleStopScan = 'stopScan';
  static const String bleConnectDevice = 'connectDevice';
  static const String bleDisconnect = 'disconnectDevice';
  static const String bleSendData = 'sendBleData';
  static const String createBond = 'createBond';//发起绑定
  static const String safeArea = 'safeArea';
  static const String gtCid = 'getuiCid';
  static const String userInfo = 'userInfo';
  static const String qqLogin = 'qqLogin';
  static const String wxLogin = 'wxLogin';
  static const String appleLogin = 'appleLogin';
  static const String amapLauncher = 'amapLauncher';
  static const String txLauncher = 'txLauncher';
  static const String bdLauncher = 'bdLauncher';
  static const String clearAppCache = 'clearAppCache';//清除app内的token等信息
  static const String getCacheInfo = 'getCacheInfo';//获取缓存信息
  static const String clearCache = 'clearCache';//清除指定类型缓存
  static const String webMsgApi = 'webMsgApi';//web端数据转存至本地的crud操作
  static const String wxPay = 'wxPay';//微信支付  https://github.com/RxReader/wechat_kit/blob/master/example/lib/main.dart
  static const String wxShare = 'wxShare';
  static const String phoneCall = 'phoneCall';
  static const String aliPay = 'aliPay';
  static const String webBrowse = 'webBrowse';
  static const String navPop = 'navPop';
  static const String statusBarColor = 'statusBarColor';
  static const String saveImgInLocal = 'saveImgInLocal';
  static const String appUpdate = 'appUpdate';
  static const String launchDetailH5 = 'launchDetailH5';
  static const String bleConnectList = 'bleConnectList'; /// 蓝牙已经连接的列表




  static final List _callbackApi = <String>[];
  static String  aliPayCbId = '';

  static String bleConnRespId = ''; // 连接蓝牙的回复ID

  ///解析前端过来的请求
  static Future parseJsRequest(dynamic data) async {
    try {
      Message message = messageFromJson(data);

      // 检查是否是old系统的登录请求，如果是则忽略，让H5自己处理
      if (message.data is Map && message.data['action'] == 'login') {
        QLog('Old系统检测到登录请求，忽略处理，让H5自己处理登录');
        return {'success': false, 'message': '请使用H5页面登录'};
      }

      if (message.api == qrCode) {
        return await JsBridgeController.instance.scanQrCode(data: message.data);
      }
      if (message.api == token) {
        return await JsBridgeController.instance
            .updateToken(token: message.data);
      }

      if (message.api == location) {
        if (Get.currentRoute == '/SplashPage') {
          ResponseModel response = ResponseModel(code: '500');
          return response.toJson();
        }
        bool b = message.data == null
            ? false
            : message.data['singleLocate'] ?? false;
        var res =
            await JsBridgeController.instance.getLocation(b).catchError((e) {
          QLog('onError，$e');
        });
        ResponseModel response = ResponseModel();
        if (res == null) {
          response.code = '500';
        } else {
          response.code = '200';
          response.data = res;
          response.msg = '';
        }
        return response.toJson();
      }

      if (message.api == bleScan) {
        if (message.data['timeout'] != null) {
          JsBridgeController.instance
              .bleStartScan(message.data['timeout'] ?? 30);
        }
      }
      if (message.api == bleStopScan) {
        JsBridgeController.instance.bleStopScan();
      }
      if (message.api == createBond) {
        JsBridgeController.instance.createBond();
      }

      if (message.api == bleConnectDevice) {
        var token = await Global.getToken();
        if (Get.currentRoute == '/SplashPage' && token == null) {
          ResponseModel response = ResponseModel(code: '500');
          return response.toJson();
        }
        if (message.callbackId != null) {
          bleConnRespId = '${message.callbackId}';

          _callbackApi.add(message.callbackId!);
        }

        if (message.data == null ||
            message.data['deviceMac'] == null ||
            message.data['serviceUid'] == null ||
            message.data['writeUid'] == null ||
            message.data['notifyUid'] == null) {
          ResponseModel response = ResponseModel();
          response.code = '400';
          response.msg = '请求参数有误';
          bleConnectCallback(data: response.toJson());
        } else {
          JsBridgeController.instance.connectDevice(message.data);
        }
        ///返回一个 -1表示结果不需要回调给js
        return -1;
      }
      if (message.api == bleDisconnect) {
        JsBridgeController.instance.disconnect();
      }

      if (message.api == bleSendData) {
        if (message.data == null) {
          return '数据不能为空';
        }
        JsBridgeController.instance.sendData(message.data);
      }
      if (message.api == safeArea) {
        var top = Get.find<HomeLogic>().safeAreaTop;
        var bottom = Get.find<HomeLogic>().safeAreaBottom;
        return {
          'top': top,
          'bottom': bottom,
          'platform': GetPlatform.isAndroid ? 'Android' : 'iOS'
        };
      }

      if (message.api == gtCid) {
        var sp = await SharedPreferences.getInstance();
        return sp.getString(Config.GT_CID);
      }

      if (message.api == userInfo) {
       return await JsBridgeController.instance.updateUserInfo(userInfo: message.data);
      }

      if (message.api == wxLogin) {
        var r = await JsBridgeController.instance.wxAuth();
        QLog('微信授权结果:$r');
        if (r == -1) {
          EasyLoading.showToast('请先安装微信客户端');
          return null;
        }
        return r;
      }
      if(message.api == qqLogin){
        // return await JsBridgeController.instance.qqLogin();
      }
      if (message.api == 'appleLogin') {
        final credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );
        QLog('苹果授权结果:$credential');
        var nickname = '';
        if (credential.givenName != null && credential.familyName != null) {
          nickname = '${credential.givenName}${credential.familyName}';
        }
        return {
          'nickname': nickname,
          'appleOpenid': credential.userIdentifier,
          'type': 3,
        };
      }
      if(message.api == amapLauncher){
        ///跳转高德地图
        var url = '${GetPlatform.isAndroid?'android':'ios'}amap://navi?sourceApplication=amap&lat=${message.data['lat']}&lon=${message.data['lon']}&dev=0&style=2';
        if(GetPlatform.isIOS){
          url = Uri.encodeFull(url);
        }
        try {
         var res = await launchUrl(Uri.parse(url)).catchError((e) {
            EasyLoading.showToast('未安装高德地图');
          });
          if (!res) {
            EasyLoading.showToast('未安装高德地图');
          }
        }catch (e) {
          EasyLoading.showToast('未安装高德地图');
        }
        return ;
      }

      if(message.api == txLauncher){
        var url = 'qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&tocoord=${message.data['lat']},${message.data['lon']}&policy=0';
        if(GetPlatform.isIOS){
          url = Uri.encodeFull(url);
        }
        try {
          var res = await launchUrl(Uri.parse(url)).catchError((e) {
            EasyLoading.showToast('未安装腾讯地图');
          });
          if (!res) {
            EasyLoading.showToast('未安装腾讯地图');
          }
        }catch (e) {
          EasyLoading.showToast('未安装腾讯地图');
        }
        return ;
      }
      if(message.api == bdLauncher){
        var url = 'baidumap://map/direction?destination=${message.data['lat']},${message.data['lon']}&coord_type=gcj02&mode=driving';
        if(GetPlatform.isIOS){
          url = Uri.encodeFull(url);
        }
        try {
          var res = await launchUrl(Uri.parse(url)).catchError((e) {
            EasyLoading.showToast('未安装百度地图');
          });
          if (!res) {
            EasyLoading.showToast('未安装百度地图');
          }
        }catch (e) {
          EasyLoading.showToast('未安装百度地图');
        }

        return ;
      }
      if(message.api == chooseImage){
        var type = message.data['type'] ?? 0;
        return await JsBridgeController.instance.chooseImg(type);
      }
      if (message.api == clearAppCache) {
        var list = message.data;
        if (list is List) {
          for (var item in list) {
            if (item == 'token') {
              QLog('清除App token');
              await Global.removeToken();
            }
            if (item == 'userInfo') {
              QLog('清除App 用户信息');
              await Global.removeUserInfo();
            }
          }
        }
        return true;
      }
      if (message.api == webMsgApi) {
        if (message.data == null || message.data['type'] == null) {
          return null;
        }
        var sp = await SharedPreferences.getInstance();
        if (message.data['type'] == 'add') {
          if (message.data['data'] == null || message.data['key'] == null) {
            return null;
          }
          sp.setString('msg_${message.data['key']}', message.data['data']);
        }
        if (message.data['type'] == 'get') {
          if (message.data['key'] == null) {
            return null;
          }
          return sp.getString('msg_${message.data['key']}');
        }
        if (message.data['type'] == 'remove') {
          if (message.data['key'] == null) {
            return null;
          }
          await sp.remove('msg_${message.data['key']}');
          return true;
        }
        return null;
      }
      if(message.api == wxShare){
        var r = await JsBridgeController.instance.wechatShare(message.data);
        if(r == -1){
          EasyLoading.showToast('请先安装微信客户端');
          return null;
        }
        return r;
      }
      if(message.api == aliPay){
        return await JsBridgeController.instance.aliPay(message.data);
      }
      if(message.api == wxPay){
        return await JsBridgeController.instance.wxPay(message.data);
      }
      ///拨号
      if(message.api == phoneCall){
        if(message.data!=null) {
          launchUrl(Uri(
            scheme: "tel",
            path: '${message.data}',
          ));
        }
      }
      if(message.api == webBrowse){
        if (message.data != null && message.data['offlinePre'] == true) {
          launchUrl(Uri.parse(message.data['url']));
        } else {
          Get.to(() => WebviewPage(), arguments: message.data);
        }
      }
      if(message.api == navPop){
        QLog('当前路由 ${Get.currentRoute}');
        if(Get.currentRoute != AppRoute.home){
          Get.back();
        }
      }
      if (message.api == statusBarColor) {
        if (message.data != null) {
          if (message.data['color'] == 'light') {
            Get.find<HomeLogic>().tabValue.value = SystemUiOverlayStyle.light;
          } else if (message.data['color'] == 'dark') {
            Get.find<HomeLogic>().tabValue.value = SystemUiOverlayStyle.dark;
          }
        }
      }
      if(message.api == saveImgInLocal){
        var l = await checkForPerm([Permission.storage], '开启存储权限', '我们需要请求使用您的 存储 权限，以便您能存储图片。');
        if(l == false){
          return;
        }

        if(message.data!=null){
          var l = message.data['imgUrl'];
          if(l!=null){
            EasyLoading.show();
            var fileName = l.split('/').last;
            var localPath = await getApplicationDocumentsDirectory();
            var savePath = '${localPath.path}/imgAssets';
            var saveDir = Directory(savePath);
            if (!saveDir.existsSync()) {
              saveDir.createSync();
            }else{
              if(saveDir.listSync().isNotEmpty){
                QLog('存在已缓存的image文件');
                ///清除本地已经缓存的ota文件
                var listSync = saveDir.listSync();
                for (var element in listSync) {
                  if (element is File) {
                    element.deleteSync();
                  }
                }
              }
            }
            var res = await Dio().download(l, '$savePath/$fileName',
                onReceiveProgress: (received, total) {
                }).catchError((e) {
              QLog('下载$l 失败,${e.toString()}');
              return e;
            });
            final result = await ImageGallerySaver.saveFile('$savePath/$fileName');
            // {filePath: file:///storage/emulated/0/Pictures/1709521944560.png, errorMessage: null, isSuccess: true}
            QLog(result);
            if(result is Map && result['isSuccess'] == true){
              EasyLoading.showSuccess('存储成功');
            }else{
              EasyLoading.showError('存储失败');
            }
          }
        }
      }
      if (message.api == appUpdate) {
        if (message.data != null) {
          var url = message.data['url'];
          var fileSize = message.data['fileSize'];
          if(url!=null){
            if(GetPlatform.isIOS){
              launchUrl(Uri.parse(url));
            }else{
             JsBridgeController.instance.downloadOAPKFile(path: url,fileSize: fileSize);
            }
          }
        }
      }
      if (message.api == launchDetailH5) {
        if (message.data != null) {
          var url = message.data['url'];
          var path = message.data['path'];
          if (path != null && url == null) {
            url = Config.BASE_WEBSITE + path;
          }
          var popPage = message.data['willPopPage'] ?? false;
          await Get.find<HomeLogic>().myWebController?.loadUrl(url);
          if (popPage == true) {
            Get.back();
          }
        }
      }
      if(message.api == bleConnectList){
        return await JsBridgeController.instance.getConnectedBleList();
      }

      if (message.api == getCacheInfo) {
        return await JsBridgeController.instance.getCacheInfo(message.data);
      }

      if (message.api == clearCache) {
        return await JsBridgeController.instance.clearCache(message.data);
      }

      if (message.api == helloApp) {
        return 'helloJs';
      }
    } catch (e) {
      e.printError();
      return null;
    }
  }

  ///蓝牙回复回调
  static void bleConnectCallback({dynamic data, bool? nativeResponseFlag}) {
    // for(int i = 0; i < _callbackApi.length; i++){
    //   var item = _callbackApi[i];
    //   if (item.contains('connectDevice')) {
    //     Get.find<HomeLogic>().myWebController?.sendMessageToJs(item,
    //         data: data, nativeResponseFlag: nativeResponseFlag);
    //     _callbackApi.removeWhere((element) => element == item);
    //   }
    // }
    QLog('蓝牙connect的回调: ${bleConnRespId}');
    Get.find<HomeLogic>().myWebController?.sendMessageToJs(bleConnRespId,
        data: data, nativeResponseFlag: nativeResponseFlag);
  
    
  }

  static void normalConnectCallback({dynamic cbId,dynamic data, bool? nativeResponseFlag}) {
    if(cbId == null || cbId == ''){
      return;
    }
    for(int i = 0; i < _callbackApi.length; i++){
      var item = _callbackApi[i];
      if (item.contains(cbId)) {
        Get.find<HomeLogic>().myWebController?.sendMessageToJs(item,
            data: data, nativeResponseFlag: nativeResponseFlag);
        _callbackApi.removeWhere((element) => element == item);
      }
    }
  }

  static Future? sendMessage(String api,
      {dynamic data, bool? nativeResponseFlag}) {
    return Get.find<HomeLogic>().myWebController?.sendMessageToJs(api,
        data: data, nativeResponseFlag: nativeResponseFlag);
  }

  static Future<bool> checkForPerm(
      List<Permission> p, String title, String msg) async {
    var l = await PermissionManager.hasPermissionGranted(p);
    if (!l) {
      var dialogConfirm = await PermissionManager.questPermsInfoDialog(
        title: title,
        msg: msg,
      );
      if (dialogConfirm == true) {
        await PermissionManager.requestPermission(p);
        return await PermissionManager.hasPermissionGranted(p);
      }else{
        return false;
      }
    }
    return true;
  }
}
