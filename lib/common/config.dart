// ignore_for_file: non_constant_identifier_names, constant_identifier_names

class Config{
  Config._();
  /// app地址
  ///

  static String base_website_test = 'https://h5-lima.newtest.senthink.com';// 测试环境
  static String base_website_prod = 'https://new-h5.limajituan.com'; // 正式环境

  static String BASE_WEBSITE = ENV == EnvType.TEST? base_website_test: base_website_prod;

  ///应用合规弹窗
  static const String APP_PERMISSION = 'appPermission';
  ///个推的CID
  static const String GT_CID = 'gtCid';

  static const String TOKEN = 'token';

  static const String LIMA_TOKEN = 'lima_token';

  static const String USER_INFO = 'userInfo';

  ///请求url - 测试
  // static String base_url_test = 'https://new-gateway-lima.newtest.senthink.com/';
  // static String base_url_test = 'https://h5.lima.lightcloudapps.com';
  static String base_url_test = 'http://192.168.31.214:8080';
  static String base_img_url_test = 'https://new-gateway-lima.newtest.senthink.com/mars/static';

  /// 请求url - 正式
  static String base_url_prod = 'https://new-gateway.limajituan.com/';
  static String base_img_url_prod = 'https://new-gateway.limajituan.com/mars/static';


  static String BASE_URL = ENV == EnvType.TEST? base_url_test : base_url_prod;
  static String BASE_IMG_URL = ENV == EnvType.TEST? base_img_url_test : base_img_url_prod;

  ///隐私政策
  
  static String user_protocol_test = 'https://h5-lima.newtest.senthink.com/me/about/privacy?nav=false';
  
  static String user_protocol_prod = 'https://new-h5.limajituan.com/me/about/privacy?nav=false';

  static String USER_PROTOCOL = ENV == EnvType.TEST? user_protocol_test : user_protocol_prod;


  /// 环境开关
  static const ENV = EnvType.TEST;

}

enum EnvType { PROD, TEST, }