import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:alipay_kit/alipay_kit.dart';
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:lima/common/ble/ble_plus.dart';
import 'package:lima/common/config.dart';
import 'package:lima/common/jsapi.dart';
import 'package:lima/global.dart';
import 'package:lima/routers/app_router.dart';
import 'package:get/get.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/mqtt.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/utils.dart';
import 'package:lima/utils/wxkit.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:encrypt/encrypt.dart' as en;
import 'package:wakelock_plus/wakelock_plus.dart';

import 'ble/permissions.dart';
import '../utils/cache_manager.dart';

class JsBridgeController {
  String kWechatAppID = 'wxd92e6f7ab4454e9d';
  String kWechatUniversalLink = 'your wechat universal link'; // iOS 请配置
  String kWechatAppSecret = '87681e9868ee3d3ca66b14ebfed6f122';
  String kWechatMiniAppID = 'your wechat miniAppId';
  bool blePairKeyMoment = false; // 是否正在鉴权
  int blePrivateKey = 0;

  JsBridgeController._() {
    Global.getBleScanResult().then((value) {
      if (value.isNotEmpty) {
        try {
          scanResults.addAll(value as Iterable<ScanResult>);
        } catch (e) {
          QLog('$e');
        }
      }
    });
  }

  static JsBridgeController instance = JsBridgeController._();

  factory JsBridgeController() {
    return instance;
  }

  StreamSubscription? _locationSubscription;

  ///蓝牙
  final blePlus = BlePlus();
  BluetoothInstance? curBleInstance; // 当前连接的蓝牙设备
  StreamSubscription? bleStreamSubscription, bleState, bleDataSub, _connSub;
  final _cmdSubject = BehaviorSubject();
  List<ScanResult> scanResults = <ScanResult>[];

  /// 扫描到的设备列表

  ///扫码
  Future scanQrCode({dynamic data}) async {
    var res;
    var granted = true;
    if (!await Permission.camera.isGranted && GetPlatform.isAndroid) {
      await PermissionManager.questPermsInfoDialog(
          title: '开启相机权限',
          msg: '正在请求使用您的 相机 权限，以便您能使用扫描二维码等功能。',
          onConfirm: () async {
            granted = true;
            return;
          },
          onCancel: () async {
            granted = false;
          });
      // QLog('相机权限: $granted');
    }
    if (granted) {
      if (data != null) {
        res = await Get.toNamed(AppRoute.scanQr, arguments: data);
      } else {
        res = await Get.toNamed(
          AppRoute.scanQr,
        );
      }
    } else {
      ResponseModel response = ResponseModel(code: '500', msg: '用户权限拒绝');
      return response.toJson();
    }
    ResponseModel response = ResponseModel();
    if (res == null) {
      response.code = '500';
      response.msg = '用户取消';
    } else {
      if (res is Map) {
        if (res['code'] == 301) {
          response.code = '301';
          response.msg = 'inputBarcode';
        }
      } else {
        response.code = '200';
        response.data = res;
        response.msg = '';
      }
    }
    return response.toJson();
  }

  Future updateToken({String? token}) async {
    var sp = await SharedPreferences.getInstance();
    if (token == null || token.isEmpty) {
      return sp.getString(Config.TOKEN);
    } else {
      sp.setString(Config.TOKEN, token);
      return token;
    }
  }

  Future updateUserInfo({dynamic userInfo}) async {
    var sp = await SharedPreferences.getInstance();
    if (userInfo != null) {
      await sp.setString(Config.USER_INFO, jsonEncode(userInfo));

      ///更新mq
      MQTT().reconnectAct();

      return userInfo;
    } else {
      var res = sp.getString(Config.USER_INFO);
      if (res != null) {
        return jsonDecode(res);
      } else {
        return null;
      }
    }
  }

  Future getLocation(bool singleLocate) async {
    Completer completer = Completer();

    if (GetPlatform.isAndroid) {
      var perms = [
        Permission.location,
      ];
      var b = await PermissionManager.hasPermissionGranted(perms);
      if (!b) {
        await PermissionManager.questPermsInfoDialog(
            title: '开启定位权限',
            msg: '立马科技需要开启您的“位置”权限获取您的位置信息、轨迹信息，用以支持寻车定位、感应解锁、记录轨迹等功能。',
            onConfirm: () async {
              return PermissionManager.requestPermission(perms).then((p) {
                var f = true;
                for (var p in p.entries) {
                  f &= p.value.isGranted;
                }
                if (!f) {
                  completer.completeError('没有定位权限');
                }
              });
            },
            onCancel: () async {
              completer.completeError('没有定位权限');
            });
      }
    }
    if (completer.isCompleted) {
      return completer.future;
    }

    if (_locationSubscription != null) {
      _locationSubscription?.cancel();
      _locationSubscription = null;
    }

    AMapFlutterLocation _locationPlugin = AMapFlutterLocation();
    AMapLocationOption locationOption = AMapLocationOption();

    ///是否单次定位
    locationOption.onceLocation = singleLocate;

    ///是否需要返回逆地理信息
    locationOption.needAddress = true;

    ///逆地理信息的语言类型
    locationOption.geoLanguage = GeoLanguage.DEFAULT;

    locationOption.desiredLocationAccuracyAuthorizationMode =
        AMapLocationAccuracyAuthorizationMode.ReduceAccuracy;

    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";

    ///设置Android端连续定位的定位间隔
    locationOption.locationInterval = 2000;

    ///设置Android端的定位模式<br>
    ///可选值：<br>
    ///<li>[AMapLocationMode.Battery_Saving]</li>
    ///<li>[AMapLocationMode.Device_Sensors]</li>
    ///<li>[AMapLocationMode.Hight_Accuracy]</li>
    locationOption.locationMode = AMapLocationMode.Hight_Accuracy;

    ///设置iOS端的定位最小更新距离<br>
    locationOption.distanceFilter = -1;

    ///设置iOS端期望的定位精度
    /// 可选值：<br>
    /// <li>[DesiredAccuracy.Best] 最高精度</li>
    /// <li>[DesiredAccuracy.BestForNavigation] 适用于导航场景的高精度 </li>
    /// <li>[DesiredAccuracy.NearestTenMeters] 10米 </li>
    /// <li>[DesiredAccuracy.Kilometer] 1000米</li>
    /// <li>[DesiredAccuracy.ThreeKilometers] 3000米</li>
    locationOption.desiredAccuracy = DesiredAccuracy.Best;

    ///设置iOS端是否允许系统暂停定位
    locationOption.pausesLocationUpdatesAutomatically = false;

    ///将定位参数设置给定位插件
    _locationPlugin.setLocationOption(locationOption);

    _locationSubscription = _locationPlugin.onLocationChanged().listen((event) {
      if (completer.isCompleted) {
        return;
      }
      if (!event.containsKey('latitude')) {
        completer.completeError('定位失败');
        return;
      }
      QLog('获取到定位信息>:  $event ');

      completer.complete({
        'latitude': event['latitude'],
        'longitude': event['longitude'],
        'address': event['address'],
        'province': event['province'],
        'city': event['city'],
      });
    });

    Future.delayed(const Duration(milliseconds: 15000), () {
      if (!completer.isCompleted) {
        completer.completeError('定位失败');
      }
    });

    _locationPlugin.startLocation();

    return completer.future.whenComplete(() {
      _locationPlugin.stopLocation();
      _locationPlugin.destroy();
      _locationSubscription?.cancel();
      _locationSubscription = null;
    });
  }

  /// 搜索蓝牙
  /// [deviceMac] :需要匹配的目标设备mac
  /// [deviceName] : 蓝牙名称
  Future bleStartScan(int duration,
      {String? deviceMac, String? serviceUid, String? deviceName}) async {
    Completer<BluetoothDevice?>? completer;
    if (deviceMac != null) {
      completer = Completer();
    }
    if (await blePlus.permissionCheck() == false) {
      if (GetPlatform.isIOS) {
        EasyLoading.showToast('请先打开蓝牙');
        return;
      }

      /// 请求权限
      bool f = await PermissionManager.questPermsInfoDialog(
          title: '权限申请说明',
          msg: '立马科技需要使用您的 蓝牙 权限，以便您可以使用蓝牙搜索周边设备。',
          onConfirm: () async =>
              await blePlus.permissionCheck(needRequest: true),
          onCancel: () async => false);
      if (!f) {
        return;
      }
    }
    scanResults.clear();
    bleStreamSubscription?.cancel();

    if (blePlus.isScanning) {
      await blePlus.stopScan();
    }

    /// 检查手机系统是否已经连接了目标蓝牙
    if (deviceName != null) {
      List<BluetoothDevice> l = GetPlatform.isAndroid
          ? await blePlus.getBondedDevice()
          : await blePlus.getSysConnectedDevice();

      QLog('系统已连接蓝牙设备列表: ${l.toString()}');

      for (BluetoothDevice d in l) {
        if (Utils.StringEqual(d.platformName, deviceName)) {
          QLog('msg: 手机系统已经连接了目标蓝牙: ${d.platformName}');
          return d;
        }
      }
    }

    bleStreamSubscription = blePlus.scanResults().listen((event) {
      if (event.isNotEmpty) {
        for (ScanResult r in event) {
          QLog('搜索到的蓝牙: ${r}');
          if (r.device.platformName.isEmpty && r.device.advName.isEmpty) {
            continue;
          }
          scanResults.addOrUpdate(r);

          if (deviceMac != null) {
            String? tempMac = r._getMac();
            if (tempMac == null) {
              continue;
            }
            if (Utils.StringEqual(deviceMac, tempMac)) {
              QLog(
                  '目标设备: $deviceMac, 蓝牙设备:${r.device.platformName} , 广播: $tempMac');
              bleStreamSubscription?.cancel();
              if (completer?.isCompleted == false) {
                completer?.complete(r.device);
              }
            }
            return;
          }

          var map = r.advertisementData.manufacturerData.map((key, value) =>
              MapEntry(blePlus.intToHex(key), blePlus.hexEncode(value)));
          var manuList = [];
          for (var item in map.entries) {
            manuList.add({'key': item.key, 'value': item.value});
          }
          var res = {
            'deviceName': r.device.advName,
            'deviceId': r.device.remoteId.str,
            'advertisementData': {
              'manufacturerData': manuList,
            }
          };
          JSApi.sendMessage('scanResult', data: res);
        }
      }
    });

    if (bleStreamSubscription != null) {
      FlutterBluePlus.cancelWhenScanComplete(bleStreamSubscription!);
    }

    blePlus.scan(
      // timeout: Duration(seconds: duration),
      withServices: serviceUid == null ? [] : [Guid(serviceUid), Guid('1812')],
    );

    bleStreamSubscription?.asFuture().timeout(Duration(seconds: duration),
        onTimeout: () {
      if (completer != null && completer.isCompleted == false) {
        completer.complete(null);
      }
    });

    if (deviceMac != null) {
      return completer?.future;
    }
  }

  Future bleStopScan() async {
    bleStreamSubscription?.cancel();
    return blePlus.stopScan();
  }

  Future createBond() async {
    if (curBleInstance == null) {
      return;
    }
    return blePlus.createBond(bluetoothInstance: curBleInstance!);
  }

  Future connectDevice(dynamic connectConfig) async {
    String? deviceMac = connectConfig['deviceMac'];
    String _serviceUid = connectConfig['serviceUid'];
    String _wirteUid = connectConfig['writeUid'];
    String _notifyUid = connectConfig['notifyUid'];
    String? deviceName = connectConfig['deviceName'];

    // if (blePlus.getConnectedDevice(mac: '$deviceMac'.replaceAll(':', '')) !=
    //     null) {
    //   JSApi.bleConnectCallback(
    //       data: ResponseModel(code: '500', msg: '蓝牙已经连接').toJson());
    //   return;
    // }
    // scanResults.clear();

    BluetoothDevice? cDevice;

    if (FlutterBluePlus.lastScanResults.isNotEmpty) {
      QLog('已搜索过的蓝牙列表: ${FlutterBluePlus.lastScanResults}');
      scanResults = FlutterBluePlus.lastScanResults;
    }

    int idx = scanResults.indexWhere((element) {
      // QLog('广播名: ${element.advertisementData.advName}, 蓝牙广播mac: ${element._getMac()}');
      return Utils.StringEqual(
          element._getMac(), '$deviceMac'.replaceAll(":", ''));
    });

    if (idx != -1) {
      cDevice = scanResults[idx].device;
    }

    if (scanResults.isEmpty || idx == -1) {
      /// 先搜索蓝牙,如果找到了目标设备就立即退出搜索
      cDevice = await bleStartScan(30,
          deviceMac: '$deviceMac'.replaceAll(':', ''),
          deviceName: deviceName,
          serviceUid: _serviceUid);
    }

    if (cDevice == null) {
      idx = scanResults.indexWhere((element) => Utils.StringEqual(
          element._getMac(), '$deviceMac'.replaceAll(":", '')));
      if (idx == -1) {
        JSApi.bleConnectCallback(
            data: ResponseModel(code: '404', msg: '没有找到目标设备,请重新搜索蓝牙').toJson());
        scanResults.clear();
        return;
      }
      cDevice = scanResults[idx].device;
    }

    BluetoothInstance bluetoothInstance = BluetoothInstance(
        device: cDevice,
        write_uid: _wirteUid,
        read_uid: _notifyUid,
        service_uid: _serviceUid,
        mac: '$deviceMac');
    if (blePlus.isScanning) {
      await blePlus.stopScan();
    }
    _connSub?.cancel();

    _connSub = bluetoothInstance.device.connectionState.listen((event) async {
      QLog('蓝牙状态变更: $event');
      if (event == BluetoothConnectionState.connected) {
        curBleInstance = bluetoothInstance;
        if (GetPlatform.isAndroid) {
          await Future.delayed(Duration(milliseconds: 500));
        }

        /// 发现服务
        await blePlus.saveReadAndWrite(bluetoothInstance: bluetoothInstance);

        ///
        await registerNotify();

        /// 将本次连接过的设备保存到SP
        Global.saveBleScanResult(FlutterBluePlus.lastScanResults);

        JSApi.sendMessage('bleConnectState', data: {
          'deviceName': bluetoothInstance.device.advName,
          'deviceMac': bluetoothInstance.mac,
          'state': 1
        });
      }
      if (event == BluetoothConnectionState.disconnected) {
        /// 本地缓存的蓝牙实例不为空时才做断开连接的操作
        if (curBleInstance == null) return;

        bleDataSub?.cancel();
        curBleInstance = null;
        JSApi.sendMessage('bleConnectState', data: {
          'deviceName': bluetoothInstance.device.advName,
          'deviceMac': bluetoothInstance.mac,
          'state': 0
        });
      }
    });

    await blePlus
        .connect(bluetoothInstance, mtu: GetPlatform.isAndroid ? 256 : null)
        .catchError((e) {
      JSApi.bleConnectCallback(
          data: ResponseModel(code: '500', msg: '蓝牙连接失败').toJson());
      QLog(e.toString());
    });
    bluetoothInstance.device
        .cancelWhenDisconnected(_connSub!, delayed: true, next: false);
  }

  Future registerNotify() async {
    if (curBleInstance == null) return;
    bleDataSub?.cancel();

    bleDataSub = blePlus
        .registerDataNotify(bluetoothInstance: curBleInstance!)
        ?.listen((event) {
      QLog('蓝牙回复数据-> ${blePlus.hexEncode(event)}');
      if (curBleInstance == null) return;
      _interceptBleData(event);

      ///数据全部转给前端
      JSApi.sendMessage('bleReceiveData', data: {
        'deviceName': curBleInstance?.device.advName,
        'deviceMac': curBleInstance?.mac,
        'data': blePlus.hexEncode(event),
      });
    });
  }

  ///发送蓝牙数据
  Future sendData(String data) async {
    if (data == '0102'.padRight(32, '0')) {
      blePairKeyMoment = true;
    } else {
      blePairKeyMoment = false;
    }
    return blePlus.actionBleWrite(
        data: blePlus.hexDecode(data), cha: curBleInstance?.write_c,splitWithMtu: false);
  }

  /// 发送蓝牙数据(List<int>)
  Future sendDataWithList(List<int> data) async {
    return blePlus.actionBleWrite(data: data, cha: curBleInstance?.write_c);
  }

  Future disconnect() async {
    if (curBleInstance == null) return;
    await blePlus.disconnect(device: curBleInstance!.device);
  }

  // https://stackoverflow.com/questions/60543706/flutter-image-picker-not-change-language-on-camera
  Future chooseImg(int type) async {
    if (type == 0) {
      ///拍照
      var granted = true;
      if (!(await Permission.camera.isGranted) && GetPlatform.isAndroid) {
        await PermissionManager.questPermsInfoDialog(
            title: '开启相机权限',
            msg: '正在请求使用您的 相机 权限，以便您通过相机拍摄选取图片。',
            onConfirm: () async {
              granted = true;
              return;
            },
            onCancel: () async => {granted = false});
      }
      if (granted) {
        ImagePicker picker = ImagePicker();
        XFile? image = await picker.pickImage(
          source: ImageSource.camera,
        );
        if (image != null) {
          // var base64 = base64Encode((await image.readAsBytes()).toList());
          // var res = 'data:image/png;base64,$base64';
          // QLog('长度 >>>: ' + res.length.toString());
          // QLog('内容： $res' );
          var res = await API.uploadFile(filePath: image.path);

          ///释放图片内存
          image = null;
          if (res.code == '8001') {
            return res.data['url'];
          }
          return null;
        } else {
          return null;
        }
      }
    } else {
      ImagePicker picker = ImagePicker();
      XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
      );
      if (image != null) {
        var res = await API.uploadFile(filePath: image.path);

        ///释放图片内存
        image = null;
        if (res.code == '8001') {
          return res.data['url'];
        }
        return null;
      } else {
        return null;
      }
    }
  }

  StreamSubscription<AlipayResp>? _paySubs;

  /// --- wxKit
  Future wxAuth() async {
    QLog('开始微信登录22...}');
    return wxkit.instance.wxAuth();
  }

  Future wechatShare(dynamic data) async {
    return wxkit.instance.wechatShare(data);
  }

  Future wxPay(dynamic data) async {
    return wxkit.instance.wxPay(data);
  }

  Future wxInstalled() async {
    return wxkit.instance.wxInstalled();
  }

  Future aliPayInstalled() async {
    return await AlipayKitPlatform.instance.isInstalled();
  }

  /// --- Alipay
  Future aliPay(dynamic data) async {
    await _paySubs?.cancel();
    _paySubs = null;
    Completer<dynamic> completer = Completer();

    _paySubs = AlipayKitPlatform.instance.payResp().listen((AlipayResp resp) {
      //{"alipay_trade_app_pay_response":{"code":"10000","msg":"Success","app_id":"2018091961462479","auth_app_id":"2018091961462479","charset":"utf-8","timestamp":"2024-01-10 14:13:01","out_trade_no":"atl1704866886009bhcpl6jee2hh","total_amount":"0.01","trade_no":"2024011022001410771413299545","seller_id":"2088231697539207"},"sign":"Q8nBSEyyvL6+52+4DE/zdehD90rlzaIr0yj6WrKs+WrLHpgJIlJGYWDdsXyiwj/oQp2za+vMkHa5Hv2NI9oyK/KGvzlvtOldx1q36lJbV4cerGEimKzQOiCDUnjamF+ciQjzg3EtDqMV8819uuhX7rwyIEDgcU9jU7WliML+hieOSghH+VnEsHf9k6Sqb6JogofaXD8BXQ6QE1goBtzluAVhP2YUO8nt+lLbvcxbx/7gXsZJ3IB7GokvsDQXhokCRdLCDRpjXeeywB12PUnlCoJu1qYsnmhOthUGD79UQkZ5TjWiVdKTQLNbNBs+ao+kqX9UWfIWJ57tjg0vVkIuhQ==","sign_type":"RSA2"}
      if (completer.isCompleted) {
        return;
      }
      final String content = 'pay: ${resp.resultStatus} - ${resp.result}';
      print('支付' + content);
      completer.complete({"code": resp.resultStatus, "result": resp.result});
    }, onError: (Object error, [StackTrace? stackTrace]) {
      print('支付error' + error.toString());
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(error, stackTrace);
    });
    AlipayKitPlatform.instance.pay(orderInfo: data);
    return completer.future.whenComplete(() {
      _paySubs?.cancel();
      _paySubs = null;
    });
  }

  ///仪表OTA
  ///
  ///
  Future monitorOta() async {
    var path = '6593cd8ab3cb1f000109050d';
    // var res = await API.checkOTAMission(deviceNo: '');
    // if(res.code == '8001'){
    //   if(res.data != null && res.data['meter']!=null){
    //     if(res.data['meter']['up'] == null){
    //       EasyLoading.showToast('没有查询到升级任务');
    //     }else{
    //       res = await API.downloadOTAFile(path: res.data['meter']['up']['url']);
    //       //todo
    //     }
    //   }
    // }
    var res = await API.downloadOTAFile(path: '');
    var dir = await getApplicationDocumentsDirectory();
    Directory advDir = Directory('${dir.path}/ota');
    if (advDir.existsSync()) {
      if (advDir.listSync().isNotEmpty) {
        var listSync = advDir.listSync();
        for (var element in listSync) {
          if (element is File) {
            otaData = element.readAsBytesSync();
            QLog('当前缓存的ota文件: ' + element.path);
            break;
          }
        }
        _actOTA(OTAState.startOta);
      }
    }
  }

  Future getConnectedBleList() async {
    List<BluetoothDevice> l = await blePlus.getSysConnectedDevice();
    var resList = [];
    for (BluetoothDevice d in l) {
      resList.add({
        'deviceName': d.advName,
        'deviceId': d.remoteId.str,
      });
    }
    return resList;
  }

  int otaBuffer = 128;
  int otaIndex = 0;
  Uint8List? otaData;
  ValueNotifier<bool> otaPeriod = ValueNotifier(false); // 是否正在进行ota升级

  /// 蓝牙密钥

  Future _actOTA(OTAState state) async {
    if (state == OTAState.startOta) {
      otaPeriod.value = true;
      var d = _otaCmd(0xfa, data: [0x11, 0x22]);
      sendDataWithList(_xorData(Uint8List.fromList(d)).toList());
    }
    if (state == OTAState.otaBuffer) {
      var d = _otaCmd(0xfb, data: toHHByte(otaData?.length ?? 0));
      sendDataWithList(_xorData(Uint8List.fromList(d)).toList());
    }
    if (state == OTAState.otaUpgrade) {
      //todo
    }
  }

  List<int> _otaCmd(int key, {List<int>? data}) {
    var list = <int>[];
    list.addAll([0xec, key]);
    if (data == null) {
      list.addAll([0x01, 0x00]);
    } else {
      list.add(data.length);
      list.addAll(data);
    }
    list.add(_checkSum(list.sublist(1)));
    list.add(0x68);
    return list;
  }

  int _checkSum(List<int> d) {
    int sum = 0;
    for (int i in d) {
      sum += i & 0xff;
    }
    return sum & 0xff;
  }

  Uint8List _xorData(Uint8List data) {
    for (int i = 0; i < data.length; i++) {
      data[i] ^= blePrivateKey;
    }
    return data;
  }

  List<int> toHHByte(int n) {
    List<int> b = List<int>.filled(4, 0);
    b[3] = n & 0xff;
    b[2] = (n >> 8) & 0xff;
    b[1] = (n >> 16) & 0xff;
    b[0] = (n >> 24) & 0xff;
    return b;
  }

  /// 拦截蓝牙收到的数据，对OTA数据进行拦截不分发
  /// @data: List<int>
  Future<bool> _interceptBleData(dynamic data) async {
    var d = data;
    if (blePairKeyMoment) {
      parsePrivateKey(d);
    }
    Uint8List xd = _xorData(Uint8List.fromList(d));

    if (xd[1] == 0xfa) {
      /// 屏幕常亮
      QLog('开启屏幕常亮');
      bool b = await WakelockPlus.enabled;
      if (!b) {
        WakelockPlus.enable();
      }
      return true;
    }
    if (xd[1] == 0xfd) {
      QLog('关闭屏幕常亮');
      bool b = await WakelockPlus.enabled;
      if (b) {
        WakelockPlus.disable();
      }
    }

    return false;
  }

  void parsePrivateKey(List<int> data) {
    if (data.length < 16) {
      return;
    }

    List<int> pubKey = [
      0x4c,
      0x49,
      0x4d,
      0x41,
      0x32,
      0x30,
      0x30,
      0x33,
      0x51,
      0x48
    ];
    pubKey.addAll(blePlus.hexDecode(curBleInstance!.mac.replaceAll(':', '')));

    en.Encrypter encrypter = en.Encrypter(en.AES(
      en.Key(
        Uint8List.fromList(pubKey),
      ),
      mode: en.AESMode.ecb,
      padding: null,
    ));

    en.IV _iv = en.IV.fromLength(16);

    /// 解密
    List<int> decryptData =
        encrypter.decryptBytes(en.Encrypted(Uint8List.fromList(data)), iv: _iv);
    QLog('密钥解密: ${blePlus.hexEncode(decryptData)}');

    if (decryptData.length > 4) {
      ///拿到密钥
      blePrivateKey = decryptData[3];
    } else {
      QLog('解密失败');
    }
  }

  RxString progress = ''.obs;
  Widget dialogWidget() {
    return Obx(() {
      return Container(
        constraints: BoxConstraints(
          minWidth: 40,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(children: [
          Align(
            alignment: Alignment.topRight,
            child: InkWell(
              onTap: () {
                EasyLoading.dismiss();
              },
              child: Text(
                '关闭',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12.sp,
                ),
              ),
            ),
          ),
          Center(
            child: CircularProgressIndicator(
              value: double.tryParse(progress.value) ?? 0,
            ),
          ),
          Text(
            '下载中: %s'.trArgs(['${progress.value}']),
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
            ),
          ).marginOnly(top: 15.h),
        ]),
      );
    });
  }

  ///下载App文件
  Future downloadOAPKFile({String? path, dynamic fileSize}) async {
    if (path != null) {
      var fileName = path.split('/').last;
      var localPath = await getApplicationDocumentsDirectory();
      var savePath = '${localPath.path}/app';
      var saveDir = Directory(savePath);
      if (!saveDir.existsSync()) {
        saveDir.createSync();
      } else {
        if (saveDir.listSync().isNotEmpty) {
          QLog('存在已缓存的app文件');

          ///清除本地已经缓存的ota文件
          var listSync = saveDir.listSync();
          for (var element in listSync) {
            if (element is File) {
              element.deleteSync();
            }
          }
        }
      }
      EasyLoading.show(indicator: dialogWidget());
      var downloadSuccess = true;
      var size = int.tryParse(fileSize) ?? -1;
      var res = await Dio().download(path, '$savePath/$fileName',
          onReceiveProgress: (received, total) {
        if (total != -1) {
          progress.value = (received / total * 100).toStringAsFixed(0) + "%";
        } else if (size != -1) {
          progress.value = (received / size * 100).toStringAsFixed(0) + "%";
        }
      }).catchError((e) {
        QLog('下载$path 失败,${e.toString()}');
        downloadSuccess = false;
        return e;
      });
      EasyLoading.dismiss();
      QLog(res.statusCode);
      if (downloadSuccess) {
        InstallPlugin.install('$savePath/$fileName');
      }
      return res;
    } else {
      return null;
    }
  }

  /// 获取缓存信息
  /// data: {type: 'all' | 'webview' | 'token' | 'advertisement' | 'userdata' | 'tempfiles'}
  Future<Map<String, dynamic>> getCacheInfo(dynamic data) async {
    try {
      QLog('JsBridgeController: 获取缓存信息请求: $data');

      String cacheType = 'all';
      if (data is Map && data['type'] != null) {
        cacheType = data['type'].toString();
      } else if (data is String) {
        cacheType = data;
      }

      final result = await CacheManager.getCacheInfo(cacheType);
      QLog('JsBridgeController: 缓存信息获取完成: ${result['success']}');

      return result;
    } catch (e) {
      QLog('JsBridgeController: 获取缓存信息失败: $e');
      return {
        'success': false,
        'error': '获取缓存信息失败: $e'
      };
    }
  }

  /// 清除缓存
  /// data: {type: 'all' | 'webview' | 'token' | 'advertisement' | 'userdata' | 'tempfiles'}
  Future<Map<String, dynamic>> clearCache(dynamic data) async {
    try {
      QLog('JsBridgeController: 清除缓存请求: $data');

      String cacheType = 'all';
      if (data is Map && data['type'] != null) {
        cacheType = data['type'].toString();
      } else if (data is String) {
        cacheType = data;
      }

      final result = await CacheManager.clearCache(cacheType);
      QLog('JsBridgeController: 缓存清除完成: ${result['success']} - ${result['message']}');

      return result;
    } catch (e) {
      QLog('JsBridgeController: 清除缓存失败: $e');
      return {
        'success': false,
        'error': '清除缓存失败: $e'
      };
    }
  }
}

enum OTAState { startOta, otaBuffer, otaUpgrade, otaFinish }

class ResponseModel {
  dynamic data;
  dynamic code;
  dynamic msg;

  ResponseModel({this.data, this.code, this.msg});

  ResponseModel.fromJson(Map<String, dynamic> json) {
    data = json['data'];
    code = json['code'];
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['data'] = this.data;
    data['code'] = this.code;
    data['msg'] = this.msg;
    return data;
  }
}

extension LimaMac on ScanResult {
  String? _getMac() {
    if (advertisementData.manufacturerData.isNotEmpty) {
      for (var item in advertisementData.manufacturerData.entries) {
        if (item.value.length < 6) continue;
        String hexMac = _hexEncode(item.value.sublist(0, 6));
        return hexMac.toUpperCase();
      }
    }
    return null;
  }

  String _hexEncode(List<int> numbers) {
    return numbers
        .map((n) => (n & 0xFF).toRadixString(16).padLeft(2, '0'))
        .join();
  }
}
