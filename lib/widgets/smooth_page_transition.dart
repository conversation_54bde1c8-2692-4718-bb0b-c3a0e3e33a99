import 'package:flutter/material.dart';

/// 平滑页面切换组件
class SmoothPageTransition extends StatefulWidget {
  final int currentIndex;
  final List<Widget> children;
  final Duration duration;

  const SmoothPageTransition({
    Key? key,
    required this.currentIndex,
    required this.children,
    this.duration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<SmoothPageTransition> createState() => _SmoothPageTransitionState();
}

class _SmoothPageTransitionState extends State<SmoothPageTransition>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int _displayIndex = 0;
  bool _isTransitioning = false;

  @override
  void initState() {
    super.initState();
    _displayIndex = widget.currentIndex;
    
    _animationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.value = 1.0;
  }

  @override
  void didUpdateWidget(SmoothPageTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentIndex != widget.currentIndex && !_isTransitioning) {
      _performTransition();
    }
  }

  void _performTransition() {
    if (_isTransitioning) return;
    
    setState(() {
      _isTransitioning = true;
    });

    // 淡出当前页面
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _displayIndex = widget.currentIndex;
        });
        // 淡入新页面
        _animationController.forward().then((_) {
          if (mounted) {
            setState(() {
              _isTransitioning = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: IndexedStack(
        index: _displayIndex,
        children: widget.children,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}