import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:lima/global.dart';
// MobSDK 已移除
// import 'package:lima/utils/mob_sdk.dart';
import 'package:lima/utils/q_log.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrivacyPolicyDialog {
  static const String _privacyAcceptedKey = 'privacy_policy_accepted';

  /// 检查是否需要显示隐私政策对话框
  static Future<bool> shouldShowPrivacyDialog() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isAccepted = prefs.getBool(_privacyAcceptedKey) ?? false;
      return !isAccepted;
    } catch (e) {
      QLog('检查隐私政策状态失败: $e');
      return true;
    }
  }

  /// 显示隐私政策对话框
  static Future<void> showPrivacyDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // 用户必须做出选择
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // 禁止返回键关闭
          child: AlertDialog(
            title: const Text(
              '隐私政策',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFFC70E2D),
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    '欢迎使用利马科技应用！',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '为了更好地为您提供服务，我们需要您同意以下隐私政策：',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  // 第三方SDK隐私协议（合并版本）
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '第三方SDK隐私协议',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1976D2),
                          ),
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          '为了向您提供更好的服务体验，我们集成了以下第三方SDK：',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                            height: 1.4,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '1. MobTech MobLinkSDK（上海掌之淘信息技术有限公司）\n'
                          '• 用途：为您提供场景还原功能\n'
                          '• 收集信息：相关的个人信息\n'
                          '• 隐私政策：详见MobLink隐私政策\n',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                            height: 1.4,
                          ),
                        ),
                        const Text(
                          '2. 个推推送SDK（每日互动股份有限公司）\n'
                          '• 用途：为您提供消息推送功能\n'
                          '• 收集信息：设备标识信息（IMEI/MAC/Android ID/IDFA/OpenUDID/GUID/SIM卡IMSI等）、应用信息（应用崩溃信息、通知开关状态、软件列表等）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息等）\n'
                          '• 隐私政策：https://docs.getui.com/privacy/\n',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                            height: 1.4,
                          ),
                        ),
                        const Text(
                          '我们会严格遵守法律法规要求，采取严格的数据安全措施保护您的个人信息。您可以随时撤回授权。',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF1976D2),
                            fontWeight: FontWeight.w500,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '我们承诺：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• 严格保护您的个人信息安全\n'
                    '• 仅在必要时收集和使用您的信息\n'
                    '• 不会将您的信息用于其他商业目的\n'
                    '• 您可以随时撤回授权',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '请您仔细阅读并确认是否同意上述隐私政策。',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => _handleDisagree(context),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                ),
                child: const Text('不同意'),
              ),
              ElevatedButton(
                onPressed: () => _handleAgree(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFC70E2D),
                  foregroundColor: Colors.white,
                ),
                child: const Text('同意'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 处理用户同意隐私政策
  static Future<void> _handleAgree(BuildContext context) async {
    try {
      // 先关闭对话框
      Navigator.of(context).pop();

      // 保存用户同意状态
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_privacyAcceptedKey, true);

      // 🔑 重要：同时更新 Global 类使用的权限状态
      await prefs.setBool(Config.APP_PERMISSION, true);
      Global.appPermissionGranted = true;

      // MobSDK 已移除
      // MobSDK.instance.setPrivacyGranted(true);
      // await MobSDK.instance.init();

      QLog('✅ 用户同意隐私政策，MobSDK已移除');

      // 🚀 重要：立即初始化第三方SDK（包括个推推送服务）
      QLog('🚀 开始初始化第三方SDK...');
      await Global.initThirdPartySdkAfterConsent();

      // 显示成功提示
      Get.snackbar(
        '提示',
        '隐私政策已确认，正在初始化服务...',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      QLog('❌ 处理隐私政策同意失败: $e');
      Get.snackbar(
        '错误',
        '处理失败，请重试',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 处理用户不同意隐私政策
  static Future<void> _handleDisagree(BuildContext context) async {
    try {
      // 先关闭对话框
      Navigator.of(context).pop();

      // MobSDK 已移除
      // MobSDK.instance.setPrivacyGranted(false);

      QLog('用户不同意隐私政策');

      // 显示提示信息
      Get.snackbar(
        '提示',
        '您可以在设置中重新确认隐私政策',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      QLog('处理隐私政策拒绝失败: $e');
    }
  }

  /// 重置隐私政策状态（用于测试）
  static Future<void> resetPrivacyStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_privacyAcceptedKey);
      // MobSDK 已移除
      // MobSDK.instance.setPrivacyGranted(false);
      QLog('隐私政策状态已重置');
    } catch (e) {
      QLog('重置隐私政策状态失败: $e');
    }
  }
}
