import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:lima/utils/q_log.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static late Dio _dio;
  static const String baseUrl = 'https://api.lima.com'; // 替换为实际的API地址

  /// 初始化Dio
  static void init() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 添加拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 添加access_token到请求头
        final prefs = await SharedPreferences.getInstance();
        final accessToken = prefs.getString('access_token');
        if (accessToken != null && accessToken.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
        
        QLog('API请求: ${options.method} ${options.uri}');
        QLog('请求头: ${options.headers}');
        if (options.data != null) {
          QLog('请求数据: ${options.data}');
        }
        
        handler.next(options);
      },
      onResponse: (response, handler) {
        QLog('API响应: ${response.statusCode} ${response.requestOptions.uri}');
        QLog('响应数据: ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        QLog('API错误: ${error.message}');
        QLog('错误详情: ${error.response?.data}');
        handler.next(error);
      },
    ));
  }

  /// GET请求
  static Future<Map<String, dynamic>> get(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// POST请求
  static Future<Map<String, dynamic>> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// POST请求 - form-data格式
  static Future<Map<String, dynamic>> postFormData(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      FormData formData = FormData();
      if (data != null) {
        data.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      final response = await _dio.post(
        path,
        data: formData,
        queryParameters: queryParameters,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// PUT请求
  static Future<Map<String, dynamic>> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// DELETE请求
  static Future<Map<String, dynamic>> delete(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        queryParameters: queryParameters,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// 处理响应
  static Map<String, dynamic> _handleResponse(Response response) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      if (response.data is Map<String, dynamic>) {
        return response.data;
      } else if (response.data is String) {
        try {
          return json.decode(response.data);
        } catch (e) {
          return {
            'state': 255,
            'msg': '响应数据格式错误',
            'data': null,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          };
        }
      }
    }
    
    return {
      'state': response.statusCode ?? 255,
      'msg': '请求失败',
      'data': response.data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 处理错误
  static Map<String, dynamic> _handleError(dynamic error) {
    String message = '网络请求失败';
    int statusCode = 255;

    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          message = '连接超时';
          break;
        case DioExceptionType.sendTimeout:
          message = '发送超时';
          break;
        case DioExceptionType.receiveTimeout:
          message = '接收超时';
          break;
        case DioExceptionType.badResponse:
          statusCode = error.response?.statusCode ?? 255;
          message = '服务器错误 ($statusCode)';
          break;
        case DioExceptionType.cancel:
          message = '请求已取消';
          break;
        case DioExceptionType.unknown:
          message = '网络连接失败';
          break;
        default:
          message = '未知错误';
      }
    }

    return {
      'state': statusCode,
      'msg': message,
      'data': null,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 保存access_token
  static Future<void> saveAccessToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
    QLog('Access token已保存: $token');
  }

  /// 获取access_token
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('access_token');
  }

  /// 清除access_token
  static Future<void> clearAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    QLog('Access token已清除');
  }
}
