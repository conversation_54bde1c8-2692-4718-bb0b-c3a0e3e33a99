import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/config/alicloud_config.dart';
import 'package:ali_auth/ali_auth.dart';
import 'package:lima/services/third_party_login_service.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/pages/login/login_logic.dart';
import 'package:lima/pages/phone_input/phone_input_view.dart';
import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/components/overlay_webview.dart';

/// 阿里云号码认证服务 (DYPNS) - 运营商一键登录
/// 支持中国移动、中国联通、中国电信的一键登录功能
/// 官方文档：https://www.aliyun.com/product/dypns
class CarrierLoginService {
  static CarrierLoginService? _instance;
  static CarrierLoginService get instance =>
      _instance ??= CarrierLoginService._();

  // 实例ID，用于调试
  final String instanceId = DateTime.now().millisecondsSinceEpoch.toString();

  CarrierLoginService._() {
    // 在构造函数中初始化clientId
    _initializeClientId();
    QLog('🏗️ CarrierLoginService实例创建，ID: $instanceId');
  }

  // 阿里云DYPNS配置从配置文件获取
  bool _isListenerSetup = false;

  bool _isAliAuthChecked = false;

  // 设备clientId
  String clientId = '';

  // 用于存储登录结果的Completer
  Completer<Map<String, dynamic>?>? _loginCompleter;

  // 防止重复处理登录成功事件
  bool _isProcessingLoginSuccess = false;

  // 全局变量：标记是否需要重新显示AliAuth
  static bool _needShowAliAuth = false;

  /// 设置需要重新显示AliAuth的标记
  static void setNeedShowAliAuth(bool need) {
    _needShowAliAuth = need;
    QLog('🔗 设置AliAuth显示标记: $need');
  }

  /// 检查并处理AliAuth显示标记
  static Future<void> checkAndHandleAliAuth() async {
    if (_needShowAliAuth) {
      QLog('🔗 检测到需要重新显示AliAuth，准备调用AliAuth.login()');
      _needShowAliAuth = false; // 重置标记
      try {
        // await Future.delayed(Duration(milliseconds: 300));
        await AliAuth.login();
        QLog('🔗 ✅ AliAuth.login()调用完成');
      } catch (e) {
        QLog('🔗 ❌ 调用AliAuth.login()失败: $e');
      }
    }
  }

  /// 显示登录成功后的系统级隐私协议确认弹窗
  Future<void> _showPrivacyAgreementDialogForLogin(
      Map<String, dynamic> eventMap) async {
    try {
      QLog('📋 显示登录成功后的系统级协议确认弹窗');
      QLog('📋 当前平台: ${Platform.operatingSystem}');
      QLog('📋 eventMap内容: $eventMap');

      // 从配置中获取协议名称
      final protocolOneName =
          AlicloudConfig.authUIConfig['privacyOne']?[0] ?? '《用户协议》';
      final protocolTwoName =
          AlicloudConfig.authUIConfig['privacyTwo']?[0] ?? '《隐私政策》';

      QLog('📋 协议名称 - 协议一: $protocolOneName, 协议二: $protocolTwoName');

      // 调用原生系统级弹窗
      const platform = MethodChannel('ali_auth');

      final params = {
        'title': '用户协议及隐私保护政策',
        'message': '我已阅读并同意\n$protocolOneName $protocolTwoName',
        'confirmText': '同意',
        'cancelText': '不同意',
      };

      QLog('📋 准备调用原生弹窗，参数: $params');

      // 添加超时处理
      final result = await platform
          .invokeMethod('showPrivacyAgreementDialog', params);

      QLog('📋 原生弹窗返回结果: $result (类型: ${result.runtimeType})');

      if (result == true) {
        QLog('📋 [实例$instanceId] 用户同意协议，显示登录中Loading');
        _isAliAuthChecked = true;
        AliAuth.quitPage();
        
        // 显示登录中的Loading提示
        EasyLoading.show(
          status: '正在登录中...',
          maskType: EasyLoadingMaskType.black,
        );
        
        try {
          // 用户同意后，调用登录成功处理方法
          await _handleCarrierLoginSuccess(eventMap);
        } finally {
          // 无论成功还是失败，都要关闭Loading
          EasyLoading.dismiss();
        }
      } else {
        QLog('📋 用户拒绝协议，准备重新显示登录页面');
        // 用户拒绝时，重置状态并确保下次可以重新触发
        _isAliAuthChecked = false;
        _isProcessingLoginSuccess = false; // 重置处理状态
        
        // ⭐️ 关键修复：强制退出并重置SDK状态，然后重新显示登录页面
        try {
          QLog('🔄 开始强制退出SDK页面，重置内部状态...');
          AliAuth.quitPage();
          QLog('🔄 ✅ 成功退出SDK页面，状态已重置');
          
          // 延迟一下确保SDK完全退出
          // await Future.delayed(const Duration(milliseconds: 100));
          
          // 重新调用AliAuth.login()显示登录页面
          EasyLoading.showError('用户不同意用户协议，取消登录');
          
        } catch (e) {
          QLog('🔄 ❌ 重置SDK状态或重新显示登录页面失败: $e');
        }
        
        // 重置登录完成器，为下次登录做准备
        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete(
              {'success': false, 'resultCode': '700000', 'msg': '用户拒绝协议'});
        }
        _loginCompleter = null; // 清空完成器，下次登录时会重新创建
        
        QLog('📋 完整状态重置完成，已重新显示登录页面');
      }
    } on PlatformException catch (e) {
      QLog('📋 ❌ 原生平台异常: ${e.code} - ${e.message}');
      QLog('📋 ❌ 异常详情: ${e.details}');
      // 平台异常时，直接处理登录成功（向后兼容）
      QLog('📋 平台异常，回退到直接处理登录成功');
      await _handleCarrierLoginSuccess(eventMap);
    } on TimeoutException catch (e) {
      QLog('📋 ⏰ 调用原生弹窗超时: $e');
      // 超时时，直接处理登录成功（向后兼容）
      QLog('📋 调用超时，回退到直接处理登录成功');
      await _handleCarrierLoginSuccess(eventMap);
    } catch (e) {
      QLog('📋 ❌ 显示登录协议确认弹框异常: $e');
      QLog('📋 ❌ 异常类型: ${e.runtimeType}');
      // 如果弹窗失败，直接处理登录成功（向后兼容）
      QLog('📋 弹窗失败，回退到直接处理登录成功');
      await _handleCarrierLoginSuccess(eventMap);
    }
  }

  void _setupListener() {
    if (_isListenerSetup) return;

    try {
      QLog('🔧 [实例$instanceId] 设置阿里云DYPNS监听器...');

      // 设置登录监听器
      AliAuth.loginListen(
        onEvent: (event) async {
          QLog('阿里云DYPNS事件: $event');
          // 处理登录事件
          if (event != null && event is Map) {
            final eventMap = Map<String, dynamic>.from(event);
            final code = eventMap['code']?.toString();
            final msg = eventMap['msg']?.toString();
            final data = eventMap['data']?.toString();
            final isChecked = eventMap['isChecked'];

            QLog('登录结果码: $code, 消息: $msg, 整个结果: $eventMap');

            // 处理登录结果
            if (code == '600000') {
              // 运营商登录成功，先显示系统级隐私协议确认弹窗
              QLog('运营商登录成功，显示系统级协议确认弹窗');
              // 重置协议状态，确保每次都会显示弹窗
              _isAliAuthChecked = false;
              await _showPrivacyAgreementDialogForLogin(eventMap);
            } else if (code == '700000') {
              // 用户取消登录，重置协议状态
              QLog('🔄 [实例$instanceId] 用户取消登录，开始重置协议状态');
              _isAliAuthChecked = false;
              _isProcessingLoginSuccess = false; // 也重置处理状态
              
              // ⭐️ 关键修复：在用户取消登录时也重置SDK状态
              try {
                QLog('🔄 用户取消登录，强制退出SDK页面重置状态...');
                await AliAuth.quitPage();
                QLog('🔄 ✅ 取消登录后SDK状态重置成功');
              } catch (e) {
                QLog('🔄 ❌ 取消登录时SDK重置失败: $e');
              }
              
              if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
                _loginCompleter!.complete({
                  'success': false,
                  'resultCode': '700000',
                  'msg': '用户取消登录'
                });
              }
              _loginCompleter = null; // 清空完成器
              QLog('🔄 [实例$instanceId] 用户取消登录状态重置完成');
            } else if (code == '700003') {
              // 用户勾选协议选项 - 只在SDK状态为true时更新，避免覆盖手动设置的true状态
              if (isChecked == true) {
                _isAliAuthChecked = isChecked;
              }
              QLog('用户勾选协议选项，当前状态: SDK=$isChecked, Flutter=$_isAliAuthChecked');
            } else if (code == '700004') {
              // 用户点击协议富文本
              QLog('用户点击协议富文本，准备打开webview');
              _handleProtocolClick(eventMap);
            } else if (code == '700001') {
              // 用户点击"切换到其他方式"按钮
              QLog('用户点击了"切换手机号登录"按钮，跳转到手机号输入页面');

              // 跳转到手机号输入页面
              Get.toNamed(AppRoute.phoneInput);

              if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
                _loginCompleter!.complete(eventMap);
              }
            } else if (code == '700005') {
              // 第三方登录按钮点击事件
              // if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
              //   _loginCompleter!.complete(eventMap);
              // }
              // QLog("_isAliAuthChecked: $_isAliAuthChecked");
              // if (_isAliAuthChecked == true) {
              // 不要立即重置状态，保持授权页面打开
              if (data == '1') {
                // Apple登录仅在iOS平台可用
                if (Platform.isIOS) {
                  QLog('用户点击了Apple登录按钮，显示隐私协议确认弹窗');
                  _showPrivacyAgreementDialogForThirdPartyLogin('Apple登录',
                      () async {
                    QLog('用户同意隐私协议，开始Apple登录');
                    await _handleAppleLogin();
                  });
                } else {
                  QLog('Android平台不支持Apple登录，忽略此点击事件');
                }
              }

              if (data == '0') {
                QLog('用户点击了微信登录按钮，显示隐私协议确认弹窗');
                _showPrivacyAgreementDialogForThirdPartyLogin('微信登录', () async {
                  QLog('用户同意隐私协议，开始微信登录');
                  await _handleWechatLogin();
                });
                // }
                // _isAliAuthChecked = false;
              }
            } else if (code?.startsWith('6') == true && code != '600001') {
              // 其他错误（600001是唤起授权页成功，不是错误）
              QLog('运营商登录错误，错误码: $code, 错误信息: $msg');

              // 针对特定错误码提供友好的用户提示
              _handleCarrierLoginError(code, msg);

              if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
                _loginCompleter!.complete(eventMap);
              }
            }
          }
        },
        onError: (error) {
          QLog('阿里云DYPNS错误: $error');
          if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
            _loginCompleter!.completeError(error);
          }
        },
      );

      _isListenerSetup = true;
      QLog('阿里云DYPNS监听器设置成功');
    } catch (e) {
      QLog('设置阿里云DYPNS监听器失败: $e');
    }
  }

  /// 处理运营商登录错误
  void _handleCarrierLoginError(String? code, String? msg) {
    String userFriendlyMessage = '';
    String actionSuggestion = '';
    bool shouldJumpToPhoneLogin = false;

    switch (code) {
      case '600008':
        userFriendlyMessage = '需要开启移动网络';
        actionSuggestion = '请开启手机的移动数据网络后重试，或使用其他登录方式';
        shouldJumpToPhoneLogin = true; // 网络问题，直接跳转
        break;
      case '600009':
        userFriendlyMessage = '当前运营商不支持';
        actionSuggestion = '您的运营商暂不支持一键登录，请使用手机号验证码登录';
        shouldJumpToPhoneLogin = true; // 运营商不支持，直接跳转
        break;
      case '600010':
        userFriendlyMessage = '网络连接异常';
        actionSuggestion = '网络异常，请稍后重试或使用其他登录方式';
        shouldJumpToPhoneLogin = true; // 网络异常，直接跳转
        break;
      case '600011':
        userFriendlyMessage = '获取运营商配置失败';
        actionSuggestion = '网络异常，请稍后重试或使用其他登录方式';
        shouldJumpToPhoneLogin = true; // 配置失败，直接跳转
        break;
      case '600012':
        userFriendlyMessage = '预登录失败';
        actionSuggestion = '请重试或使用手机号验证码登录';
        shouldJumpToPhoneLogin = true; // 预登录失败，直接跳转
        break;
      case '600013':
        userFriendlyMessage = '用户取消登录';
        actionSuggestion = '运营商维护升级，该功能不可用，可选择其他登录方式';
        shouldJumpToPhoneLogin = false; // 用户取消，不自动跳转
        break;
      case '600014':
        userFriendlyMessage = '运营商授权失败';
        actionSuggestion = '授权失败，请重试或使用其他登录方式';
        shouldJumpToPhoneLogin = true; // 授权失败，直接跳转
        break;
      case '600015':
        userFriendlyMessage = '获取手机号失败';
        actionSuggestion = '无法获取手机号，请使用手机号验证码登录';
        shouldJumpToPhoneLogin = true; // 获取手机号失败，直接跳转
        break;
      case '600017':
        userFriendlyMessage = 'SDK初始化失败';
        actionSuggestion = '初始化失败，请重启应用或使用其他登录方式';
        shouldJumpToPhoneLogin = true; // SDK初始化失败，直接跳转
        break;
      case '600024':
        shouldJumpToPhoneLogin = false; // 授权失败，直接跳转
        break;
      default:
        userFriendlyMessage = '一键登录失败';
        actionSuggestion = '请重试或使用手机号验证码登录';
        shouldJumpToPhoneLogin = true; // 其他错误，直接跳转
        break;
    }

    QLog('运营商登录错误处理:');
    QLog('  错误码: $code');
    QLog('  原始消息: $msg');
    QLog('  用户友好消息: $userFriendlyMessage');
    QLog('  操作建议: $actionSuggestion');
    QLog('  是否需要跳转到手机验证码登录: $shouldJumpToPhoneLogin');

    // 显示用户友好的错误提示
    if (code == '600013' || code == '600024') {
      // 用户取消登录，不显示错误提示
      QLog('用户主动取消登录，不显示错误提示');
    } else {
      // 显示错误提示和操作建议
      EasyLoading.showError(
        '$userFriendlyMessage\n$actionSuggestion',
        duration: Duration(seconds: 3), // 缩短显示时间
      );
    }

    // 如果需要跳转到手机验证码登录，触发跳转
    if (shouldJumpToPhoneLogin) {
      QLog('运营商登录失败，准备跳转到手机验证码登录');
      _triggerPhoneVerificationLogin();
    }
  }

  /// 触发跳转到手机验证码登录
  void _triggerPhoneVerificationLogin() {
    try {
      QLog('触发跳转到手机验证码登录');

      // 延迟一点时间，让错误提示显示完
      Future.delayed(const Duration(milliseconds: 500), () async {
        try {
          // 创建 LoginLogic 实例并调用 testPhoneVerificationLogin
          final loginLogic = LoginLogic();
          await loginLogic.testPhoneVerificationLogin();
          QLog('已调用 LoginLogic.testPhoneVerificationLogin()');
        } catch (e) {
          QLog('调用 LoginLogic.testPhoneVerificationLogin() 失败: $e');
          // 如果调用失败，回退到原来的跳转方式
          Get.toNamed('/login', arguments: {
            'fromCarrierLoginError': true,
            'skipCarrierLogin': true,
          });
        }
      });
    } catch (e) {
      QLog('触发跳转到手机验证码登录失败: $e');
    }
  }

  /// 处理协议点击事件
  Future<void> _handleProtocolClick(Map<String, dynamic> eventMap) async {
    try {
      QLog('🔗 处理协议点击事件开始: $eventMap');

      final data = eventMap['data'];
      QLog('🔗 提取的data数据: $data, 类型: ${data.runtimeType}');

      // 处理不同类型的data数据
      Map<String, dynamic>? protocolData;
      if (data != null) {
        if (data is Map<String, dynamic>) {
          protocolData = data;
        } else if (data is Map) {
          // 转换为Map<String, dynamic>
          protocolData = Map<String, dynamic>.from(data);
        }
      }

      if (protocolData != null) {
        final protocolName = protocolData['name']?.toString();
        final protocolUrl = protocolData['url']?.toString();

        QLog('🔗 协议名称: $protocolName');
        QLog('🔗 协议URL: $protocolUrl');

        if (protocolUrl != null && protocolUrl.isNotEmpty) {
          QLog('🔗 准备跳转到webview页面...');

          // 添加参数禁用错误报告，减少CORS错误
          String finalUrl = protocolUrl;
          if (!finalUrl.contains('disable_sentry')) {
            final separator = finalUrl.contains('?') ? '&' : '?';
            finalUrl = '$finalUrl${separator}disable_sentry=true';
          }

          // Android系统特殊处理：先关闭ali_auth页面，查看协议后再重新显示
          QLog('🔗 当前平台: ${Platform.operatingSystem}');
          QLog('🔗 是否为Android: ${Platform.isAndroid}');

          if (Platform.isAndroid) {
            try {
              QLog('🔗 Android系统：开始处理协议点击...');

              // 先关闭ali_auth页面
              AliAuth.quitPage();

              // 跳转到协议页面，等待返回
              await Get.to(() => WebviewPage(), arguments: {
                'url': finalUrl,
                'title': protocolName ?? '协议详情',
                'AliAuth': true, // 标记来自AliAuth
              });

              QLog('🔗 协议页面已返回，检查全局变量标记');

              // 检查全局变量，如果需要则重新显示ali_auth页面
              await CarrierLoginService.checkAndHandleAliAuth();
            } catch (e) {
              QLog('🔗 Android协议处理失败: $e');
              // 发生错误时也要尝试重新显示ali_auth页面
              try {
                await AliAuth.login();
              } catch (authError) {
                QLog('🔗 重新显示ali_auth页面失败: $authError');
              }
            }
          } else {
            // iOS系统使用高层级WebView覆盖层
            try {
              QLog('🔗 iOS系统：使用OverlayWebView显示协议页面...');
              OverlayWebViewManager.show(
                url: finalUrl,
                title: protocolName ?? '协议详情',
                onClose: () {
                  QLog('🔗 协议页面已关闭');
                },
              );
              QLog('🔗 OverlayWebView显示完成');
            } catch (e1) {
              QLog('🔗 OverlayWebView失败: $e1，尝试备用方案...');
              try {
                Get.toNamed(AppRoute.webview, arguments: {
                  'url': finalUrl,
                  'title': protocolName ?? '协议详情',
                });
              } catch (e2) {
                QLog('🔗 备用方案也失败: $e2');
              }
            }
          }
        } else {
          QLog('🔗 ❌ 协议URL为空，无法打开webview');
        }
      } else {
        QLog('🔗 ❌ 协议数据为空或格式错误: $data');
      }
    } catch (e) {
      QLog('🔗 ❌ 处理协议点击事件异常: $e');
    }
  }

  /// 处理微信登录
  Future<void> _handleWechatLogin() async {
    try {
      QLog('开始处理微信登录...');
      QLog(
          '_loginCompleter状态: $_loginCompleter, isCompleted: ${_loginCompleter?.isCompleted}');

      // 检查_loginCompleter是否有效，如果为null则创建新的
      if (_loginCompleter == null) {
        QLog('_loginCompleter为null，创建新的Completer用于微信登录');
        _loginCompleter = Completer<Map<String, dynamic>?>();
      }

      if (_loginCompleter!.isCompleted) {
        QLog('警告：_loginCompleter已经完成，创建新的Completer');
        _loginCompleter = Completer<Map<String, dynamic>?>();
      }

      // 不要关闭运营商授权页面，保持页面打开状态
      // await closeAuthPage();

      // 使用统一的第三方登录服务
      final result = await ThirdPartyLoginService.instance.wechatLogin();

      if (result.success) {
        QLog('微信登录成功，检查是否需要绑定手机号');

        // 检查返回数据中是否包含bindKey，如果有则需要进行手机号绑定
        final userData = result.userData;
        if (userData != null && userData['bindKey'] != null) {
          QLog('检测到bindKey，需要进行手机号绑定: ${userData['bindKey']}');
          // 启动手机号绑定流程，不等待返回结果
          _handlePhoneBinding(userData, loginType: 'wechat');
          // 手机号绑定流程会自动处理后续跳转，这里直接返回
          return;
        } else {
          QLog('微信登录完成，无需手机号绑定');
          await _saveUserInfo(userData);
          if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
            _loginCompleter!.complete({
              'success': true,
              'resultCode': '600000',
              'msg': '微信登录成功',
              'loginType': 'wechat',
              'data': userData
            });
          }

          // 显示登录成功提示
          EasyLoading.showSuccess('微信登录成功');

          // 关闭运营商登录界面
          QLog('📱 微信登录成功，调用AliAuth.quitPage()关闭界面');
          AliAuth.quitPage();

          QLog('微信登录成功处理完成，跳转到主Tab页面');
          // 重置协议状态，为下次登录做准备
          QLog('🔄 [实例$instanceId] 微信登录成功，重置协议状态');
          _isAliAuthChecked = false;
          Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
        }
        return; // 成功后立即返回，避免继续执行
      } else {
        QLog('处理微信登录失败: ${result.success}');

        // 微信登录失败时，尝试重新显示AliAuth授权页面
        if (result.errorCode != 'WECHAT_CANCEL') {
          try {
            QLog('微信登录失败，尝试重新显示运营商授权页面...');
            await AliAuth.login();
          } catch (e) {
            QLog('重新显示运营商授权页面失败: $e');
          }
          EasyLoading.showError(result.errorMessage ?? '微信登录失败');
        }

        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': result.errorCode ?? 'WECHAT_ERROR',
            'msg': result.errorMessage ?? '微信登录失败'
          });
        }
      }
    } catch (e) {
      QLog('微信登录异常: $e');

      // 发生异常时，尝试重新显示AliAuth授权页面
      try {
        QLog('微信登录异常，尝试重新显示运营商授权页面...');
        await AliAuth.login();
      } catch (authError) {
        QLog('重新显示运营商授权页面失败: $authError');
      }

      EasyLoading.showError('微信登录失败');
      if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
        _loginCompleter!.complete({
          'success': false,
          'resultCode': 'WECHAT_ERROR',
          'msg': '微信登录失败: $e'
        });
      }
    }
  }

  /// 处理需要手机号绑定的情况
  Future<void> _handlePhoneBinding(Map<String, dynamic> userData,
      {String loginType = 'wechat'}) async {
    try {
      QLog('需要绑定手机号，bindKey: ${userData['bindKey']}, loginType: $loginType');

      // 验证必要参数
      if (userData['bindKey'] == null) {
        QLog('错误：bindKey为null，无法进行手机号绑定');
        EasyLoading.showError('登录数据异常，请重试');
        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': 'BIND_KEY_MISSING',
            'msg': '登录数据异常，请重试'
          });
        }
        return;
      }

      QLog('启动手机号绑定流程，跳转到手机号输入页面');

      // 验证路由参数
      final arguments = <String, dynamic>{
        'bindKey': userData['bindKey']?.toString() ?? '',
        'loginType': loginType,
        'originalData': userData,
      };

      QLog('跳转参数: $arguments');

      try {
        // 使用Get.to跳转，不等待返回结果
        // 因为验证码页面会直接跳转到主Tab页面
        Get.to(
          () => const PhoneInputPage(),
          arguments: arguments,
        );

        QLog('手机号绑定流程已启动，验证码页面将直接处理后续跳转');

        // 完成当前登录流程，标记为成功
        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': true,
            'resultCode': '600000',
            'msg': '$loginType登录成功，已启动手机号绑定流程',
            'loginType': loginType,
            'data': userData
          });
        }
      } catch (routeError) {
        QLog('路由跳转失败: $routeError');
        EasyLoading.showError('页面跳转失败，请重试');
        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': 'ROUTE_ERROR',
            'msg': '页面跳转失败: $routeError'
          });
        }
      }
    } catch (e, stackTrace) {
      QLog('处理手机号绑定异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('手机号绑定异常: $e');
      if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
        _loginCompleter!.complete({
          'success': false,
          'resultCode': 'PHONE_BINDING_ERROR',
          'msg': '手机号绑定异常: $e'
        });
      }
    }
  }

  /// 保存用户信息
  Future<void> _saveUserInfo(dynamic userData) async {
    if (userData == null) return;

    try {
      // 使用LoginLogic中的saveUserInfo方法
      final loginLogic = LoginLogic();
      await loginLogic.saveUserInfo(userData);
      QLog('✅ 使用LoginLogic保存用户信息成功');
    } catch (e) {
      QLog('❌ 保存用户信息失败: $e');
    }
  }

  /// 处理Apple登录
  Future<void> _handleAppleLogin() async {
    if (!Platform.isIOS) {
      QLog('Apple登录仅支持iOS平台');
      if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
        _loginCompleter!.complete({
          'success': false,
          'resultCode': 'APPLE_NOT_SUPPORTED',
          'msg': 'Apple登录仅支持iOS'
        });
      }
      return;
    }

    try {
      QLog('开始处理Apple登录...');
      QLog(
          '_loginCompleter状态: $_loginCompleter, isCompleted: ${_loginCompleter?.isCompleted}');

      // 检查_loginCompleter是否有效，如果为null则创建新的
      if (_loginCompleter == null) {
        QLog('_loginCompleter为null，创建新的Completer用于Apple登录');
        _loginCompleter = Completer<Map<String, dynamic>?>();
      }

      if (_loginCompleter!.isCompleted) {
        QLog('警告：_loginCompleter已经完成，创建新的Completer');
        _loginCompleter = Completer<Map<String, dynamic>?>();
      }

      // 不要关闭运营商授权页面，保持页面打开状态
      // await closeAuthPage();

      // 使用统一的第三方登录服务
      final result = await ThirdPartyLoginService.instance.appleLogin();

      QLog('Apple登录详细结果:');
      QLog('  - 成功: ${result.success}');
      QLog('  - 登录类型: ${result.loginType}');
      QLog('  - 错误码: ${result.errorCode}');
      QLog('  - 错误信息: ${result.errorMessage}');
      QLog('  - 用户数据类型: ${result.userData?.runtimeType}');
      QLog('  - 用户数据内容: ${result.userData}');

      if (result.success) {
        QLog('Apple登录成功，检查是否需要绑定手机号');

        // 检查返回数据中是否包含bindKey，如果有则需要进行手机号绑定
        final userData = result.userData;
        if (userData != null && userData['bindKey'] != null) {
          QLog('检测到bindKey，需要进行手机号绑定: ${userData['bindKey']}');
          // 启动手机号绑定流程，不等待返回结果
          _handlePhoneBinding(userData, loginType: 'apple');
          // 手机号绑定流程会自动处理后续跳转，这里直接返回
          return;
        } else {
          QLog('Apple登录完成，无需手机号绑定');
          await _saveUserInfo(userData);
          if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
            _loginCompleter!.complete({
              'success': true,
              'resultCode': '600000',
              'msg': 'Apple登录成功',
              'loginType': 'apple',
              'data': userData
            });
          }

          // 显示登录成功提示
          EasyLoading.showSuccess('Apple登录成功');

          // 关闭运营商登录界面
          QLog('📱 Apple登录成功，调用AliAuth.quitPage()关闭界面');
          AliAuth.quitPage();

          QLog('Apple登录成功处理完成，跳转到主Tab页面');
          // 重置协议状态，为下次登录做准备
          QLog('🔄 [实例$instanceId] Apple登录成功，重置协议状态');
          _isAliAuthChecked = false;
          Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
        }
        return; // 成功后立即返回，避免继续执行
      } else {
        QLog('Apple登录失败，尝试重新显示运营商授权页面...');

        // Apple登录失败时，尝试重新显示AliAuth授权页面
        if (result.errorCode != 'APPLE_NOT_SUPPORTED') {
          try {
            await AliAuth.login();
          } catch (authError) {
            QLog('重新显示运营商授权页面失败: $authError');
          }
          EasyLoading.showError(result.errorMessage ?? 'Apple登录失败');
        }

        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': result.errorCode ?? 'APPLE_ERROR',
            'msg': result.errorMessage ?? 'Apple登录失败'
          });
        }
      }
    } catch (e) {
      QLog('处理Apple登录失败: $e');

      // 发生异常时，尝试重新显示AliAuth授权页面
      try {
        QLog('Apple登录异常，尝试重新显示运营商授权页面...');
        await AliAuth.login();
      } catch (authError) {
        QLog('重新显示运营商授权页面失败: $authError');
      }

      EasyLoading.showError('Apple登录失败');
      if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
        _loginCompleter!.complete({
          'success': false,
          'resultCode': 'APPLE_ERROR',
          'msg': 'Apple登录失败: $e'
        });
      }
    }
  }

  /// 检查运营商登录是否可用
  Future<CarrierAvailability> checkAvailability() async {
    try {
      QLog('检查运营商登录可用性...');

      // 检查配置是否有效
      if (!AlicloudConfig.isConfigValid()) {
        return CarrierAvailability(
          isAvailable: false,
          errorCode: 'CONFIG_INVALID',
          errorMessage: AlicloudConfig.getConfigError(),
        );
      }

      // 初始化SDK
      final initResult = await initialize();
      if (!initResult) {
        return CarrierAvailability(
          isAvailable: false,
          errorCode: 'INIT_FAILED',
          errorMessage: 'SDK初始化失败',
        );
      }

      // SDK初始化成功即表示可用
      return CarrierAvailability(
        isAvailable: true,
        operatorType: 'unknown', // 实际运营商类型需要从SDK获取
      );
    } catch (e) {
      QLog('检查运营商登录可用性失败: $e');
      return CarrierAvailability(
        isAvailable: false,
        errorCode: 'CHECK_FAILED',
        errorMessage: '检查失败: $e',
      );
    }
  }

  /// 初始化阿里云DYPNS SDK
  Future<bool> initialize() async {
    try {
      QLog('开始初始化阿里云DYPNS SDK...');

      // 检查配置是否有效
      if (!AlicloudConfig.isConfigValid()) {
        QLog('阿里云配置无效: ${AlicloudConfig.getConfigError()}');
        return false;
      }

      // 先设置监听器
      _setupListener();

      Map<String, dynamic> configMap = {
        'top': 20,
        'left': 20,
        'width': 40,
        'height': 40,
        'imgPath': "assets/images/return_btn_fill.png"
      };
      CustomView customReturnBtn = CustomView.fromJson(configMap);

      // 使用ali_auth插件初始化
      // 创建AliAuthModel配置对象 - 根据设计稿更新
      final authModel = AliAuthModel(
          "oD8/hdcv958QwW6F4K2oSIHdDPOlRvY+SYJCZw03Q2Gk6igAg2Ae96vpXdU+wjuJZUeRt29+u7bmLHhEU6vHqaOagpXxSngzcE+OsoIuLHg+jHR4kRW93q7lwblfU8DFk/woadJKM8INsdh2cWf26k8vPfTqCsx4FhIVxA48tN8G0+H9Fjnx/XH6wRLxXqpn88ZQpwTyJ34JzzjLSVADDaGdoStSql3b4qnI7AtV5tyqVAzTmNwd6RWGvRhSlcq+HBEnX/rcIRZygVjFHqGZ/H0JeTLCpprsF48YRc4AA64=", // androidSk
          "YeNb8BYamoyuArE16bcdo/+EZVrkaa+/XJYsJad/pbQSV+MvNLQQ9qzlz+qCHAmm9G2CwGDbW8V586CdgRMx9k3kjVV3fkq5+diT5OwR3WLPygEeh0wnVEtdbCdrYb9vFoS1ypBmjsWcT0n5OMErHDhdR+4lxPzNJ1F7Cxr0kzWBKr+36afMaykC9ekwRra9xW1gIUTCWf7scTKvTvuVMcIAX3h67gbO6gQQT3fzE1DF6J6HATjrcKO3VzeZkhvZiJrxTtVZsxg=", // iossk
          isDebug: true,
          pageType: PageType.fullPort,

          // 导航栏设置 - 黑字透明底和返回键
          navHidden: false, // 显示导航栏
          navColor: "#00000000", // 透明背景 (ARGB格式，前两位00表示完全透明)
          navText: "运营商一键登录", // 导航栏标题
          navTextColor: "#000000", // 黑色文字
          navTextSize: 18, // 标题字体大小
          navReturnHidden: false, // 显示返回按钮
          navReturnImgPath: Platform.isAndroid
              ? "assets/images/nav_back_black_2x.png"
              : "assets/images/nav_back_black.png", // 黑色返回箭头图标
          navReturnImgWidth: 20, // 返回按钮宽度
          navReturnImgHeight: 20, // 返回按钮高度

          // Logo设置 - 红色盾牌图标
          logoImgPath: 'assets/images/logo.png',
          logoHidden: false,
          logoWidth: 100,
          logoHeight: 100,
          logoOffsetY: Platform.isAndroid ? 40 : 100, // 根据设计稿调整Logo位置
          logoScaleType: ScaleType.fitCenter,

          // 手机号码显示设置
          numberColor: "#000000", // 黑色文字
          numberSize: 24, // 较大字体
          numFieldOffsetY: Platform.isAndroid ? 180 : 240, // 手机号码位置
          numberLayoutGravity: Gravity.centerHorizntal,

          // 一键登录按钮设置 - 紫色渐变背景
          logBtnText: '本机号码一键登录',
          logBtnTextColor: '#FFFFFF', // 白色文字
          logBtnTextSize: 16,
          logBtnHeight: 50,
          logBtnMarginLeftAndRight: 20, // 左右边距
          logBtnOffsetY: Platform.isAndroid ? 240 : 300, // 按钮位置
          logBtnLayoutGravity: Gravity.centerHorizntal,
          logBtnBackgroundPath:
              "assets/images/login_btn_normal.png,assets/images/login_btn_normal.png,assets/images/login_btn_normal.png",

          // 切换按钮设置 - 白色背景，蓝色边框和文字
          switchAccText: "切换手机号登录",
          switchAccTextColor: "#4A90E2", // 蓝色文字
          switchAccTextSize: 16,
          switchAccHidden: false,
          switchOffsetY: Platform.isAndroid ? 320 : 380, // 切换按钮位置（登录按钮下方）
          switchAccBackgroundPath:
              "assets/images/switch_btn_normal.png,assets/images/switch_btn_press.png", // 需要白色背景的按钮图片

          // 隐私协议设置
          checkboxHidden: true, // 显示checkbox才能让其他配置生效
          privacyState: true, // 默认勾选，但仍需用户确认
          checkBoxWidth: 20,
          checkBoxHeight: 20,
          privacyTextSize: 12,
          privacyOffsetY: Platform.isAndroid ? 400 : 500, // 协议位置
          privacyMargin: 28, // 左边距30px
          protocolLayoutGravity: Gravity.centerHorizntal,
          privacyBefore: '登录即同意',
          protocolOneName: '《用户协议》',
          protocolOneURL: '${AlicloudConfig.authUIConfig['privacyOne'][1]}',
          protocolTwoName: ',《隐私政策》。',
          protocolTwoURL: AlicloudConfig.authUIConfig['privacyTwo'][1],
          protocolOwnOneColor: "#000000", // 协议链接蓝色
          protocolOwnTwoColor: "#000000",
          protocolOwnColor: "#000000",
          protocolColor: "#000000", // 普通文字黑色

          // 复选框图片
          uncheckedImgPath: "assets/images/checked_unchecked.png",
          checkedImgPath: "assets/images/checked_checked.png",

          // 背景设置 - 渐变背景
          backgroundColor: "#F5F5F5", // 浅灰色背景作为备用
          pageBackgroundPath: 'assets/images/login-bg.jpg', // 需要创建渐变背景图

          // 状态栏设置
          isStatusBarHidden: false,
          lightColor: false, // 深色状态栏内容（因为背景是浅色）
          statusBarColor: "#FFFFFF",

          // 隐藏loading - 解决点击登录按钮后的loading问题
          isHiddenLoading: true,
          
          // Android专用：隐藏登录按钮的Toast和Loading
          logBtnToastHidden: true,

          // Slogan隐藏（设计稿中没有显示）
          sloganHidden: true,
          toastText: '',
          toastBackground: '#00000000', // 设置toast背景为透明（ARGB格式）

          // 第三方登录配置 - 底部微信和Apple
          customThirdView: CustomThirdView.fromJson({
            'viewItemPath': Platform.isAndroid
                ? [
                    "assets/images/wechat.png",
                  ]
                : [
                    "assets/images/wechat.png",
                    "assets/images/apple.png",
                  ],
            'viewItemName': Platform.isAndroid ? [""] : ["", ""],
            'dividerImagePath': Platform.isAndroid
                ? 'assets/images/third-party-bg_2x.png'
                : 'assets/images/third-party-bg.png', // 分割线图片路径
            'top': Platform.isAndroid ? 580 : 680, // 第三方登录位置 - 页面底部
            'left': 20,
            'right': 20,
            'itemWidth': 40,
            'itemHeight': 40,
            'space': 50, // 微信和Apple图标之间的间距
            'color': '#666666',
            'size': 14,
          }),

          // 其他设置
          autoQuitPage: false,
          isHideToast: true, // 显示Toast，用于拦截协议未勾选的情况
          switchCheck: false, // 切换按钮不需要协议勾选
          vendorPrivacyPrefix: "《",
          vendorPrivacySuffix: "》",
          customReturnBtn: customReturnBtn);

      // 调用初始化方法，添加超时机制
      QLog('正在调用 AliAuth.initSdk...');
      QLog('当前平台: ${Platform.operatingSystem}');

      QLog('📱 第三方登录配置 (Android和iOS统一使用CustomThirdView):');
      QLog('📱 当前平台: ${Platform.operatingSystem}');
      QLog('📱 CustomThirdView配置: ${authModel.customThirdView?.toJson()}');
      QLog('📱 第三方登录按钮是否启用: ${authModel.customThirdView != null}');

      if (authModel.customThirdView != null) {
        final config = authModel.customThirdView!.toJson();
        QLog('📱 图片路径: ${config['viewItemPath']}');
        QLog('📱 按钮尺寸: ${config['itemWidth']}x${config['itemHeight']}');
        QLog('📱 按钮间距: ${config['space']}');
        QLog('📱 位置: top=${config['top']}, left=${config['left']}');
      }

      try {
        final result = await AliAuth.initSdk(authModel);
        QLog('阿里云DYPNS SDK初始化结果: $result');

        // iOS特殊处理：尝试预设隐私协议同意状态
        if (Platform.isIOS) {
          await _handleIOSPrivacyAgreement();
        }

        // Android平台：创建第三方登录视图
        if (Platform.isAndroid && authModel.customThirdView != null) {
          await _createAndroidThirdPartyView(authModel.customThirdView!);
        }
      } catch (e) {
        QLog('阿里云DYPNS SDK初始化异常: $e，但继续执行');
      }

      return true;
    } catch (e) {
      QLog('阿里云DYPNS SDK初始化失败: $e');
      return false;
    }
  }

  /// 关闭阿里云DYPNS授权页面
  Future<void> closeAuthPage() async {
    try {
      QLog('开始调用AliAuth.quitPage()...');
      // 使用超时处理，避免无限等待
      await AliAuth.quitPage().timeout(
        Duration(seconds: 3),
        onTimeout: () {
          QLog('⏰ AliAuth.quitPage()超时，可能页面已经关闭');
          return;
        },
      );
      QLog('✅ AliAuth.quitPage()调用成功，授权页面已关闭');
    } catch (e) {
      QLog('❌ AliAuth.quitPage()调用失败: $e');
      // 即使关闭失败也不抛出异常，让后续流程继续
    }
  }

  /// 处理运营商登录成功
  Future<void> _handleCarrierLoginSuccess(Map<String, dynamic> eventMap) async {
    // 防止重复处理
    if (_isProcessingLoginSuccess) {
      QLog('⚠️ [实例$instanceId] 运营商登录成功事件正在处理中，忽略重复触发');
      return;
    }

    _isProcessingLoginSuccess = true;

    try {
      QLog('🎯 [实例$instanceId] 处理运营商登录成功，事件数据: $eventMap');

      // 从事件数据中提取aliCode
      final aliCode = eventMap['data']?.toString();
      QLog("打印aliCode: $aliCode");
      if (aliCode == null || aliCode.isEmpty) {
        QLog('❌ 运营商登录成功但没有获取到aliCode');
        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': 'NO_ALI_CODE',
            'msg': '运营商登录成功但没有获取到aliCode'
          });
        }
        return;
      }

      QLog(
          '✅ 获取到aliCode: ${aliCode.length > 20 ? '${aliCode.substring(0, 20)}...' : aliCode}');
      QLog(
          '✅ 获取到clientId: ${clientId.length > 20 ? '${clientId.substring(0, 20)}...' : clientId}');

      // 调用后端运营商登录接口
      QLog('📞 调用后端运营商登录接口...');
      final apiResult = await API.dypnsLoginApi(
        aliCode: aliCode,
        clientId: clientId,
      );

      QLog('📋 后端运营商登录接口响应: ${apiResult.toJson()}');

      if (apiResult.state == 200) {
        // 登录成功
        QLog('🎉 运营商登录完全成功22');

        // 保存用户信息和token
        await _saveUserInfo(apiResult.data);

        // 先跳转到主Tab页面，然后异步关闭授权页面
        QLog('开始跳转到主Tab页面');
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
        QLog('跳转到主Tab页面完成');

        // 异步关闭授权页面，不阻塞主流程
        QLog('准备异步关闭授权页面...');
        closeAuthPage().then((_) {
          QLog('授权页面异步关闭完成');
        }).catchError((e) {
          QLog('授权页面异步关闭失败: $e');
        });

        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': true,
            'resultCode': '600000',
            'msg': '运营商登录成功',
            'loginType': 'carrier',
            'data': apiResult.data
          });
        }
      } else {
        // 登录失败
        QLog('❌ 后端运营商登录失败: ${apiResult.msg}');

        if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
          _loginCompleter!.complete({
            'success': false,
            'resultCode': apiResult.state.toString(),
            'msg': apiResult.msg ?? '运营商登录失败'
          });
        }
      }
    } catch (e) {
      QLog('❌ [实例$instanceId] 处理运营商登录成功异常: $e');

      if (_loginCompleter != null && !_loginCompleter!.isCompleted) {
        _loginCompleter!.complete({
          'success': false,
          'resultCode': 'CARRIER_LOGIN_ERROR',
          'msg': '运营商登录处理异常: $e'
        });
      }
    } finally {
      // 处理完成后重置标记
      _isAliAuthChecked = false;
      _isProcessingLoginSuccess = false;
      QLog('🔄 [实例$instanceId] 运营商登录成功处理完成，重置防重复标记');
    }
  }

  /// iOS特殊处理：预设隐私协议同意状态
  Future<void> _handleIOSPrivacyAgreement() async {
    try {
      QLog('🍎 iOS特殊处理：尝试预设隐私协议同意状态');

      // 方法1：尝试通过MethodChannel直接设置
      try {
        const platform = MethodChannel('alicloud_auth');
        await platform.invokeMethod('setPrivacyAgreement', {'agreed': true});
        QLog('🍎 ✅ 通过MethodChannel设置隐私协议同意状态成功');
      } catch (e) {
        QLog('🍎 ❌ MethodChannel设置失败: $e');
      }

      // 方法2：尝试通过ali_auth插件的内部方法
      try {
        // 这里可能需要调用ali_auth插件的特定方法
        // 由于插件API限制，我们先记录日志
        QLog('🍎 尝试其他iOS隐私协议处理方法...');
      } catch (e) {
        QLog('🍎 ❌ 其他方法失败: $e');
      }
    } catch (e) {
      QLog('🍎 ❌ iOS隐私协议处理异常: $e');
    }
  }

  /// 创建Android第三方登录视图
  Future<void> _createAndroidThirdPartyView(
      CustomThirdView customThirdView) async {
    try {
      QLog('🤖 Android平台：创建第三方登录视图');

      // 调用Android原生方法创建第三方登录视图
      final platform = MethodChannel('alicloud_auth');

      final result = await platform.invokeMethod('createThirdPartyView', {
        'customThirdView': customThirdView.toJson(),
      });

      if (result == true) {
        QLog('🤖 ✅ Android第三方登录视图创建成功');
      } else {
        QLog('🤖 ❌ Android第三方登录视图创建失败');
      }
    } catch (e) {
      QLog('🤖 ❌ 创建Android第三方登录视图异常: $e');
    }
  }

  /// 初始化设备clientId
  Future<void> _initializeClientId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        clientId = androidInfo.id; // Android设备ID
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        clientId = iosInfo.identifierForVendor ?? ''; // iOS设备ID
      }

      QLog('设备clientId: $clientId');
    } catch (e) {
      QLog('获取设备clientId失败: $e');
      // 如果获取失败，生成一个基于时间戳的ID作为备用
      clientId = 'client_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// 显示第三方登录的隐私协议确认弹窗
  Future<void> _showPrivacyAgreementDialogForThirdPartyLogin(
      String loginType, VoidCallback onConfirm) async {
    try {
      QLog('📋 [第三方登录] 开始显示隐私协议确认弹窗: $loginType');

      // 从配置中获取协议名称
      final protocolOneName =
          AlicloudConfig.authUIConfig['privacyOne']?[0] ?? '《用户协议》';
      final protocolTwoName =
          AlicloudConfig.authUIConfig['privacyTwo']?[0] ?? '《隐私政策》';

      QLog('📋 [第三方登录] 准备调用iOS原生弹窗，协议: $protocolOneName $protocolTwoName');

      // 调用原生系统级弹窗
      const platform = MethodChannel('ali_auth');

      // 增加超时处理，避免无限等待
      final result = await platform.invokeMethod('showPrivacyAgreementDialog', {
        'title': '用户协议及隐私保护政策',
        'message': '使用$loginType前，请阅读并同意\n$protocolOneName $protocolTwoName',
        'confirmText': '同意',
        'cancelText': '不同意',
      }).timeout(
        Duration(seconds: 30),
        onTimeout: () {
          QLog('📋 [第三方登录] ❌ 弹窗调用超时');
          return false;
        },
      );

      QLog('📋 [第三方登录] iOS原生弹窗返回结果: $result');

      if (result == true) {
        QLog('📋 [第三方登录] ✅ 用户同意隐私协议，开始执行$loginType');
        _isAliAuthChecked = true;

        // 延迟一下确保弹窗完全关闭
        await Future.delayed(Duration(milliseconds: 300));

        // 用户同意后，执行第三方登录
        onConfirm();
      } else {
        QLog('📋 [第三方登录] ❌ 用户拒绝隐私协议，取消$loginType流程');
        // 用户拒绝时，保持在运营商登录界面，不执行任何操作
        // 注意：这里不调用cancelLoginVCAnimated，保持界面不被关闭
      }
    } catch (e) {
      QLog('📋 [第三方登录] ❌ 显示隐私协议确认弹窗异常: $e');

      // 检查是否是平台方法不存在的错误
      if (e.toString().contains('MissingPluginException') ||
          e.toString().contains('not implemented')) {
        QLog('📋 [第三方登录] ❌ iOS原生方法未实现，使用Flutter弹窗降级');
        await _showFlutterPrivacyDialog(loginType, onConfirm);
      } else {
        // 其他错误，直接执行登录（降级处理）
        QLog('📋 [第三方登录] 弹窗失败，直接执行$loginType');
        onConfirm();
      }
    }
  }

  /// Flutter弹窗降级方案
  Future<void> _showFlutterPrivacyDialog(
      String loginType, VoidCallback onConfirm) async {
    try {
      QLog('📋 [第三方登录] 使用Flutter弹窗显示隐私协议确认');

      final protocolOneName =
          AlicloudConfig.authUIConfig['privacyOne']?[0] ?? '《用户协议》';
      final protocolTwoName =
          AlicloudConfig.authUIConfig['privacyTwo']?[0] ?? '《隐私政策》';

      final result = await Get.dialog<bool>(
        AlertDialog(
          title: Text('用户协议及隐私保护政策'),
          content:
              Text('使用$loginType前，请阅读并同意\n$protocolOneName $protocolTwoName'),
          actions: [
            TextButton(
              onPressed: () {
                QLog('📋 [第三方登录] Flutter弹窗 - 用户拒绝');
                Get.back(result: false);
              },
              child: Text('不同意'),
            ),
            TextButton(
              onPressed: () {
                QLog('📋 [第三方登录] Flutter弹窗 - 用户同意');
                Get.back(result: true);
              },
              child: Text('同意'),
            ),
          ],
        ),
        barrierDismissible: false,
      );

      if (result == true) {
        QLog('📋 [第三方登录] ✅ 用户同意隐私协议（Flutter弹窗），开始执行$loginType');
        _isAliAuthChecked = true;
        onConfirm();
      } else {
        QLog('📋 [第三方登录] ❌ 用户拒绝隐私协议（Flutter弹窗），取消$loginType流程');
      }
    } catch (e) {
      QLog('📋 [第三方登录] ❌ Flutter弹窗异常: $e，直接执行登录');
      onConfirm();
    }
  }
}

/// 运营商可用性检查结果
class CarrierAvailability {
  final bool isAvailable;
  final String? errorCode;
  final String? errorMessage;
  final String? operatorType;
  final bool isWifiConnected;

  CarrierAvailability({
    required this.isAvailable,
    this.errorCode,
    this.errorMessage,
    this.operatorType,
    this.isWifiConnected = false,
  });

  factory CarrierAvailability.fromMap(Map<String, dynamic> map) {
    return CarrierAvailability(
      isAvailable: map['isAvailable'] ?? false,
      errorCode: map['errorCode'],
      errorMessage: map['errorMessage'],
      operatorType: map['operatorType'],
      isWifiConnected: map['isWifiConnected'] ?? false,
    );
  }
}

/// 运营商登录结果
class CarrierLoginResult {
  final bool success;
  final String? errorCode;
  final String? errorMessage;
  final String? token;
  final String? accessCode;
  final String? operatorType;
  final String? phoneNumber; // 脱敏手机号

  CarrierLoginResult({
    required this.success,
    this.errorCode,
    this.errorMessage,
    this.token,
    this.accessCode,
    this.operatorType,
    this.phoneNumber,
  });

  factory CarrierLoginResult.fromMap(Map<String, dynamic> map) {
    return CarrierLoginResult(
      success: map['success'] ?? false,
      errorCode: map['errorCode'],
      errorMessage: map['errorMessage'],
      token: map['token'],
      accessCode: map['accessCode'],
      operatorType: map['operatorType'],
      phoneNumber: map['phoneNumber'],
    );
  }
}

/// Token验证结果
class TokenVerifyResult {
  final bool success;
  final String? errorCode;
  final String? errorMessage;
  final String? phoneNumber; // 真实手机号
  final String? carrier; // 运营商

  TokenVerifyResult({
    required this.success,
    this.errorCode,
    this.errorMessage,
    this.phoneNumber,
    this.carrier,
  });

  factory TokenVerifyResult.fromMap(Map<String, dynamic> map) {
    return TokenVerifyResult(
      success: map['success'] ?? false,
      errorCode: map['errorCode'],
      errorMessage: map['errorMessage'],
      phoneNumber: map['phoneNumber'],
      carrier: map['carrier'],
    );
  }
}

/// 错误码定义
class CarrierLoginErrorCode {
  static const String networkError = 'NETWORK_ERROR';
  static const String notSupported = 'NOT_SUPPORTED';
  static const String userCanceled = 'USER_CANCELED';
  static const String timeout = 'TIMEOUT';
  static const String initFailed = 'INIT_FAILED';
  static const String preLoginFailed = 'PRELOGIN_FAILED';
  static const String loginFailed = 'LOGIN_FAILED';
  static const String wifiConnected = 'WIFI_CONNECTED';
  static const String operatorNotSupported = 'OPERATOR_NOT_SUPPORTED';
  static const String verifyFailed = 'VERIFY_FAILED';
}
