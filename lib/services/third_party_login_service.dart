import 'dart:async';
import 'dart:io';
import 'package:lima/utils/q_log.dart';
import 'package:lima/common/bridge_controller.dart';
import 'package:lima/utils/http/api.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';

/// 第三方登录服务
/// 统一处理微信登录和Apple登录逻辑
class ThirdPartyLoginService {
  static ThirdPartyLoginService? _instance;
  static ThirdPartyLoginService get instance => _instance ??= ThirdPartyLoginService._();

  ThirdPartyLoginService._();

  /// 微信登录
  Future<ThirdPartyLoginResult> wechatLogin() async {
    try {
      QLog('开始微信登录...');
      
      // 调用微信授权
      final wechatResult = await JsBridgeController.instance.wxAuth();
      
      if (wechatResult == null || wechatResult == -1) {
        return ThirdPartyLoginResult(
          success: false,
          errorCode: 'WECHAT_CANCEL',
          errorMessage: '微信登录取消',
        );
      }
      
      QLog('微信授权成功，调用后端API...');
      QLog("微信回调完整数据: $wechatResult");
      
      // 获取设备clientId
      final clientId = await _getClientId();
      
      // 调用后端微信登录接口
      final result = await API.wechatLoginApi(
        openid: wechatResult['wxOpenid'] ?? '',
        unionid: wechatResult['wxUnionid'],
        nickname: wechatResult['nickname'],
        sex: wechatResult['sex']?.toString() ?? '',
        province: wechatResult['province'] ?? '',
        city: wechatResult['city'] ?? '',
        country: wechatResult['country'] ?? '',
        headimgurl: wechatResult['img'],
        clientId: clientId,
        appType: Platform.isIOS ? '2' : '1',
      );

      QLog('微信登录API响应: ${result.toJson()}');

      if (result.code == 200 || result.state == 200) {
        return ThirdPartyLoginResult(
          success: true,
          loginType: 'wechat',
          userData: result.data,
          errorCode: null,
          errorMessage: null,
        );
      } else {
        return ThirdPartyLoginResult(
          success: false,
          errorCode: 'WECHAT_API_ERROR',
          errorMessage: result.msg ?? '微信登录失败',
        );
      }
    } catch (e) {
      QLog('微信登录失败: $e');
      return ThirdPartyLoginResult(
        success: false,
        errorCode: 'WECHAT_ERROR',
        errorMessage: '微信登录失败: $e',
      );
    }
  }

  /// Apple登录
  Future<ThirdPartyLoginResult> appleLogin() async {
    try {
      if (!GetPlatform.isIOS) {
        return ThirdPartyLoginResult(
          success: false,
          errorCode: 'APPLE_NOT_SUPPORTED',
          errorMessage: 'Apple登录仅支持iOS',
        );
      }
      
      QLog('开始Apple登录...');
      
      // 调用苹果登录
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      var nickname = '';
      if (credential.givenName != null && credential.familyName != null) {
        nickname = '${credential.givenName}${credential.familyName}';
      }
      
      // 获取设备clientId
      final clientId = await _getClientId();

      // 调用后端苹果登录接口
      final result = await API.appleLoginApi(
        appleOpenid: credential.userIdentifier ?? '',
        nickname: nickname,
        email: credential.email,
        clientId: clientId,
        appType: Platform.isIOS ? '2' : '1',
      );

      QLog('Apple登录API响应: ${result.toJson()}');

      if (result.code == 200 || result.state == 200) {
        return ThirdPartyLoginResult(
          success: true,
          loginType: 'apple',
          userData: result.data,
          errorCode: null,
          errorMessage: null,
        );
      } else {
        return ThirdPartyLoginResult(
          success: false,
          errorCode: 'APPLE_API_ERROR',
          errorMessage: result.msg ?? 'Apple登录失败',
        );
      }
    } catch (e) {
      QLog('Apple登录失败: $e');
      return ThirdPartyLoginResult(
        success: false,
        errorCode: 'APPLE_ERROR',
        errorMessage: 'Apple登录失败: $e',
      );
    }
  }

  /// 获取设备clientId
  Future<String> _getClientId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String clientId = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        clientId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        clientId = iosInfo.identifierForVendor ?? '';
      }

      return clientId.isNotEmpty ? clientId : 'client_${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      QLog('获取设备clientId失败: $e');
      return 'client_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
}

/// 第三方登录结果
class ThirdPartyLoginResult {
  final bool success;
  final String? loginType; // 'wechat' 或 'apple'
  final dynamic userData; // 用户数据
  final String? errorCode;
  final String? errorMessage;

  ThirdPartyLoginResult({
    required this.success,
    this.loginType,
    this.userData,
    this.errorCode,
    this.errorMessage,
  });

  factory ThirdPartyLoginResult.fromMap(Map<String, dynamic> map) {
    return ThirdPartyLoginResult(
      success: map['success'] ?? false,
      loginType: map['loginType'],
      userData: map['userData'],
      errorCode: map['errorCode'],
      errorMessage: map['errorMessage'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'success': success,
      'loginType': loginType,
      'userData': userData,
      'errorCode': errorCode,
      'errorMessage': errorMessage,
    };
  }

  @override
  String toString() {
    return 'ThirdPartyLoginResult{success: $success, loginType: $loginType, errorCode: $errorCode, errorMessage: $errorMessage, userData: $userData}';
  }
}
