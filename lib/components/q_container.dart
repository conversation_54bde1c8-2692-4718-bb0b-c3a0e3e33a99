import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 背景纵向渐变 Container
class QContainerGradient extends Container {
  QContainerGradient({
    Key? key,
    required List<Color> colors,
    List<double>? stops,
    Alignment? begin,
    Alignment? end,
    TileMode tileMode = TileMode.repeated,
    super.height,
    super.width,
    super.child,
    super.padding,
    super.margin,
    BorderRadiusGeometry? borderRadius,
  }) : super(
          key: key,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: colors,
              begin: begin ?? Alignment.topCenter,
              end: end ?? Alignment.bottomCenter,
              stops: stops,
              tileMode: tileMode,
            ),
            borderRadius: borderRadius,
          ),
        );
}

/// 圆角
class QContainerCircle extends Container {
  QContainerCircle({
    super.child,
    super.width,
    super.height,
    double? radius,
    Color? color,
    super.padding,
    super.margin,
    super.alignment,
    super.constraints,
    String? backgroundImage,
    Color? shadowColor,
    BoxBorder? border,
    BorderRadiusGeometry? borderRadius,
  })  : assert(borderRadius == null || radius == null,
            "borderRadius 和 radiu 不能同时设置"),
        super(
          decoration: BoxDecoration(
            boxShadow: shadowColor == null
                ? null
                : [
                    BoxShadow(
                      color: shadowColor,
                      blurRadius: radius ?? 8.r,
                      spreadRadius: 0,
                      offset: Offset(2, 2),
                    ),
                  ],
            borderRadius: borderRadius ?? BorderRadius.circular(radius ?? 6.r),
            color: color,
            border: border,
            image: backgroundImage == null
                ? null
                : DecorationImage(
                    image: AssetImage(backgroundImage),
                    fit: BoxFit.fill,
                  ),
          ),
        );
}
