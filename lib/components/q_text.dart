import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lima/res/colors.dart';

class QText extends Text {
  /// default fontSzie 13.sp
  /// default color 0xFF848484
  /// default fontWeight normal
  QText(
    super.data, {
    TextStyle? style,
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? lineHeight,
    super.textAlign,
    super.maxLines,
    super.softWrap,
    super.overflow,
    super.selectionColor,
  }) : super(
          style: style ??
              TextStyle(
                fontWeight: fontWeight ?? FontWeight.normal,
                color: color ?? AppColors.color_FF848484,
                fontSize: fontSize ?? 13.sp,
                height: lineHeight,
                overflow: TextOverflow.ellipsis,
              ),
        );
}
