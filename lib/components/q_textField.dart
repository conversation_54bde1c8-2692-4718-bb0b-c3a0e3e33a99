import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lima/res/colors.dart';

class QTextField extends TextField {
  QTextField({
    super.controller,
    super.maxLines = 1,
    super.maxLength,
    super.inputFormatters,
    super.keyboardType,
    super.obscureText,
    super.onChanged,
    super.onSubmitted,
    super.textAlign,
    super.enabled,
    super.textInputAction,

    /// 边框默认透明色
    InputBorder? border,

    /// filled 为true 背景色 fillColor,
    // bool filled = false,
    EdgeInsetsGeometry? contentPadding,
    // 需要设置 filled 为 ture
    // Color? fillColor,
    String? hintText,
    TextStyle? hintStyle,
    TextStyle? style,

    /// 计数器样式
    TextStyle? counterStyle,
    FocusNode? focusNode,
  }) : super(
          style: style ??
              TextStyle(
                fontSize: 13.sp,
                color: AppColors.color_FF1D1D1D,
              ),
          focusNode: focusNode,
          decoration: InputDecoration(
            hintText: hintText ?? "请输入",
            hintStyle: hintStyle ??
                TextStyle(
                  fontSize: 13.sp,
                  color: AppColors.color_FFC5C7C9,
                ),
            border: border ??
                OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
                ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: Colors.transparent,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: Colors.transparent,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: Colors.transparent,
              ),
            ),
            contentPadding: contentPadding ?? EdgeInsets.all(0),
            counterStyle: counterStyle,
          ),
        );
}
