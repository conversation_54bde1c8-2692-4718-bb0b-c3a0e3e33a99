import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// appbar 返回按钮类型
enum AppBarBackType { Back, Close, None }

const double kNavigationBarHeight = 44.0;

// 自定义 AppBar
class MyAppBar extends AppBar implements PreferredSizeWidget {
  MyAppBar({
    Key? key,
    super.title,
    AppBarBackType? leadingType,
    WillPopCallback? onWillPop,
    Widget? leading,
    Color? backBtnColor,
    super.systemOverlayStyle = SystemUiOverlayStyle.light,
    super.backgroundColor,
    super.actions,
    super.centerTitle = true,
    super.elevation = 0,
    super.bottom,
  }) : super(
          key: key,
          leading: leading ??
              (leadingType == AppBarBackType.None
                  ? Container()
                  : AppBarBack(
                      leadingType ?? AppBarBackType.Back,
                      color: backBtnColor,
                      onWillPop: onWillPop,
                    )),
        );
  @override
  get preferredSize => Size.fromHeight(44.h);
}

// 自定义返回按钮
class AppBarBack extends StatelessWidget {
  final AppBarBackType _backType;
  final Color? color;
  final WillPopCallback? onWillPop;

  AppBarBack(this._backType, {this.onWillPop, this.color});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final willBack = onWillPop == null ? true : await onWillPop!();
        if (!willBack) return;
        Navigator.pop(context);
      },
      child: _backType == AppBarBackType.Close
          ? Container(
              color:  Colors.transparent,
              child: Icon(
                Icons.close,
                color: color ?? Color(0xFF222222),
                size: 24.0,
              ),
            )
          : Container(
              padding: EdgeInsets.only(right: 15),
              // color: Colors.red,
              color:  Colors.transparent,
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 24.0,
                color: color ?? Color(0xFF222222),
              ),
            ),
    );
  }
}

class MyTitle extends StatelessWidget {
  final String _title;
  final Color? color;
  final double? fontSize;

  MyTitle(this._title, {this.color, this.fontSize});

  @override
  Widget build(BuildContext context) {
    return Text(
      _title,
      style: TextStyle(
        color: color ?? Color(0xFF222222),
        fontSize: fontSize ?? 18.sp,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
