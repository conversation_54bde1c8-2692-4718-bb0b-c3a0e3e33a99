import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_container.dart';
import 'package:lima/components/q_divider.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/components/q_textField.dart';

import '../res/colors.dart';

class QAlert extends StatelessWidget {
  final String? title;
  final String? contentStr;
  final Widget? child;
  final bool tapDismiss;
  final String cancelTitle;
  final Color cancelTitleColor;
  final String confirmTitle;
  final Color confirmTitleColor;
  final GestureTapCallback? onCancel;
  final GestureTapCallback? onConfim;
  final bool showCancel;///是否显示取消按钮
  QAlert({
    super.key,
    this.title,
    this.contentStr,
    this.child,
    this.tapDismiss = true,
    this.cancelTitle = "取消",
    this.cancelTitleColor = AppColors.color_FF848484,
    this.confirmTitle = "确定",
    this.confirmTitleColor = AppColors.color_FF2B6BFF,
    this.onCancel,
    this.onConfim,
    this.showCancel = true,
  }) : assert(child == null || contentStr == null, "child 和 contentStr 不可同时使用");

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tapDismiss
          ? () {
              Get.back();
            }
          : null,
      child: Scaffold(
        backgroundColor: AppColors.color_00FFFFFF,
        body: Center(
          child: GestureDetector(
            onTap: () {},
            child: QContainerCircle(
              radius: 12.r,
              padding: EdgeInsets.only(top: 30.h),
              color: AppColors.color_FFFFFFFF,
              margin: EdgeInsets.symmetric(horizontal: 32.w),
              constraints: BoxConstraints(minHeight: 100.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Visibility(
                    visible: title != null,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      alignment: Alignment.center,
                      child: QText(
                        title ?? "",
                        textAlign: TextAlign.center,
                        color: AppColors.color_FF1D1D1D,
                        fontSize: 18.sp,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 10.h,
                      left: 20.w,
                      right: 20.w,
                      bottom: 20.h,
                    ),
                    width: double.infinity,
                    alignment: Alignment.topCenter,
                    child: child != null
                        ? child
                        : QText(
                            contentStr ?? "",
                            color: AppColors.color_FF848484,
                            fontSize: 14.sp,
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.visible,
                          ),
                  ),
                  QDivider(),
                  Row(
                    children: [
                      Visibility(
                        visible: showCancel,
                        child: Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              Get.back();
                              onCancel?.call();
                            },
                            child: Container(
                              height: 44.h,
                              child: Center(
                                child: QText(
                                  cancelTitle,
                                  color: cancelTitleColor,
                                  fontSize: 17.sp,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: showCancel,
                        child: Container(
                          color: AppColors.dividerColor,
                          width: 0.5.w,
                          height: 44.h,
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            Get.back();
                            onConfim?.call();
                          },
                          child: Container(
                            height: 44.h,
                            child: Center(
                              child: QText(
                                confirmTitle,
                                color: confirmTitleColor,
                                fontSize: 17.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class QInputAlert extends StatelessWidget {
  final String? title;
  final String? text;
  final String? hintText;
  final Widget? child;
  final bool tapDismiss;
  final String cancelTitle;
  final Color cancelTitleColor;
  final String confirmTitle;
  final Color confirmTitleColor;
  final GestureTapCallback? onCancel;
  final void Function(String text)? onConfim;
  final bool? dismissOnConfirm; /// 点击确定的时候关闭dialog
  final TextEditingController controller = TextEditingController();
  QInputAlert({
    super.key,
    this.title,
    this.text,
    this.hintText,
    this.child,
    this.tapDismiss = true,
    this.cancelTitle = "取消",
    this.cancelTitleColor = AppColors.color_FF848484,
    this.confirmTitle = "确定",
    this.confirmTitleColor = AppColors.color_FF2B6BFF,
    this.onCancel,
    this.onConfim,
    this.dismissOnConfirm = true,
  }) : assert(child == null || hintText == null, "child 和 hintText 不可同时使用");

  @override
  Widget build(BuildContext context) {
    controller.text = text ?? "";
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tapDismiss
          ? () {
              Get.back();
            }
          : null,
      child: Scaffold(
        backgroundColor: AppColors.color_00FFFFFF,
        body: Center(
          child: GestureDetector(
            onTap: () {},
            child: QContainerCircle(
              radius: 12.r,
              padding: EdgeInsets.only(top: 30.h),
              color: AppColors.color_FFFFFFFF,
              margin: EdgeInsets.symmetric(horizontal: 32.w),
              constraints: BoxConstraints(minHeight: 100.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Visibility(
                    visible: title != null,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      alignment: Alignment.center,
                      child: QText(
                        title ?? "",
                        textAlign: TextAlign.center,
                        color: AppColors.color_FF1D1D1D,
                        fontSize: 18.sp,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 10.h,
                      left: 20.w,
                      right: 20.w,
                      bottom: 20.h,
                    ),
                    width: double.infinity,
                    alignment: Alignment.topCenter,
                    child: child != null
                        ? child
                        : QContainerCircle(
                            radius: 4.r,
                            color: AppColors.color_FFF4F6FA,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: QTextField(
                              controller: controller,
                              hintText: hintText,
                            ),
                          ),
                  ),
                  QDivider(),
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            Get.back();
                            onCancel?.call();
                          },
                          child: Container(
                            height: 44.h,
                            child: Center(
                              child: QText(
                                cancelTitle,
                                color: cancelTitleColor,
                                fontSize: 17.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        color: AppColors.dividerColor,
                        width: 0.5.w,
                        height: 44.h,
                      ),
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            if (dismissOnConfirm == true) {
                              Get.back();
                            }
                            onConfim?.call(controller.text);
                          },
                          child: Container(
                            height: 44.h,
                            child: Center(
                              child: QText(
                                confirmTitle,
                                color: confirmTitleColor,
                                fontSize: 17.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
