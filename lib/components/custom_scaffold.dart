import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../res/colors.dart';
import 'custom_appbar.dart';

class BaseScaffold extends Scaffold {
  BaseScaffold({
    String? title,
    PreferredSizeWidget? appBar,
    required super.body,
    List<Widget>? actions,
    AppBarBackType? leadType,
    WillPopCallback? onWillPop,
    SystemUiOverlayStyle systemOverlayStyle = SystemUiOverlayStyle.light,
    super.floatingActionButton,
    Color appBarBackgroundColor = AppColors.color_FFFFFFFF,
    Color? titleColor,
    bool centerTitle = true,
    super.floatingActionButtonLocation,
    super.backgroundColor = AppColors.color_FFF8F8FA,
    super.extendBodyBehindAppBar,
    PreferredSizeWidget? bottom,
    super.bottomNavigationBar,
    double? elevation = 0,
    super.resizeToAvoidBottomInset,
    Color? backBtnColor,
  }) : super(
          appBar: appBar ??
              MyAppBar(
                systemOverlayStyle: systemOverlayStyle,
                leadingType: leadType ?? AppBarBackType.Back,
                onWillPop: onWillPop,
                actions: actions ?? [],
                centerTitle: centerTitle,
                title: MyTitle(
                  title ?? '',
                  color: titleColor ?? AppColors.color_FF1D1D1D,
                ),
                elevation: elevation,
                backgroundColor: appBarBackgroundColor,
                bottom: bottom,
                backBtnColor: backBtnColor,
              ),
        );
}

/// 自定义底部导航栏
class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 88.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              index: 0,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              label: '首页',
            ),
            _buildNavItem(
              index: 1,
              icon: Icons.directions_car_outlined,
              activeIcon: Icons.directions_car,
              label: '车辆',
            ),
            _buildNavItem(
              index: 2,
              icon: Icons.store_outlined,
              activeIcon: Icons.store,
              label: '服务',
            ),
            _buildNavItem(
              index: 3,
              icon: Icons.person_outline,
              activeIcon: Icons.person,
              label: '我的',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final isActive = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: 60.w,
        height: 60.h,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标容器
            Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: isActive ? const Color(0xFFE53E3E) : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isActive ? activeIcon : icon,
                size: 24.sp,
                color: isActive ? Colors.white : const Color(0xFF666666),
              ),
            ),
            SizedBox(height: 4.h),
            // 标签文字
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: isActive ? const Color(0xFFE53E3E) : const Color(0xFF666666),
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 带有红色圆形背景的首页图标版本
class CustomBottomNavigationWithCircle extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigationWithCircle({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 88.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildSpecialNavItem(
              index: 0,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              label: '首页',
              isSpecial: true, // 首页使用特殊样式
            ),
            _buildSpecialNavItem(
              index: 1,
              icon: Icons.directions_car_outlined,
              activeIcon: Icons.directions_car,
              label: '车辆',
            ),
            _buildSpecialNavItem(
              index: 2,
              icon: Icons.store_outlined,
              activeIcon: Icons.store,
              label: '服务',
            ),
            _buildSpecialNavItem(
              index: 3,
              icon: Icons.person_outline,
              activeIcon: Icons.person,
              label: '我的',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
    bool isSpecial = false,
  }) {
    final isActive = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: 60.w,
        height: 60.h,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标容器
            Container(
              width: isSpecial ? 48.w : 40.w,
              height: isSpecial ? 48.h : 40.h,
              decoration: BoxDecoration(
                color: (isSpecial || isActive) ? const Color(0xFFE53E3E) : Colors.transparent,
                shape: BoxShape.circle,
                boxShadow: isSpecial ? [
                  BoxShadow(
                    color: const Color(0xFFE53E3E).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
              child: Icon(
                (isSpecial || isActive) ? activeIcon : icon,
                size: isSpecial ? 28.sp : 24.sp,
                color: (isSpecial || isActive) ? Colors.white : const Color(0xFF666666),
              ),
            ),
            SizedBox(height: 4.h),
            // 标签文字
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: (isSpecial || isActive) ? const Color(0xFFE53E3E) : const Color(0xFF666666),
                fontWeight: (isSpecial || isActive) ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 使用示例：
///
/// class MainPage extends StatefulWidget {
///   @override
///   _MainPageState createState() => _MainPageState();
/// }
///
/// class _MainPageState extends State<MainPage> {
///   int _currentIndex = 0;
///
///   final List<Widget> _pages = [
///     HomePage(),
///     VehiclePage(),
///     ServicePage(),
///     ProfilePage(),
///   ];
///
///   @override
///   Widget build(BuildContext context) {
///     return Scaffold(
///       body: _pages[_currentIndex],
///       bottomNavigationBar: CustomBottomNavigationWithCircle(
///         currentIndex: _currentIndex,
///         onTap: (index) {
///           setState(() {
///             _currentIndex = index;
///           });
///         },
///       ),
///     );
///   }
/// }
