import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../pages/old/web/web_page.dart';
import '../utils/q_log.dart';

/// 高层级WebView组件，确保显示在ali_auth之上
class OverlayWebView extends StatefulWidget {
  final String url;
  final String title;
  final VoidCallback? onClose;

  const OverlayWebView({
    Key? key,
    required this.url,
    required this.title,
    this.onClose,
  }) : super(key: key);

  @override
  State<OverlayWebView> createState() => _OverlayWebViewState();
}

class _OverlayWebViewState extends State<OverlayWebView> {
  WebController? myWebController;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Container(
        color: Colors.white,
        child: SafeArea(
          child: Column(
            children: [
              // 自定义标题栏
              Container(
                height: 56,
                padding: EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      offset: Offset(0, 1),
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // 返回按钮
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () {
                        QLog('🔗 关闭OverlayWebView');
                        widget.onClose?.call();
                        Get.back();
                      },
                    ),
                    // 标题
                    Expanded(
                      child: Text(
                        widget.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // 占位，保持标题居中
                    SizedBox(width: 48),
                  ],
                ),
              ),
              // WebView内容
              Expanded(
                child: WebPage(
                  homeUrl: widget.url,
                  onWebCreated: (c) {
                    myWebController = c;
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    myWebController?.dispose();
    super.dispose();
  }
}

/// 显示高层级WebView的方法
class OverlayWebViewManager {
  static OverlayEntry? _overlayEntry;

  /// 显示WebView覆盖层
  static void show({
    required String url,
    required String title,
    VoidCallback? onClose,
  }) {
    if (_overlayEntry != null) {
      hide(); // 先关闭已存在的覆盖层
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => OverlayWebView(
        url: url,
        title: title,
        onClose: () {
          hide();
          onClose?.call();
        },
      ),
    );

    // 获取当前的Overlay并插入
    final overlay = Overlay.of(Get.context!);
    overlay.insert(_overlayEntry!);
    
    QLog('🔗 OverlayWebView已显示，URL: $url');
  }

  /// 隐藏WebView覆盖层
  static void hide() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      QLog('🔗 OverlayWebView已隐藏');
    }
  }
}
