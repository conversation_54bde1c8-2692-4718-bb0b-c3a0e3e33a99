import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 用户协议确认弹框
class PrivacyAgreementDialog extends StatelessWidget {
  final String? protocolOneName;
  final String? protocolTwoName;
  final VoidCallback? onAgree;
  final VoidCallback? onDisagree;

  const PrivacyAgreementDialog({
    Key? key,
    this.protocolOneName,
    this.protocolTwoName,
    this.onAgree,
    this.onDisagree,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.black54,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 40),
                
                // 标题
                const Text(
                  '用户协议及隐私保护政策',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // 协议内容
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      style: const TextStyle(
                        fontSize: 14,
                        height: 1.6,
                        color: Color(0xFF666666),
                      ),
                      children: [
                        const TextSpan(text: '我已阅读并同意\n'),
                        TextSpan(
                          text: protocolOneName ?? '《用户协议》',
                          style: const TextStyle(
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const TextSpan(text: ' '),
                        TextSpan(
                          text: protocolTwoName ?? '《隐私政策》',
                          style: const TextStyle(
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // 按钮区域
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      // 不同意按钮
                      Expanded(
                        child: Container(
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24),
                            border: Border.all(
                              color: const Color(0xFFE5E5E5),
                              width: 1,
                            ),
                          ),
                          child: TextButton(
                            onPressed: () {
                              Get.back();
                              onDisagree?.call();
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: const Color(0xFF999999),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: const Text(
                              '不同意',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      // 同意按钮
                      Expanded(
                        child: Container(
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24),
                            gradient: const LinearGradient(
                              colors: [Color(0xFF8B7CF6), Color(0xFFA78BFA)],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                          child: TextButton(
                            onPressed: () {
                              Get.back();
                              onAgree?.call();
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: const Text(
                              '同意',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // 底部安全区域
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示协议确认弹框
  static Future<bool?> show({
    String? protocolOneName,
    String? protocolTwoName,
  }) async {
    bool? result;
    
    await Get.dialog(
      PrivacyAgreementDialog(
        protocolOneName: protocolOneName,
        protocolTwoName: protocolTwoName,
        onAgree: () {
          result = true;
        },
        onDisagree: () {
          result = false;
        },
      ),
      barrierDismissible: false,
    );
    
    return result;
  }
}