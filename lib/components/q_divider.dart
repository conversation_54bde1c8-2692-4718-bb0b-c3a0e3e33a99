import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lima/res/colors.dart';

class QDivider extends Divider {
  final double? qHeight;
  final Color? qColor;
  QDivider({
    super.key,
    this.qHeight,
    super.thickness,
    super.indent,
    super.endIndent,
    this.qColor,
  }) : super(
          height: qHeight ?? 0.5.h,
          color: qColor ?? AppColors.dividerColor,
        );
}
