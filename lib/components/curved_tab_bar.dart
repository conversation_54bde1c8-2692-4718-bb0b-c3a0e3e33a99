import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as Math;

import 'package:lima/res/colors.dart';

class CurvedTabBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onTap;
  final List<TabBarItem> items;
  final Color backgroundColor;
  final Color selectedColor;
  final Color unselectedColor;

  const CurvedTabBar({
    super.key,
    required this.selectedIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor = Colors.white,
    this.selectedColor = Colors.transparent,
    this.unselectedColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80.h, // 缩小TabBar高度匹配背景图片
      child: Stack(
        children: [
          // 背景容器 - 透明背景
          Container(
            decoration: BoxDecoration(
              color: Colors.white, // 改为透明背景
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
          ),

          // 选中项的弧形背景图片 - 放在中间层，大小与选中Tab项一致
          if (selectedIndex >= 0 && selectedIndex < items.length)
            Positioned(
              left: selectedIndex * (MediaQuery.of(context).size.width / items.length),
              top: 0,
              child: Container(
                width: MediaQuery.of(context).size.width / items.length,
                height: 60.h, // 匹配TabBar高度
                color: Colors.grey[300],
                child: Image.asset(
                  'assets/tab-bar/tabbar-sel-bg.png',
                  width: 100.w,  // 缩小宽度
                  height: 60.h, // 进一步调整高度
                  fit: BoxFit.fill, // 改为contain确保完全显示
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: MediaQuery.of(context).size.width / items.length,
                      height: 60.h, // 匹配TabBar高度
                      color: Colors.red.withValues(alpha: 0.3),
                      child: Center(
                        child: Text(
                          'IMG ERROR',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

          // TabBar内容 - 放在最上层，确保图标不被遮挡
          SafeArea(
            child: Row(
              children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == selectedIndex;

              return Expanded(
                child: GestureDetector(
                  onTap: () => onTap(index),
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 60.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 图标容器 - 选中时添加向上偏移以匹配背景图片凹槽
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          transform: isSelected
                            ? Matrix4.translationValues(0, -8.h, 0) // 选中时向上偏移
                            : Matrix4.identity(),
                          width: isSelected ? 36.w : 36.w, // 缩小图标容器适应TabBar高度
                          height: isSelected ? 36.w : 36.w,
                          decoration: BoxDecoration(
                            color: isSelected ? AppColors.primary : Colors.transparent, // 选中时使用灰色背景
                            borderRadius: BorderRadius.circular(isSelected ? 18.w : 18.w), // 调整圆角适应缩小的容器
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: selectedColor.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Center(
                            child: Image.asset(
                              isSelected ? item.selectedIcon : item.icon,
                              width: isSelected ? 24.w : 24.w, // 缩小图标大小
                              height: isSelected ? 24.w : 24.w,
                              color: isSelected ? Colors.white : unselectedColor, // 保持选中时白色图标
                            ),
                          ),
                        ),

                        // 文字标签 - 选中时隐藏
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          height: isSelected ? 0 : 13.h, // 缩小文字标签高度
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 200),
                            opacity: isSelected ? 0.0 : 1.0,
                            child: Padding(
                              padding: EdgeInsets.only(top: isSelected ? 0 : 0), // 减少间距
                              child: Text(
                                item.label,
                                style: TextStyle(
                                  fontSize: 8.sp, // 缩小字体
                                  color: unselectedColor,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        ],
      ),
    );
  }
}

class TabBarItem {
  final String icon;
  final String selectedIcon;
  final String label;

  const TabBarItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}


