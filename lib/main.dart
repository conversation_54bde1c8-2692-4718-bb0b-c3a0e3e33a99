import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/global.dart';
import 'package:lima/routers/app_router.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // 立即设置屏幕方向
  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);

  // 立即启动UI，不等待初始化完成
  runApp(const MyApp());

  // 异步执行初始化，不阻塞UI显示
  Global.init();
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        // useInheritedMediaQuery: true,
        builder: (context, child) {
          return GetMaterialApp(
            title: '立马科技',
            theme: ThemeData(
              highlightColor: const Color.fromRGBO(0, 0, 0, 0),
              splashColor: const Color.fromRGBO(0, 0, 0, 0),
            ),
            debugShowCheckedModeBanner: false,
            enableLog: true,
            initialRoute: AppRoute.mainTab,
            getPages: AppRoute.pages,
            builder: EasyLoading.init(builder: (context, child) {
              return Scaffold(
                body: child,
              );
            }),
          );
        });
  }
}
