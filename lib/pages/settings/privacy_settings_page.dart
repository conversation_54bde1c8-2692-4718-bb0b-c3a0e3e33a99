import 'package:flutter/material.dart';
import 'package:get/get.dart';
// MobSDK 已移除
// import 'package:lima/utils/mob_sdk.dart';
import 'package:lima/widgets/privacy_policy_dialog.dart';
import 'package:lima/utils/q_log.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _isPrivacyGranted = false;

  @override
  void initState() {
    super.initState();
    _loadPrivacyStatus();
  }

  /// 加载隐私协议状态
  Future<void> _loadPrivacyStatus() async {
    try {
      final shouldShow = await PrivacyPolicyDialog.shouldShowPrivacyDialog();
      setState(() {
        _isPrivacyGranted = !shouldShow;
      });
    } catch (e) {
      QLog('加载隐私协议状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私设置'),
        backgroundColor: const Color(0xFFC70E2D),
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 隐私政策状态卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    _isPrivacyGranted ? Icons.check_circle : Icons.cancel,
                    color: _isPrivacyGranted ? Colors.green : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '隐私政策状态',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _isPrivacyGranted ? '已同意' : '未同意',
                          style: TextStyle(
                            fontSize: 14,
                            color: _isPrivacyGranted ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 操作按钮
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.visibility, color: Color(0xFFC70E2D)),
                  title: const Text('查看隐私政策'),
                  subtitle: const Text('重新查看完整的隐私政策内容'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _showPrivacyPolicy,
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.refresh, color: Color(0xFFC70E2D)),
                  title: const Text('重新确认隐私政策'),
                  subtitle: const Text('重新进行隐私政策确认流程'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _resetAndShowPrivacyPolicy,
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.info_outline, color: Color(0xFFC70E2D)),
                  title: const Text('获取隐私协议内容'),
                  subtitle: const Text('获取 MobTech 隐私协议详细内容'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _getMobPrivacyContent,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 说明文本
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '关于隐私政策',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '我们使用 MobTech MobLinkSDK 为您提供场景还原功能。为了保护您的隐私，我们需要您的明确授权。您可以随时在此页面重新确认或撤回授权。',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示隐私政策对话框
  Future<void> _showPrivacyPolicy() async {
    await PrivacyPolicyDialog.showPrivacyDialog(context);
    _loadPrivacyStatus(); // 重新加载状态
  }

  /// 重置并显示隐私政策
  Future<void> _resetAndShowPrivacyPolicy() async {
    try {
      await PrivacyPolicyDialog.resetPrivacyStatus();
      if (mounted) {
        await PrivacyPolicyDialog.showPrivacyDialog(context);
        _loadPrivacyStatus(); // 重新加载状态
      }
    } catch (e) {
      QLog('重置隐私政策失败: $e');
      Get.snackbar(
        '错误',
        '重置失败，请重试',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取 MobTech 隐私协议内容
  Future<void> _getMobPrivacyContent() async {
    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // MobSDK 已移除，无法获取隐私协议内容
      // final content = await MobSDK.instance.getPrivateContent(1);
      final content = null; // MobSDK已移除
      
      // 关闭加载对话框
      Get.back();

      if (content != null && content.isNotEmpty) {
        // 显示内容对话框
        Get.dialog(
          AlertDialog(
            title: const Text('MobTech 隐私协议'),
            content: SingleChildScrollView(
              child: Text(content),
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      } else {
        Get.snackbar(
          '提示',
          '暂时无法获取隐私协议内容',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // 关闭加载对话框
      Get.back();
      
      QLog('获取隐私协议内容失败: $e');
      Get.snackbar(
        '错误',
        '获取失败，请重试',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
