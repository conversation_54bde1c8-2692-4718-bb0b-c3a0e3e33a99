import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'package:lima/res/colors.dart';
import 'bind_phone_logic.dart';

/// 绑定手机号页面
class BindPhonePage extends StatelessWidget {
  const BindPhonePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(BindPhoneLogic());

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login-bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: BaseScaffold(
        backgroundColor: Colors.transparent,
        appBarBackgroundColor: Colors.transparent,
        title: '绑定手机号',
        body: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  kToolbarHeight,
            ),
            child: IntrinsicHeight(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  children: [
                    SizedBox(height: 30.h),

                    // Logo
                    _buildLogo(),

                    SizedBox(height: 24.h),

                    // 标题和描述
                    _buildTitleSection(logic),

                    SizedBox(height: 40.h),

                    // 手机号输入框
                    _buildPhoneInput(logic),

                    SizedBox(height: 32.h),

                    // 获取验证码按钮
                    _buildGetCodeButton(logic),

                    // 弹性空间
                    const Spacer(),

                    SizedBox(height: 60.h),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      child: Image.asset(
        'assets/images/logo.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建标题和描述部分
  Widget _buildTitleSection(BindPhoneLogic logic) {
    return Obx(() => Column(
      children: [
        Text(
          '绑定手机号',
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 12.h),
        Text(
          '请绑定手机号以完成${logic.loginType.value}登录',
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF666666),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    ));
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput(BindPhoneLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // +86前缀
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '+86',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20.h,
            color: const Color(0xFFE5E5E5),
          ),
          // 手机号输入
          Expanded(
            child: TextField(
              controller: logic.phoneController,
              keyboardType: TextInputType.phone,
              maxLength: 11,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                hintText: '输入手机号',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建获取验证码按钮
  Widget _buildGetCodeButton(BindPhoneLogic logic) {
    return Obx(() {
      final isLoading = logic.countdown.value > 0;
      final buttonText = isLoading ? '${logic.countdown.value}s后重新发送' : '获取验证码';
      
      return SizedBox(
        width: double.infinity,
        height: 56.h,
        child: ElevatedButton(
          onPressed: logic.isPhoneValid.value && logic.canSendCode.value
              ? logic.sendVerificationCode
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4A4A4A),
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28.r),
            ),
            disabledBackgroundColor: Colors.grey[300],
            disabledForegroundColor: Colors.grey[600],
          ),
          child: Text(
            buttonText,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    });
  }


}