import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'dart:async';

import '../../global.dart';
import '../../utils/q_log.dart';
import '../../utils/http/api.dart';
import '../../services/third_party_login_service.dart';
import '../../routers/app_router.dart';
import '../../components/q_text.dart';

class BindPhoneLogic extends GetxController {
  // 手机号输入控制器
  final phoneController = TextEditingController();
  
  // 响应式变量
  final isPhoneValid = false.obs;
  final countdown = 0.obs;
  final canSendCode = true.obs;
  final bindKey = ''.obs;
  final loginType = ''.obs;
  final originalData = <String, dynamic>{}.obs;
  
  // 倒计时定时器
  Timer? _countdownTimer;
  
  // 设备clientId
  String clientId = '';
  
  @override
  void onInit() {
    super.onInit();
    _initializeData();
    _initializeClientId();
    
    // 监听手机号输入变化
    phoneController.addListener(_onPhoneChanged);
  }
  
  @override
  void onClose() {
    phoneController.dispose();
    _countdownTimer?.cancel();
    super.onClose();
  }
  
  /// 初始化数据
  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>? ?? {};
    
    bindKey.value = arguments['bindKey'] ?? '';
    loginType.value = arguments['loginType'] ?? '';
    originalData.value = arguments['originalData'] ?? {};
    
    QLog('绑定手机号页面初始化');
    QLog('bindKey: ${bindKey.value}');
    QLog('loginType: ${loginType.value}');
    QLog('originalData: ${originalData.value}');
  }
  
  /// 初始化设备clientId
  Future<void> _initializeClientId() async {
    try {
      // 使用GTID作为clientId
      clientId = await Global.getGTID() ?? 'unknown';
      QLog('获取到clientId: $clientId');
    } catch (e) {
      QLog('获取clientId失败: $e', mode: QLogMode.error);
      clientId = 'unknown';
    }
  }
  
  /// 手机号输入变化处理
  void _onPhoneChanged() {
    final phone = phoneController.text.trim();
    isPhoneValid.value = _isValidPhone(phone);
  }
  
  /// 验证手机号格式
  bool _isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    // 中国大陆手机号正则
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }
  
  /// 发送验证码
  Future<void> sendVerificationCode() async {
    if (!canSendCode.value || !isPhoneValid.value) return;
    
    await _doSendVerificationCode();
  }
  
  /// 执行发送验证码
  Future<void> _doSendVerificationCode() async {
    final phone = phoneController.text.trim();
    
    try {
      EasyLoading.show(status: '发送中...');
      
      // 调用获取验证码API
      final result = await API.getSmsCodeApi(
        mobile: phone,
      );
      
      EasyLoading.dismiss();
      
      if (result.code == 200 || result.state == 200) {
        QLog('验证码发送成功');
        EasyLoading.showSuccess('验证码已发送');
        
        // 开始倒计时
        _startCountdown();
        
        // 跳转到验证码页面
        final verificationResult = await Get.toNamed(AppRoute.verification, arguments: {
          'phone': phone,
          'bindKey': bindKey.value,
          'loginType': loginType.value,
          'originalData': originalData.value,
          'clientId': clientId,
          'isBinding': true, // 标识这是绑定流程
        });
        
        // 处理验证码页面返回结果
        if (verificationResult != null) {
          QLog('绑定手机号完成，返回结果: $verificationResult');
          Get.back(result: verificationResult);
        }
      } else {
        QLog('验证码发送失败: ${result.msg}', mode: QLogMode.error);
        EasyLoading.showError(result.msg ?? '发送失败');
      }
    } catch (e) {
      QLog('发送验证码异常: $e', mode: QLogMode.error);
      EasyLoading.showError('发送失败，请重试');
    }
  }
  
  /// 开始倒计时
  void _startCountdown() {
    countdown.value = 60;
    canSendCode.value = false;
    
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        canSendCode.value = true;
      }
    });
  }
  

  
  /// 微信登录（绑定模式下不允许）
  Future<void> wechatLogin() async {
    // 绑定模式下不允许第三方登录
    EasyLoading.showInfo('当前为绑定手机号模式，请输入手机号完成绑定');
  }
  
  /// Apple登录（绑定模式下不允许）
  Future<void> appleLogin() async {
    // 绑定模式下不允许第三方登录
    EasyLoading.showInfo('当前为绑定手机号模式，请输入手机号完成绑定');
  }
}