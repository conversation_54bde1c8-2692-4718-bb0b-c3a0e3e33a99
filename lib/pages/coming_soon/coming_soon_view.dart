import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/colors.dart';

class ComingSoonPage extends StatelessWidget {
  const ComingSoonPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取传递的参数
    final String title = Get.arguments?['title'] ?? '功能';
    final String subtitle = Get.arguments?['subtitle'] ?? '即将上线';
    final IconData icon = Get.arguments?['icon'] ?? Icons.construction;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: QText(
          title,
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20.sp,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: Container(
        width: double.infinity,
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 主图标
            Container(
              width: 120.w,
              height: 120.w,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60.w),
              ),
              child: Icon(
                icon,
                size: 60.sp,
                color: AppColors.primary,
              ),
            ),
            
            SizedBox(height: 40.h),
            
            // 标题
            QText(
              title,
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 16.h),
            
            // 副标题
            QText(
              subtitle,
              fontSize: 18.sp,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 12.h),
            
            // 描述文字
            QText(
              '我们正在努力开发中\n敬请期待更多精彩功能',
              fontSize: 16.sp,
              color: Colors.grey[500],
              textAlign: TextAlign.center,
              lineHeight: 1.5,
            ),
            
            SizedBox(height: 60.h),
            
            // // 装饰性元素
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     _buildDot(true),
            //     SizedBox(width: 8.w),
            //     _buildDot(false),
            //     SizedBox(width: 8.w),
            //     _buildDot(false),
            //   ],
            // ),
            
            SizedBox(height: 40.h),
            
            // 返回按钮
            Container(
              width: double.infinity,
              height: 50.h,
              child: ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.w),
                  ),
                ),
                child: QText(
                  '返回',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDot(bool isActive) {
    return Container(
      width: 8.w,
      height: 8.w,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : Colors.grey[300],
        borderRadius: BorderRadius.circular(4.w),
      ),
    );
  }
}
