import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/res/colors.dart';
import 'package:lima/components/curved_tab_bar.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/widgets/privacy_policy_dialog.dart';

import 'main_tab_logic.dart';
import '../community/new_community_view.dart';
import '../old/home/<USER>';
import '../service/service_view.dart';
import '../shop/home/<USER>';

class MainTabPage extends StatefulWidget {
  const MainTabPage({super.key});

  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage> {
  Timer? _uiTimeoutTimer;
  
  // 缓存所有页面
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();

    // 预创建所有页面，确保缓存
    _pages = [
      NewCommunityPage(tabIndex: 0), // 社区
      ShopHomePage(tabIndex: 1), // 商城
      HomePage(), // 车辆  
      ServicePage(), // 服务
      NewCommunityPage(tabIndex: 4), // 我的
    ];

    // 确保MainTabLogic实例存在
    if (!Get.isRegistered<MainTabLogic>()) {
      Get.put(MainTabLogic(), permanent: true);
      QLog('MainTabView: MainTabLogic已在initState中创建');
    } else {
      QLog('MainTabView: MainTabLogic已存在，跳过创建');
    }

    // 检查并显示隐私政策对话框
    _checkPrivacyPolicy();

    // 添加一个简单的3秒超时，直接在UI层面强制隐藏loading
    _uiTimeoutTimer = Timer(const Duration(seconds: 3), () {
      if (!mounted) return; // 检查组件是否还存在

      try {
        // 确保MainTabLogic实例存在，如果不存在则创建
        final logic = Get.put(MainTabLogic(), permanent: true);
        QLog('MainTabView: 3秒UI层面强制超时检查 - isFirstAppLoad: ${logic.isFirstAppLoad.value}, isFirstLoadComplete: ${logic.isFirstLoadComplete.value}');

        // 如果还在loading状态，强制完成
        if (!logic.isFirstLoadComplete.value) {
          QLog('MainTabView: 3秒UI层面强制超时，直接隐藏loading');

          // 直接设置完成状态，跳过动画
          logic.isFirstLoadComplete.value = true;
          logic.isFirstAppLoad.value = false;
          logic.isLoadingFadingOut.value = false;
        } else {
          QLog('MainTabView: loading已经完成，无需处理');
        }
      } catch (e) {
        QLog('MainTabView: 超时时无法获取MainTabLogic: $e');
      }
    });

    // logic已经在前面初始化了
  }

  @override
  void dispose() {
    _uiTimeoutTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 确保MainTabLogic实例存在，如果不存在则创建
    final logic = Get.put(MainTabLogic(), permanent: true);

    return Scaffold(
      body: Obx(() {
        // 如果是第一次加载且未完成，显示loading
        if (logic.isFirstAppLoad.value && !logic.isFirstLoadComplete.value) {
          return AnimatedOpacity(
            opacity: logic.isLoadingFadingOut.value ? 0.0 : 1.0,
            duration: const Duration(milliseconds: 500),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.white,
              child: Image.network(
                'https://h5-lima.newtest.senthink.com/_next/image?url=%2Fimages%2FlimaGif.gif&w=860&q=75',
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover, // 全屏覆盖
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.white,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.white,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
              ),
            ),
          );
        }

        // 使用IndexedStack来支持缓存，更简单可靠
        return IndexedStack(
          index: logic.currentIndex.value,
          children: _pages,
        );
      }),

      bottomNavigationBar: Obx(() {
        // 如果是第一次加载且未完成，隐藏底部导航栏
        if (logic.isFirstAppLoad.value && !logic.isFirstLoadComplete.value) {
          return const SizedBox.shrink();
        }

        return CurvedTabBar(
          selectedIndex: logic.currentIndex.value,
          onTap: (index) {
            QLog('MainTabView: 点击tab: $index，当前索引: ${logic.currentIndex.value}');
            logic.currentIndex.value = index;
            QLog('MainTabView: 索引已更新为: ${logic.currentIndex.value}');
          },
          selectedColor: AppColors.primary,
          unselectedColor: Colors.grey[600]!,
          items: const [
            TabBarItem(
              icon: 'assets/tab-bar/home.png',
              selectedIcon: 'assets/tab-bar/home_sel.png',
              label: '社区',
            ),
            TabBarItem(
              icon: 'assets/tab-bar/store.png',
              selectedIcon: 'assets/tab-bar/store.png',
              label: '商城',
            ),
            TabBarItem(
              icon: 'assets/tab-bar/vehicle.png',
              selectedIcon: 'assets/tab-bar/vehicle_sel.png',
              label: '车辆',
            ),
            TabBarItem(
              icon: 'assets/tab-bar/service.png',
              selectedIcon: 'assets/tab-bar/service_sel.png',
              label: '服务',
            ),
            TabBarItem(
              icon: 'assets/tab-bar/user.png',
              selectedIcon: 'assets/tab-bar/user_sel.png',
              label: '我的',
            ),
          ],
        );
      }),
    );
  }


  /// 检查并显示隐私政策对话框
  Future<void> _checkPrivacyPolicy() async {
    try {
      // 延迟一点时间，确保UI完全加载
      await Future.delayed(const Duration(milliseconds: 500));

      // 检查是否需要显示隐私政策对话框
      final shouldShow = await PrivacyPolicyDialog.shouldShowPrivacyDialog();

      if (shouldShow && mounted) {
        QLog('需要显示隐私政策对话框');
        await PrivacyPolicyDialog.showPrivacyDialog(context);
      } else {
        QLog('用户已同意隐私政策，跳过对话框');
      }
    } catch (e) {
      QLog('检查隐私政策失败: $e');
    }
  }
}
