import 'dart:async';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/pages/simple_webview/simple_webview_page.dart';

class MainTabLogic extends GetxController {
  var currentIndex = 0.obs;

  // 标记是否是第一次加载app
  var isFirstAppLoad = true.obs;

  // 标记第一次加载是否完成
  var isFirstLoadComplete = false.obs;

  // 控制loading退场动画
  var isLoadingFadingOut = false.obs;

  @override
  void onInit() {
    super.onInit();
    QLog('MainTabLogic: onInit 开始，启动强制超时机制');

    // 启动5秒强制超时，确保不会一直卡在loading页面
    Timer(const Duration(seconds: 5), () {
      if (isFirstAppLoad.value && !isFirstLoadComplete.value) {
        QLog('MainTabLogic: 5秒强制超时，隐藏loading页面');
        markFirstLoadComplete();
      }
    });

    // 检查是否有传递的selectedTab参数
    final arguments = Get.arguments;
    if (arguments != null &&
        arguments is Map &&
        arguments.containsKey('selectedTab')) {
      final selectedTab = arguments['selectedTab'] as int?;
      if (selectedTab != null && selectedTab >= 0 && selectedTab < 5) {
        currentIndex.value = selectedTab;
      }
    }
  }

  void changeTab(int index) {
    final previousIndex = currentIndex.value;
    QLog('MainTabLogic: 切换tab从 $previousIndex 到 $index');

    currentIndex.value = index;

    // 暂时禁用H5导航，让缓存机制处理页面切换
    QLog('MainTabLogic: 使用缓存机制，跳过H5导航调用');
  }

  /// 切换到指定tab
  void switchToTab(int index) {
    changeTab(index);
  }

  /// 手动触发H5导航（用于登录后等场景）
  void triggerH5Navigation(int tabIndex) {
    QLog('MainTabLogic: 手动触发H5导航，tabIndex: $tabIndex');
    _generateH5NavigationData(tabIndex, tabIndex);
  }

  /// 生成H5导航数据
  void _generateH5NavigationData(int index, int previousIndex) {
    QLog('MainTabLogic: 开始生成H5导航数据 - tab $index, 从tab $previousIndex 切换过来');

    String targetPage;
    Map<String, dynamic> navigationData;

    // 检查是否从原生页面（车辆/服务）切换到H5页面
    bool fromNativeToH5 = (previousIndex == 2 || previousIndex == 3) &&
        (index == 0 || index == 1 || index == 4);
    QLog('MainTabLogic: fromNativeToH5 = $fromNativeToH5');

    switch (index) {
      case 0: // 社区
        targetPage = '/pages/index/index';
        navigationData = {
          'action': 'navigate',
          'targetPage': targetPage,
          'tabIndex': index,
          'previousIndex': previousIndex,
          'fromNative': fromNativeToH5,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        QLog('MainTabLogic: 直接调用社区页面导航: $navigationData');
        SimpleWebViewPage.notifyAllH5Navigation(navigationData);
        break;

      case 1: // 商城
        targetPage = '/pages/shop/home/<USER>';
        navigationData = {
          'action': 'navigate',
          'targetPage': targetPage,
          'tabIndex': index,
          'previousIndex': previousIndex,
          'fromNative': fromNativeToH5,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        QLog('MainTabLogic: 直接调用商城页面导航: $navigationData');
        SimpleWebViewPage.notifyAllH5Navigation(navigationData);
        break;

      case 2: // 车辆
        targetPage = '/'; // 车辆页面使用根路径
        navigationData = {
          'action': 'navigate',
          'targetPage': targetPage,
          'tabIndex': index,
          'previousIndex': previousIndex,
          'fromNative': fromNativeToH5,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        QLog('MainTabLogic: 直接调用车辆页面导航: $navigationData');
        // 车辆页面使用HomeLogic的WebView，需要特殊处理
        try {
          final homeLogic = Get.find<HomeLogic>();
          homeLogic.handleNavigation(navigationData);
        } catch (e) {
          QLog('MainTabLogic: 无法找到HomeLogic实例: $e');
        }
        break;

      case 3: // 服务
        targetPage = '/service'; // 服务页面使用/service路径
        navigationData = {
          'action': 'navigate',
          'targetPage': targetPage,
          'tabIndex': index,
          'previousIndex': previousIndex,
          'fromNative': fromNativeToH5,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        break;

      case 4: // 我的
        // 如果是从原生页面切换过来，使用完整的页面路径
        if (fromNativeToH5) {
          targetPage = '/#/pages/user/user'; // 包含widget.pagePath的完整路径
          QLog('MainTabLogic: 从原生页面切换到我的页面，使用完整路径: $targetPage');
        } else {
          targetPage = '/pages/user/user'; // H5页面间切换，使用相对路径
          QLog('MainTabLogic: H5页面间切换到我的页面，使用相对路径: $targetPage');
        }

        navigationData = {
          'action': 'navigate',
          'targetPage': targetPage,
          'tabIndex': index,
          'previousIndex': previousIndex,
          'fromNative': fromNativeToH5,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        QLog('MainTabLogic: 直接调用我的页面导航: $navigationData');
        SimpleWebViewPage.notifyAllH5Navigation(navigationData);
        break;

      default:
        QLog('MainTabLogic: 未知的tab索引: $index');
        break;
    }
  }

  /// 标记第一次加载完成
  void markFirstLoadComplete() {
    QLog('MainTabLogic: 标记第一次加载完成，开始淡出动画');
    // QLog('MainTabLogic: 调用栈: ${StackTrace.current}');r

    // 先触发淡出动画
    isLoadingFadingOut.value = true;

    // 延迟500毫秒后完全隐藏loading
    Future.delayed(const Duration(milliseconds: 500), () {
      isFirstLoadComplete.value = true;
      isFirstAppLoad.value = false;
      isLoadingFadingOut.value = false;
      QLog('MainTabLogic: 淡出动画完成，loading已隐藏');
    });
  }
}
