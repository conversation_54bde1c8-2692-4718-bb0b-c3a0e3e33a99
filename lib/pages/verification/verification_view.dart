import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'verification_logic.dart';

/// 验证码接收页面
class VerificationPage extends StatelessWidget {
  const VerificationPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(VerificationLogic());

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login-bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: BaseScaffold(
        backgroundColor: Colors.transparent,
        appBarBackgroundColor: Colors.transparent,
        // title: '密码登录',
        // actions: [
        //   GestureDetector(
        //     onTap: () {
        //       // 跳转到密码登录页面
        //       Get.toNamed('/password_login');
        //     },
        //     child: Container(
        //       padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        //       margin: EdgeInsets.only(right: 16.w),
        //       child: Text(
        //         '密码登录',
        //         style: TextStyle(
        //           fontSize: 16.sp,
        //           color: const Color(0xFF333333),
        //           fontWeight: FontWeight.w500,
        //         ),
        //       ),
        //     ),
        //   ),
        // ],
        body: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 
                         MediaQuery.of(context).padding.top - 
                         kToolbarHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // 主要内容区域
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),

                          // Logo
                          _buildLogo(),

                          SizedBox(height: 24.h),

                          // 标题
                          _buildTitle(logic),

                          SizedBox(height: 16.h),

                          // 描述文字
                          _buildDescription(logic),

                          SizedBox(height: 32.h),

                          // 验证码输入框
                          _buildCodeInput(logic),

                          SizedBox(height: 20.h),

                          // 重新发送按钮
                          _buildResendButton(logic),

                          // 弹性空间，将第三方登录推到底部
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),

                  // 底部第三方登录 - 只有在没有bindKey时才显示
                  Obx(() {
                    if (logic.bindKey.value.isEmpty) {
                      return Container(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: Column(
                          children: [
                            SizedBox(height: 20.h),
                            _buildThirdPartyLogin(logic),
                            SizedBox(height: 60.h),
                          ],
                        ),
                      );
                    } else {
                      // 有bindKey时，只显示底部间距
                      return SizedBox(height: 40.h);
                    }
                  }),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return SizedBox(
      width: 120.w,
      height: 120.w,
      child: Image.asset(
        'assets/images/logo.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle(VerificationLogic logic) {
    return Obx(() => Text(
      logic.getMainTitle(),
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
      ),
    ));
  }

  /// 构建描述文字
  Widget _buildDescription(VerificationLogic logic) {
    return Obx(() => Text(
      logic.getPageDescription(),
      style: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xFF666666),
      ),
      textAlign: TextAlign.center,
    ));
  }

  /// 构建验证码输入框
  Widget _buildCodeInput(VerificationLogic logic) {
    return Column(
      children: [
        // 显示的6个输入格子
        GestureDetector(
          onTap: () {
            logic.codeFocusNode.requestFocus();
          },
          child: Obx(() {
            final code = logic.inputCode.value;
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(6, (index) {
                return Container(
                  width: 45.w,
                  height: 56.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: const Color(0xFFE5E5E5),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      index < code.length ? code[index] : '',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                );
              }),
            );
          }),
        ),
        // 隐藏的输入框
        Opacity(
          opacity: 0.0,
          child: SizedBox(
            height: 0,
            child: TextField(
              controller: logic.codeController,
              focusNode: logic.codeFocusNode,
              keyboardType: TextInputType.number,
              maxLength: 6,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: '',
              ),
              onChanged: (value) {
                // 自动验证当输入6位数字时
                if (value.length == 6) {
                  logic.verifyCode();
                }
              },
            ),
          ),
        ),
      ],
    );
  }



  /// 构建重新发送按钮
  Widget _buildResendButton(VerificationLogic logic) {
    return Center(
      child: Obx(() => GestureDetector(
        onTap: logic.isCountdownActive.value ? null : logic.resendCode,
        child: Text(
          logic.countdownText,
          style: TextStyle(
            fontSize: 14.sp,
            color: logic.isCountdownActive.value
                ? const Color(0xFF999999)
                : const Color(0xFF666666),
            fontWeight: FontWeight.normal,
          ),
        ),
      )),
    );
  }

  /// 构建第三方登录
  Widget _buildThirdPartyLogin(VerificationLogic logic) {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            Expanded(child: Divider(color: Colors.grey[400])),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                '第三方平台登录',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[700],
                ),
              ),
            ),
            Expanded(child: Divider(color: Colors.grey[400])),
          ],
        ),

        SizedBox(height: 24.h),

        // 第三方登录图标
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 微信登录
            GestureDetector(
              onTap: () {
                // TODO: 实现微信登录
                print('微信登录');
              },
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF07C160),
                  borderRadius: BorderRadius.circular(25.r),
                ),
                child: Icon(
                  Icons.wechat,
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
            ),

            SizedBox(width: 40.w),

            // Apple ID登录
            GestureDetector(
              onTap: () {
                // TODO: 实现Apple登录
                print('Apple登录');
              },
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(25.r),
                ),
                child: Icon(
                  Icons.apple,
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
