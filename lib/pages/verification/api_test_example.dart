import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/q_log.dart';

/// 验证码API测试示例
/// 展示如何使用新的验证码相关API
class VerificationApiTestExample {
  
  /// 测试获取验证码API
  static Future<void> testGetSmsCode() async {
    try {
      QLog('=== 测试获取验证码API ===');
      
      final result = await API.getSmsCodeApi(
        mobile: '13800138000', // 测试手机号
      );
      
      QLog('获取验证码结果: ${result.toJson()}');
      
      if (result.state == 200) {
        QLog('✅ 获取验证码成功');
      } else {
        QLog('❌ 获取验证码失败: ${result.msg}');
      }
    } catch (e) {
      QLog('❌ 获取验证码异常: $e');
    }
  }
  
  /// 测试绑定手机号API
  static Future<void> testBindMobile() async {
    try {
      QLog('=== 测试绑定手机号API ===');
      
      final result = await API.bindMobileApi(
        bindKey: 'test-bind-key-123456', // 测试bindKey
        mobile: '13800138000', // 测试手机号
        smsCode: '123456', // 测试验证码
        appType: '2', // iOS
      );
      
      QLog('绑定手机号结果: ${result.toJson()}');
      
      if (result.state == 200) {
        QLog('✅ 绑定手机号成功');
      } else {
        QLog('❌ 绑定手机号失败: ${result.msg}');
      }
    } catch (e) {
      QLog('❌ 绑定手机号异常: $e');
    }
  }
  
  /// 完整的验证码流程测试
  static Future<void> testCompleteFlow() async {
    try {
      QLog('=== 测试完整验证码流程 ===');
      
      const mobile = '13800138000';
      const bindKey = 'test-bind-key-123456';
      
      // 1. 获取验证码
      QLog('步骤1: 获取验证码');
      final smsResult = await API.getSmsCodeApi(mobile: mobile);
      
      if (smsResult.state != 200) {
        QLog('❌ 获取验证码失败，流程终止');
        return;
      }
      
      QLog('✅ 验证码发送成功');
      
      // 2. 模拟用户输入验证码（实际应用中由用户输入）
      const userInputCode = '123456'; // 模拟用户输入的验证码
      
      // 3. 绑定手机号
      QLog('步骤2: 绑定手机号');
      final bindResult = await API.bindMobileApi(
        bindKey: bindKey,
        mobile: mobile,
        smsCode: userInputCode,
        appType: '2', // iOS
      );
      
      if (bindResult.state == 200) {
        QLog('✅ 完整流程测试成功');
        QLog('绑定结果数据: ${bindResult.data}');
      } else {
        QLog('❌ 绑定手机号失败: ${bindResult.msg}');
      }
      
    } catch (e) {
      QLog('❌ 完整流程测试异常: $e');
    }
  }
  
  /// 测试不同的错误场景
  static Future<void> testErrorScenarios() async {
    QLog('=== 测试错误场景 ===');
    
    // 测试无效手机号
    try {
      QLog('测试场景1: 无效手机号');
      await API.getSmsCodeApi(mobile: '123'); // 无效手机号
    } catch (e) {
      QLog('无效手机号测试结果: $e');
    }
    
    // 测试无效bindKey
    try {
      QLog('测试场景2: 无效bindKey');
      await API.bindMobileApi(
        bindKey: 'invalid-bind-key',
        mobile: '13800138000',
        smsCode: '123456',
      );
    } catch (e) {
      QLog('无效bindKey测试结果: $e');
    }
    
    // 测试无效验证码
    try {
      QLog('测试场景3: 无效验证码');
      await API.bindMobileApi(
        bindKey: 'test-bind-key-123456',
        mobile: '13800138000',
        smsCode: '000000', // 无效验证码
      );
    } catch (e) {
      QLog('无效验证码测试结果: $e');
    }
  }
}

/// 使用示例：
/// 
/// ```dart
/// // 在需要测试的地方调用
/// await VerificationApiTestExample.testGetSmsCode();
/// await VerificationApiTestExample.testBindMobile();
/// await VerificationApiTestExample.testCompleteFlow();
/// await VerificationApiTestExample.testErrorScenarios();
/// ```
