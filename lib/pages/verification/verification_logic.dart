import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/http/api.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:lima/pages/login/login_logic.dart';

import 'package:lima/routers/app_router.dart';

/// 验证码页面控制器
class VerificationLogic extends GetxController {
  final TextEditingController codeController = TextEditingController();
  final FocusNode codeFocusNode = FocusNode();
  
  // 响应式变量
  final RxInt countdown = 60.obs;
  final RxBool isCountdownActive = false.obs;
  final RxString bindKey = ''.obs;
  final RxString phoneNumber = ''.obs;
  final RxBool isCodeValid = false.obs; // 验证码有效性状态
  final RxString inputCode = ''.obs; // 响应式验证码输入

  
  Timer? _countdownTimer;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
    // 初始化时自动发送验证码
    _sendInitialCode();
    // 监听验证码输入
    codeController.addListener(_validateCode);
  }

  /// 验证验证码有效性
  void _validateCode() {
    final code = codeController.text.trim();
    inputCode.value = code; // 更新响应式变量
    isCodeValid.value = code.length == 6;
  }

  @override
  void onClose() {
    codeController.dispose();
    codeFocusNode.dispose();
    _countdownTimer?.cancel();
    super.onClose();
  }

  /// 初始化页面数据
  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      bindKey.value = arguments['bindKey'] ?? '';
      phoneNumber.value = arguments['phoneNumber'] ?? '';
      QLog('验证码页面初始化 - bindKey: ${bindKey.value}, phoneNumber: ${phoneNumber.value}');
    }
  }

  /// 初始化时自动发送验证码
  Future<void> _sendInitialCode() async {
    // 如果有手机号，自动发送验证码
    if (phoneNumber.value.isNotEmpty) {
      QLog('自动发送验证码到: ${phoneNumber.value}');
      await _sendVerificationCode();
    } else {
      QLog('没有手机号，跳过自动发送验证码');
      // 如果没有手机号，仍然启动倒计时（可能是其他方式获取的验证码）
      _startCountdown();
    }
  }

  /// 发送验证码的通用方法
  Future<void> _sendVerificationCode() async {
    try {
      EasyLoading.show(status: '发送中...');

      // 调用获取验证码的API
      final result = await API.getSmsCodeApi(
        mobile: phoneNumber.value, // 手机号
      );

      QLog('获取验证码API响应: ${result.toJson()}');

      if (result.state == 200) {
        EasyLoading.showSuccess('验证码已发送');
        _startCountdown();
      } else {
        final errorMsg = result.msg ?? '发送失败';
        EasyLoading.showError(errorMsg);
        QLog('获取验证码失败: state=${result.state}, msg=$errorMsg');
      }
    } catch (e) {
      QLog('获取验证码异常: $e');
      EasyLoading.showError('发送失败，请稍后重试');
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    if (isCountdownActive.value) return;
    
    isCountdownActive.value = true;
    countdown.value = 60;

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        isCountdownActive.value = false;
        timer.cancel();
      }
    });
  }

  /// 重新发送验证码
  Future<void> resendCode() async {
    if (isCountdownActive.value) return;

    QLog('用户点击重新发送验证码');
    await _sendVerificationCode();
  }

  /// 验证验证码
  Future<void> verifyCode() async {
    final code = codeController.text.trim();
    if (code.isEmpty) {
      EasyLoading.showError('请输入验证码');
      return;
    }

    if (code.length != 6) {
      EasyLoading.showError('请输入6位验证码');
      return;
    }

    try {
      EasyLoading.show(status: '验证中...');

      // 根据是否有bindKey决定调用哪个API
      if (bindKey.value.isNotEmpty) {
        QLog('检测到bindKey，调用授权登录绑定手机号API');
        await _verifyWithBindKey(code);
      } else {
        QLog('没有bindKey，调用手机号+验证码免密登录API');
        await _verifyWithoutBindKey(code);
      }
    } catch (e) {
      QLog('验证码验证异常: $e');
      EasyLoading.showError('验证失败，请检查验证码');
    }
  }

  /// 有bindKey时的验证逻辑（授权登录绑定手机号）
  Future<void> _verifyWithBindKey(String code) async {
    final result = await API.bindMobileApi(
      bindKey: bindKey.value,
      mobile: phoneNumber.value,
      smsCode: code,
      appType: _getAppType(),
    );

    QLog('绑定手机号API响应: ${result.toJson()}');

    if (result.state == 200) {
      EasyLoading.showSuccess('验证成功');
      _handleVerificationSuccess(result.data);
    } else {
      final errorMsg = result.msg ?? '验证失败';
      EasyLoading.showError(errorMsg);
      QLog('绑定手机号失败: state=${result.state}, msg=$errorMsg');
    }
  }

  /// 没有bindKey时的验证逻辑（手机号+验证码免密登录）
  Future<void> _verifyWithoutBindKey(String code) async {
    final result = await API.freeSecretLoginApi(
      mobile: phoneNumber.value,
      smsCode: code,
      appType: _getAppType(),
      clientId: await _getClientId(), // 获取clientId
    );

    QLog('手机号+验证码免密登录API响应: ${result.toJson()}');

    if (result.code == '200' || result.code == 200) {
      _handleDirectLoginSuccess(result.data);
    } else {
      final errorMsg = result.msg ?? '登录失败';
      EasyLoading.showError(errorMsg);
      QLog('手机号+验证码登录失败: code=${result.code}, msg=$errorMsg');
    }
  }

  /// 获取clientId
  Future<String?> _getClientId() async {
    try {
      // 从路由参数中获取clientId
      final arguments = Get.arguments as Map<String, dynamic>?;
      if (arguments != null && arguments['clientId'] != null) {
        return arguments['clientId'];
      }

      // 如果没有传递clientId，可以从其他地方获取
      // 这里可以根据实际情况调整
      return null;
    } catch (e) {
      QLog('获取clientId异常: $e');
      return null;
    }
  }

  /// 获取应用类型
  /// 1-android，2-ios
  String _getAppType() {
    try {
      if (Platform.isIOS) {
        return '2'; // iOS
      } else if (Platform.isAndroid) {
        return '1'; // Android
      } else {
        QLog('未知平台，默认返回iOS类型');
        return '2'; // 默认iOS
      }
    } catch (e) {
      QLog('获取平台类型异常: $e，默认返回iOS类型');
      return '2'; // 异常时默认iOS
    }
  }

  /// 处理验证成功（有bindKey的情况）
  void _handleVerificationSuccess(Map<String, dynamic>? data) async {
    QLog('验证码验证成功，准备跳转...');

    try {
      // 保存用户信息和access_token
      if (data != null) {
        await _saveUserInfo(data);
        QLog('用户信息保存成功');
      }

      // 检查是否是绑定流程
      final arguments = Get.arguments as Map<String, dynamic>?;
      final isBinding = arguments?['isBinding'] == true;
      
      if (isBinding) {
        // 绑定流程：返回成功结果给绑定手机号页面
        QLog('绑定手机号成功，返回结果给绑定页面');
        Get.back(result: {
          'success': true,
          'message': '绑定成功',
          'userData': data
        });
      } else {
        // 普通验证流程：直接跳转到主页面
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
      }
    } catch (e) {
      QLog('保存用户信息失败: $e');
      
      // 检查是否是绑定流程
      final arguments = Get.arguments as Map<String, dynamic>?;
      final isBinding = arguments?['isBinding'] == true;
      
      if (isBinding) {
        // 绑定流程：返回失败结果
        Get.back(result: {
          'success': false,
          'message': '保存用户信息失败: $e'
        });
      } else {
        // 即使保存失败，也跳转到主Tab页面
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
      }
    }
  }

  /// 处理直接登录成功（没有bindKey的情况）
  Future<void> _handleDirectLoginSuccess(Map<String, dynamic>? data) async {
    QLog('手机号+验证码登录成功，准备保存用户信息...');

    try {
      // 保存用户信息和access_token
      if (data != null) {
        await _saveUserInfo(data);
        QLog('用户信息保存成功');
      }

      // 检查是否是从WebView调用的登录
      if (Get.currentRoute.contains('login') && Get.previousRoute.contains('simple_webview')) {
        QLog('检测到从WebView调用的登录，返回成功结果');
        // 返回登录成功结果给WebView
        Get.back(result: {
          'success': true,
          'message': '登录成功',
          'userData': data
        });
      } else {
        // 正常的登录流程，跳转到主Tab页面
        QLog('正常登录流程，跳转到主Tab页面');
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
      }
    } catch (e) {
      QLog('保存用户信息失败: $e');

      // 检查是否是从WebView调用的登录
      if (Get.currentRoute.contains('login') && Get.previousRoute.contains('simple_webview')) {
        QLog('WebView登录流程，返回失败结果');
        Get.back(result: {
          'success': false,
          'message': '保存用户信息失败: $e'
        });
      } else {
        // 即使保存失败，也跳转到主Tab页面
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
      }
    }
  }

  /// 保存用户信息
  Future<void> _saveUserInfo(Map<String, dynamic> userData) async {
    try {
      // 使用LoginLogic中的saveUserInfo方法
      final loginLogic = LoginLogic();
      await loginLogic.saveUserInfo(userData);
      QLog('✅ 使用LoginLogic保存用户信息成功');
    } catch (e) {
      QLog('保存用户信息异常: $e');
      rethrow;
    }
  }

  /// 格式化手机号显示
  String get maskedPhoneNumber {
    if (phoneNumber.value.isEmpty) return '';
    if (phoneNumber.value.length < 7) return phoneNumber.value;
    
    final phone = phoneNumber.value;
    return '${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}';
  }

  /// 获取倒计时显示文本
  String get countdownText {
    if (isCountdownActive.value) {
      return '${countdown.value}s后重新发送';
    } else {
      return '重新发送';
    }
  }

  /// 动态获取页面标题
  String getPageTitle() {
    if (bindKey.value.isNotEmpty) {
      return '验证码验证'; // 有bindKey时显示验证码验证
    } else {
      return '手机号验证'; // 没有bindKey时显示手机号验证
    }
  }

  /// 动态获取页面主标题
  String getMainTitle() {
    if (bindKey.value.isNotEmpty) {
      return '请输入验证码'; // 有bindKey时显示请输入验证码
    } else {
      return '验证手机号'; // 没有bindKey时显示验证手机号
    }
  }

  /// 动态获取页面描述
  String getPageDescription() {
    if (phoneNumber.value.isNotEmpty) {
      if (bindKey.value.isNotEmpty) {
        return '验证码已发送至 $maskedPhoneNumber\n请输入验证码完成手机号绑定';
      } else {
        return '验证码已发送至 $maskedPhoneNumber\n请输入验证码完成登录';
      }
    } else {
      if (bindKey.value.isNotEmpty) {
        return '请输入收到的6位验证码完成手机号绑定';
      } else {
        return '请输入收到的6位验证码完成登录';
      }
    }
  }

  /// 动态获取确认按钮文案
  String getConfirmButtonText() {
    if (bindKey.value.isNotEmpty) {
      return '确认绑定'; // 有bindKey时显示确认绑定
    } else {
      return '确认登录'; // 没有bindKey时显示确认登录
    }
  }

  /// 动态获取底部提示文案
  String getBottomTipText() {
    if (bindKey.value.isNotEmpty) {
      return '验证码有效期为5分钟，输入验证码后将完成手机号绑定和登录';
    } else {
      return '验证码有效期为5分钟，输入验证码后将完成登录';
    }
  }
}
