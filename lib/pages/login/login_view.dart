
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/colors.dart';
import 'package:lima/res/icons.dart';

import 'login_logic.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(LoginLogic());

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        // leading: IconButton(
        //   icon: Icon(Icons.arrow_back_ios, color: Colors.black87),
        //   onPressed: () => Get.back(),
        // ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 40.h),
              
              // Logo和标题
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 80.w,
                      height: 80.w,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Icon(
                        Icons.electric_scooter,
                        color: Colors.white,
                        size: 40.sp,
                      ),
                    ),
                    SizedBox(height: 24.h),
                    QText(
                      '欢迎使用立马科技',
                      fontSize: 28.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    SizedBox(height: 8.h),
                    QText(
                      '请登录您的账户',
                      fontSize: 16.sp,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 24.h),

              SizedBox(height: 24.h),

              // 运营商一键登录按钮
              Container(
                width: double.infinity,
                height: 50.h,
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: ElevatedButton.icon(
                  onPressed: logic.carrierLogin,
                  icon: Icon(
                    Icons.phone_android,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                  label: QText(
                    '本机号码一键登录',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF4CAF50), // 绿色，表示快捷
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shadowColor: Color(0xFF4CAF50).withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 32.h),

              // 手机号验证码登录按钮
              Container(
                width: double.infinity,
                height: 50.h,
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: ElevatedButton.icon(
                  onPressed: logic.testPhoneVerificationLogin,
                  icon: Icon(
                    Icons.phone_android,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                  label: QText(
                    '手机号验证码登录',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary, // 蓝色，表示常规登录
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shadowColor: AppColors.primary.withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 32.h),
              // 分割线
              Row(
                children: [
                  Expanded(child: Divider(color: Colors.grey[300])),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: QText(
                      '其他登录方式',
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Expanded(child: Divider(color: Colors.grey[300])),
                ],
              ),

              SizedBox(height: 32.h),

              // 密码登录按钮
              Container(
                width: double.infinity,
                height: 50.h,
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: ElevatedButton.icon(
                  onPressed: logic.passwordLogin,
                  icon: Icon(
                    Icons.lock_outline,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                  label: QText(
                    '手机号密码登录',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF2196F3), // 蓝色，表示密码登录
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shadowColor: Color(0xFF2196F3).withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16.h),

              // 忘记密码链接
              Center(
                child: GestureDetector(
                  onTap: logic.forgotPassword,
                  child: Text(
                    '忘记密码？',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.primary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 32.h),
              // 分割线
              Row(
                children: [
                  Expanded(child: Divider(color: Colors.grey[300])),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: QText(
                      '其他登录方式',
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Expanded(child: Divider(color: Colors.grey[300])),
                ],
              ),

              SizedBox(height: 32.h),

              // 第三方登录
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 微信登录
                  _buildSocialLoginButton(
                    icon: AppIcons.wechat,
                    label: '微信',
                    color: Color(0xFF07C160),
                    onTap: logic.wechatLogin,
                  ),

                  // 苹果登录 (仅iOS显示)
                  if (GetPlatform.isIOS)
                    _buildSocialLoginButton(
                      icon: Icons.apple,
                      label: '苹果',
                      color: Colors.black,
                      onTap: logic.appleLogin,
                    ),
                ],
              ),

              SizedBox(height: 20.h),



              SizedBox(height: 60.h),

              // 用户协议确认
              Obx(() => Row(
                children: [
                  Checkbox(
                    value: logic.isAgreementAccepted.value,
                    onChanged: (value) {
                      logic.toggleAgreement();
                    },
                    activeColor: AppColors.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                        children: [
                          TextSpan(text: '我已阅读并同意'),
                          WidgetSpan(
                            child: GestureDetector(
                              onTap: logic.showUserAgreement,
                              child: Text(
                                '《用户协议》',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.primary,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                          TextSpan(text: '和'),
                          WidgetSpan(
                            child: GestureDetector(
                              onTap: logic.showPrivacyPolicy,
                              child: Text(
                                '《隐私政策》',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.primary,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )),
              
              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSocialLoginButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80.w,
        height: 80.w,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 28.sp),
            SizedBox(height: 8.h),
            QText(
              label,
              fontSize: 12.sp,
              color: Colors.grey[700],
            ),
          ],
        ),
      ),
    );
  }
}
