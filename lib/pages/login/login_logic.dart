import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/utils/http/api.dart';

import 'package:lima/utils/q_log.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lima/services/third_party_login_service.dart';
import 'package:lima/services/carrier_login_service.dart';
import 'package:ali_auth/ali_auth.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/global.dart';
import 'package:lima/pages/main_tab/main_tab_logic.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/pages/service/service_logic.dart';


class LoginLogic extends GetxController {
  // 控制器
  final phoneController = TextEditingController();
  final codeController = TextEditingController();

  // 响应式变量
  final isLoading = false.obs;
  final canLogin = false.obs;
  final canSendCode = false.obs;
  final countdown = 0.obs;
  final isAgreementAccepted = false.obs; // 用户协议同意状态


  // 设备clientId
  String clientId = '';

  // 定时器
  Timer? _countdownTimer;
  
  @override
  void onInit() {
    super.onInit();
    _initializeClientId();
    validateForm();
  }
  
  @override
  void onClose() {
    phoneController.dispose();
    codeController.dispose();
    _countdownTimer?.cancel();
    super.onClose();
  }
  
  /// 获取设备clientId
  String getClientId() => clientId;

  /// 初始化设备clientId
  Future<void> _initializeClientId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        clientId = androidInfo.id; // Android设备ID
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        clientId = iosInfo.identifierForVendor ?? ''; // iOS设备ID
      }

      QLog('设备clientId: $clientId');
    } catch (e) {
      QLog('获取设备clientId失败: $e');
      // 如果获取失败，生成一个基于时间戳的ID作为备用
      clientId = 'client_${DateTime.now().millisecondsSinceEpoch}';
    }
  }





  /// 验证表单
  void validateForm() {
    final phone = phoneController.text.trim();
    final code = codeController.text.trim();

    // 验证手机号格式
    final phoneValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);

    // 验证验证码
    final codeValid = code.length == 6;

    // 更新发送验证码按钮状态
    canSendCode.value = phoneValid && countdown.value == 0;

    // 更新登录按钮状态（需要同意用户协议）
    canLogin.value = phoneValid && codeValid && !isLoading.value && isAgreementAccepted.value;
  }
  
  /// 发送验证码
  Future<void> sendVerificationCode() async {
    if (!canSendCode.value) return;
    
    final phone = phoneController.text.trim();
    
    try {
      EasyLoading.show(status: '发送中...');
      
      // 调用获取验证码API
      final result = await API.getSmsCodeApi(
        mobile: phone,
      );
      
      if (result.code == '200' || result.code == 200) {
        EasyLoading.showSuccess('验证码已发送');
        startCountdown();
      } else {
        EasyLoading.showError(result.msg ?? '发送失败');
      }
    } catch (e) {
      QLog('发送验证码失败: $e');
      EasyLoading.showError('发送失败，请重试');
    }
  }
  
  /// 开始倒计时
  void startCountdown() {
    countdown.value = 60;
    canSendCode.value = false;
    
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        validateForm();
      }
    });
  }
  
  /// 手机号登录
  Future<void> login() async {
    if (!canLogin.value) return;

    final phone = phoneController.text.trim();
    final code = codeController.text.trim();

    try {
      isLoading.value = true;
      validateForm();

      EasyLoading.show(status: '登录中...');

      // 确定appType
      String appType = '1'; // 默认Android
      if (GetPlatform.isIOS) {
        appType = '2'; // iOS
      }

      QLog('手机号登录 - 手机号: $phone, 验证码: $code, appType: $appType, clientId: $clientId');

      // 调用新的手机号+验证码免密登录API
      final result = await API.freeSecretLoginApi(
        mobile: phone,
        smsCode: code,
        appType: appType,
        clientId: clientId,
      );

      QLog('登录API响应: ${result.toJson()}');
      QLog('登录API响应data详情: ${result.data}');
      QLog('登录API响应data类型: ${result.data.runtimeType}');
      if (result.data is Map) {
        QLog('登录API响应data所有字段: ${(result.data as Map).keys.toList()}');
      }

      if (result.code == '200' || result.code == 200) {
        QLog('手机号登录成功');

        // 登录成功，保存用户信息（会自动显示登录成功提示）
        await saveUserInfo(result.data, showSuccessToast: false); // 不显示toast，避免重复

        // 检查是否是从WebView调用的登录
        final currentRoute = Get.currentRoute;
        final previousRoute = Get.previousRoute;
        QLog('当前路由: $currentRoute, 上一个路由: $previousRoute');

        if (previousRoute.contains('simple_webview') ||
            Get.arguments != null && Get.arguments['fromWebView'] == true) {
          QLog('检测到从WebView调用的登录，返回成功结果');
          EasyLoading.showSuccess('登录成功');
          // 返回登录成功结果给WebView，传入token
          Get.back(result: {
            'success': true,
            'message': '登录成功',
            'token': result.data['access_token'] ?? result.data['token'],
            'userData': result.data,
          });
        } else {
          QLog('正常登录流程，跳转到主Tab页面');
          EasyLoading.showSuccess('登录成功');
          // 正常的登录流程，跳转到主Tab页面并刷新WebView token
          _navigateToMainTab();
        }
      } else {
        QLog('手机号登录失败: ${result.msg}');
        EasyLoading.showError(result.msg ?? '登录失败');
      }
    } catch (e) {
      QLog('手机号登录异常: $e');
      EasyLoading.showError('登录失败，请重试');
    } finally {
      isLoading.value = false;
      validateForm();
    }
  }
  
  /// 微信登录
  Future<void> wechatLogin() async {
    try {
      EasyLoading.show(status: '微信登录中...');

      // 使用统一的第三方登录服务
      final result = await ThirdPartyLoginService.instance.wechatLogin();

      if (result.success) {
        QLog('微信登录成功，检查是否需要绑定手机号');

        // 检查返回数据中是否包含bindKey，如果有则需要进行手机号绑定
        final userData = result.userData;
        if (userData != null && userData['bindKey'] != null) {
          QLog('检测到bindKey，需要进行手机号绑定: ${userData['bindKey']}');
          EasyLoading.dismiss();
          await _handlePhoneBinding(userData, loginType: 'wechat');
        } else {
          QLog('微信登录完成，无需手机号绑定');
          await saveUserInfo(userData);
          _navigateToMainTab();
        }
      } else {
        if (result.errorCode == 'WECHAT_CANCEL') {
          EasyLoading.dismiss();
        } else {
          EasyLoading.showError(result.errorMessage ?? '微信登录失败');
        }
      }
    } catch (e) {
      QLog('微信登录失败: $e');
      EasyLoading.showError('微信登录失败');
    }
  }

  /// 处理需要手机号绑定的情况
  Future<void> _handlePhoneBinding(Map<String, dynamic> userData, {String loginType = 'wechat'}) async {
    try {
      QLog('需要绑定手机号，bindKey: ${userData['bindKey']}, loginType: $loginType');

      // 验证必要参数
      if (userData['bindKey'] == null) {
        QLog('错误：bindKey为null，无法进行手机号绑定');
        EasyLoading.showError('登录数据异常，请重试');
        return;
      }

      QLog('准备跳转到手机号输入页面...');
      QLog('路由名称: ${AppRoute.phoneInput}');
      QLog('传递参数: bindKey=${userData['bindKey']}, loginType=$loginType');

      // 跳转到手机号输入页面
      // 注意：由于验证码页面会直接跳转到Demo页面，这里不需要等待返回结果
      QLog('启动手机号绑定流程，跳转到手机号输入页面');

      final bindResult = await Get.toNamed(AppRoute.bindPhone, arguments: {
        'bindKey': userData['bindKey'],
        'loginType': loginType,
        'originalData': userData,
      });
      
      // 处理绑定结果
      if (bindResult != null && bindResult['success'] == true) {
        QLog('绑定手机号成功，跳转到主页面');
        _navigateToMainTab();
      }

      QLog('手机号绑定流程已启动，验证码页面将直接处理后续跳转');
      // 不再等待返回结果，因为验证码页面会直接跳转到Demo页面
    } catch (e, stackTrace) {
      QLog('处理手机号绑定异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('手机号绑定异常: $e');
    }
  }

  /// 苹果登录
  Future<void> appleLogin() async {
    if (!GetPlatform.isIOS) return;

    try {
      EasyLoading.show(status: '苹果登录中...');

      // 使用统一的第三方登录服务
      final result = await ThirdPartyLoginService.instance.appleLogin();

      if (result.success) {
        QLog('Apple登录成功，检查是否需要绑定手机号');

        // 检查返回数据中是否包含bindKey，如果有则需要进行手机号绑定
        final userData = result.userData;
        if (userData != null && userData['bindKey'] != null) {
          QLog('检测到bindKey，需要进行手机号绑定: ${userData['bindKey']}');
          EasyLoading.dismiss();
          await _handlePhoneBinding(userData, loginType: 'apple');
        } else {
          QLog('Apple登录完成，无需手机号绑定');
          await saveUserInfo(userData);
          _navigateToMainTab();
        }
      } else {
        if (result.errorCode == 'APPLE_NOT_SUPPORTED') {
          EasyLoading.showError('Apple登录仅支持iOS');
        } else {
          EasyLoading.showError(result.errorMessage ?? 'Apple登录失败');
        }
      }
    } catch (e) {
      QLog('苹果登录失败: $e');
      EasyLoading.showError('苹果登录失败');
    }
  }

  /// 测试微信登录bindKey检测功能
  Future<void> testWechatLoginWithBindKey() async {
    try {
      QLog('=== 测试微信登录bindKey检测功能 ===');

      // 模拟微信登录API返回包含bindKey的响应
      final mockUserData = {
        'bindKey': 'a4291165-0141-449f-85ff-534ee25f9271',
        'redirect': false,
        'userInfo': {
          'openid': 'mock_openid_123',
          'nickname': '测试用户',
          'avatar': 'https://example.com/avatar.jpg',
        },
      };

      QLog('模拟微信登录成功，检测到bindKey: ${mockUserData['bindKey']}');
      QLog('即将跳转到手机号输入页面');

      // 直接调用手机号绑定处理方法
      await _handlePhoneBinding(mockUserData, loginType: 'wechat');

    } catch (e, stackTrace) {
      QLog('测试微信登录bindKey检测功能异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('测试失败: $e');
    }
  }

  /// 测试直接跳转到手机号输入页面
  Future<void> testPhoneInputPage() async {
    try {
      QLog('=== 测试直接跳转到手机号输入页面 ===');

      final result = await Get.toNamed(AppRoute.phoneInput, arguments: {
        'bindKey': 'test-bind-key-123',
        'loginType': 'wechat',
        'originalData': {'test': 'data'},
      });

      QLog('手机号输入页面返回结果: $result');

    } catch (e, stackTrace) {
      QLog('测试手机号输入页面异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('测试失败: $e');
    }
  }

  /// 运行完整的手机号输入页面测试
  Future<void> runPhoneInputTest() async {
    try {
      QLog('=== 开始运行手机号输入页面测试 ===');

      // 1. 验证路由常量
      QLog('phoneInput路由: ${AppRoute.phoneInput}');
      QLog('verification路由: ${AppRoute.verification}');

      // 2. 测试路由跳转
      QLog('测试路由跳转...');
      final testArguments = {
        'bindKey': 'test-bind-key-12345',
        'loginType': 'wechat',
        'originalData': {'test': 'data'},
      };

      QLog('测试参数: $testArguments');

      final result = await Get.toNamed(AppRoute.phoneInput, arguments: testArguments);

      QLog('路由跳转结果: $result');

      if (result != null) {
        QLog('✅ 路由跳转成功');
        EasyLoading.showSuccess('测试成功');
      } else {
        QLog('⚠️ 路由跳转返回null（可能是用户取消）');
        EasyLoading.showInfo('测试完成（用户取消）');
      }

    } catch (e, stackTrace) {
      QLog('运行测试异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('测试失败: $e');
    }
  }

  /// 测试access_token保存和使用
  Future<void> testAccessTokenHandling() async {
    try {
      QLog('=== 测试access_token保存和使用 ===');

      // 1. 模拟登录成功返回的数据（包含access_token）
      final mockLoginData = {
        'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token_data',
        'userInfo': {
          'id': '12345',
          'nickname': '测试用户',
          'avatar': 'https://example.com/avatar.jpg',
        },
        'expires_in': 7200,
      };

      QLog('模拟登录数据: $mockLoginData');

      // 2. 保存用户信息（包括access_token）
      await saveUserInfo(mockLoginData);

      // 3. 验证token是否正确保存
      final savedToken = await Global.getToken();
      if (savedToken != null) {
        QLog('✅ access_token保存成功: ${savedToken.substring(0, 20)}...');
      } else {
        QLog('❌ access_token保存失败');
        EasyLoading.showError('Token保存失败');
        return;
      }

      // 4. 测试完成
      QLog('✅ access_token测试完成');
      EasyLoading.showSuccess('access_token测试成功');

    } catch (e, stackTrace) {
      QLog('测试access_token异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('测试失败: $e');
    }
  }

  /// 运营商一键登录（简化版本）
  Future<void> carrierLogin() async {
    try {
      QLog('开始运营商一键登录...');

      // 1. 检查运营商登录可用性
      final availability = await CarrierLoginService.instance.checkAvailability();
      if (!availability.isAvailable) {
        EasyLoading.dismiss();
        _handleCarrierLoginUnavailable(availability);
        return;
      }

      // 2. 初始化阿里云DYPNS SDK
      final initSuccess = await CarrierLoginService.instance.initialize();
      if (!initSuccess) {
        EasyLoading.dismiss();
        _showCarrierLoginFailedDialog('初始化失败', '请检查网络连接后重试');
        return;
      }

      QLog('✅ 运营商一键登录初始化成功');

      // 3. 直接调用登录方法显示授权页面
      EasyLoading.show(status: '正在启动授权页面...');

      try {
        // 调用AliAuth.login()显示授权页面
        await AliAuth.login();
        EasyLoading.dismiss();
        QLog('✅ 运营商授权页面已显示');
      } catch (e) {
        EasyLoading.dismiss();
        QLog('❌ 显示授权页面失败: $e');
        _showCarrierLoginFailedDialog('启动失败', '无法显示授权页面，请重试');
      }

    } catch (e) {
      QLog('运营商一键登录失败: $e');
      EasyLoading.dismiss();
      _showCarrierLoginFailedDialog('一键登录失败', '网络异常，请重试或使用其他登录方式');
    }
  }

  /// 处理运营商登录不可用的情况
  void _handleCarrierLoginUnavailable(CarrierAvailability availability) {
    String title = '一键登录不可用';
    String message = '';

    switch (availability.errorCode) {
      case 'CONFIG_INVALID':
        message = '配置异常，请联系客服';
        break;
      case 'INIT_FAILED':
        message = '初始化失败，请重试或使用手机号登录';
        break;
      case 'CHECK_FAILED':
        message = '检查失败，请使用手机号验证码登录';
        break;
      default:
        message = availability.errorMessage ?? '当前环境不支持一键登录';
        break;
    }

    _showCarrierLoginFailedDialog(title, message);
  }

  /// 显示运营商登录失败对话框
  void _showCarrierLoginFailedDialog(String title, String message) {
    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              // 自动滚动到手机号登录区域
              _scrollToPhoneLogin();
            },
            child: Text('使用手机号登录'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 滚动到手机号登录区域
  void _scrollToPhoneLogin() {
    // 这里可以添加滚动逻辑，如果页面有ScrollController的话
    QLog('引导用户使用手机号登录');
  }

  /// 测试没有bindKey的手机号+验证码登录流程
  Future<void> testPhoneVerificationLogin() async {
    try {
      QLog('=== 测试没有bindKey的手机号+验证码登录流程 ===');

      // 跳转到手机号输入页面（没有bindKey）
      final result = await Get.toNamed(AppRoute.phoneInput, arguments: {
        // 注意：这里没有传递bindKey，模拟普通的手机号登录
        // 'bindKey': '11',
        'loginType': 'phone',
        'clientId': clientId,
      });

      QLog('手机号+验证码登录流程返回结果: $result');

      if (result != null && result['success'] == true) {
        if (result['directLogin'] == true) {
          QLog('✅ 手机号+验证码直接登录成功');
          QLog('用户信息: ${result['userData']}');

          // 保存用户信息（包括access_token）
          await saveUserInfo(result['userData']);

          // 跳转到主Tab页面，默认显示"我的"页面并刷新WebView token
          _navigateToMainTab();
          EasyLoading.showSuccess('验证成功');
        } else {
          QLog('✅ 手机号验证流程完成');
          EasyLoading.showSuccess('验证成功');
        }
      } else {
        QLog('⚠️ 手机号+验证码登录流程被取消或失败');
        // EasyLoading.showInfo('登录流程被取消');
      }

    } catch (e, stackTrace) {
      QLog('测试手机号+验证码登录异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('测试失败: $e');
    }
  }

  /// 检查登录状态
  static Future<bool> checkLoginStatus() async {
    try {
      QLog('=== 检查登录状态 ===');

      // 1. 检查本地是否有token
      final token = await Global.getToken();
      if (token == null || token.isEmpty) {
        QLog('本地没有token，需要登录');
        return false;
      }

      QLog('本地存在token: ${token.substring(0, 20)}...');

      // 2. 调用获取个人信息接口验证token有效性
      final result = await API.getMemberInfoApi();

      if (result.state == 200 && result.data != null) {
        QLog('✅ 登录状态有效，用户信息: ${result.data}');

        // 可以选择更新本地用户信息
        await _updateLocalUserInfo(result.data);

        return true;
      } else {
        QLog('❌ 登录状态无效: state=${result.state}, msg=${result.msg}');

        // token无效，清除本地存储
        await Global.removeAllTokens();

        return false;
      }

    } catch (e) {
      QLog('检查登录状态异常: $e');

      // 网络异常或其他错误，根据具体情况处理
      if (e.toString().contains('401') || e.toString().contains('403')) {
        // 认证失败，清除token
        QLog('认证失败，清除本地token');
        await Global.removeAllTokens();
        return false;
      } else {
        // 网络异常等，暂时认为登录状态有效（避免网络问题导致频繁登录）
        QLog('网络异常，暂时保持登录状态');
        return true;
      }
    }
  }

  /// 更新本地用户信息
  static Future<void> _updateLocalUserInfo(Map<String, dynamic> userInfo) async {
    try {
      final sp = await SharedPreferences.getInstance();
      await sp.setString(Config.USER_INFO, jsonEncode(userInfo));
      QLog('本地用户信息已更新');
    } catch (e) {
      QLog('更新本地用户信息失败: $e');
    }
  }

  /// 应用启动时的登录状态检查和页面跳转
  static Future<void> handleAppStartup() async {
    try {
      QLog('=== 应用启动，检查登录状态 ===');

      final isLoggedIn = await checkLoginStatus();

      if (isLoggedIn) {
        QLog('用户已登录，跳转到主Tab页面');
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3}); // 跳转到主Tab页面，默认显示"我的"页面
      } else {
        QLog('用户未登录，跳转到登录页面');
        Get.offAllNamed('/login'); // 跳转到登录页面，清除所有页面栈
      }

    } catch (e) {
      QLog('应用启动检查异常: $e');
      // 异常情况下跳转到登录页面
      Get.offAllNamed('/login');
    }
  }

  /// 测试登录状态检查
  Future<void> testLoginStatusCheck() async {
    try {
      QLog('=== 测试登录状态检查 ===');

      final isLoggedIn = await checkLoginStatus();

      if (isLoggedIn) {
        QLog('✅ 登录状态检查通过');
        EasyLoading.showSuccess('登录状态有效');
        Get.offAllNamed('/home');
      } else {
        QLog('❌ 登录状态检查失败');
        EasyLoading.showError('登录状态无效，需要重新登录');
      }

    } catch (e, stackTrace) {
      QLog('测试登录状态检查异常: $e');
      QLog('异常堆栈: $stackTrace');
      EasyLoading.showError('检查失败: $e');
    }
  }



  /// 保存用户信息
  Future<void> saveUserInfo(dynamic userData, {bool showSuccessToast = true}) async {
    if (userData == null) return;

    QLog('=== 开始保存用户信息 ===');
    QLog('用户数据: $userData');
    QLog('用户数据类型: ${userData.runtimeType}');
    if (userData is Map) {
      QLog('用户数据所有字段: ${(userData as Map).keys.toList()}');
    }
    QLog('========================');

    try {
      final sp = await SharedPreferences.getInstance();

      // 保存access_token或token（支持多种字段名）
      String? token;
      if (userData['access_token'] != null) {
        token = userData['access_token'];
        QLog('检测到access_token字段: $token');
      } else if (userData['token'] != null) {
        token = userData['token'];
        QLog('检测到token字段: $token');
      } else if (userData['accessToken'] != null) {
        token = userData['accessToken'];
        QLog('检测到accessToken字段: $token');
      }

      // 使用Global类的setToken方法保存token
      if (token != null && token.isNotEmpty) {
        await Global.setToken(token);
        QLog('✅ access_token已保存到本地存储');
      } else {
        QLog('⚠️ 未找到有效的token字段');
      }

      // 保存lima_token
      String? limaToken;
      if (userData['lima_token'] != null) {
        limaToken = userData['lima_token'];
        QLog('检测到lima_token字段: $limaToken');
      }

      if (limaToken != null && limaToken.isNotEmpty) {
        await Global.setLimaToken(limaToken);
        QLog('✅ lima_token已保存到本地存储');
      } else {
        QLog('⚠️ 未找到有效的lima_token字段');
      }

      // 保存用户信息
      if (userData['userInfo'] != null) {
        await sp.setString(Config.USER_INFO, jsonEncode(userData['userInfo']));
        QLog('✅ 用户信息已保存');
      } else {
        // 如果没有单独的userInfo，将整个userData作为用户信息保存
        // 但要排除敏感的token信息
        final userInfoData = Map<String, dynamic>.from(userData);
        userInfoData.remove('access_token');
        userInfoData.remove('token');
        userInfoData.remove('accessToken');
        userInfoData.remove('lima_token');

        await sp.setString(Config.USER_INFO, jsonEncode(userInfoData));
        QLog('✅ 用户数据已保存（已排除token）');
      }

      QLog('✅ 用户信息和token保存成功');

      // 显示登录成功提示（可选）
      if (showSuccessToast) {
        EasyLoading.showSuccess('登录成功');
      }
    } catch (e) {
      QLog('❌ 保存用户信息失败: $e');
    }
  }
  
  /// 切换用户协议同意状态
  void toggleAgreement() {
    if (!isAgreementAccepted.value) {
      // 如果当前未同意，显示确认弹窗
      showAgreementConfirmDialog();
    } else {
      // 如果已同意，直接取消同意
      isAgreementAccepted.value = false;
      validateForm();
    }
  }

  /// 显示用户协议确认弹窗
  void showAgreementConfirmDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('用户协议确认'),
        content: const Text(
          '请仔细阅读并确认您同意《用户协议》和《隐私政策》的全部内容。\n\n点击"确认"表示您已阅读并同意相关条款。',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // 关闭弹窗，不同意协议
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back(); // 关闭弹窗
              isAgreementAccepted.value = true; // 同意协议
              validateForm(); // 重新验证表单
            },
            child: const Text(
              '确认',
              style: TextStyle(color: Color(0xFFC70E2D)),
            ),
          ),
        ],
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }

  /// 显示用户协议
  void showUserAgreement() {
    Get.to(() => WebviewPage(), arguments: {
      'url': '${Config.BASE_WEBSITE}/me/about/agreement?nav=false',
      'navBar': false,
      'title': '用户协议',
    });
  }

  /// 显示隐私政策
  void showPrivacyPolicy() {
    Get.to(() => WebviewPage(), arguments: {
      'url': Config.USER_PROTOCOL,
      'navBar': false,
      'title': '隐私政策',
    });
  }

  /// 密码登录
  Future<void> passwordLogin() async {
    try {
      QLog('跳转到密码登录页面');

      // 跳转到密码登录页面
      final result = await Get.toNamed('/password_login');

      if (result != null && result['success'] == true) {
        QLog('密码登录成功，保存用户信息');
        await saveUserInfo(result['userData']);
        _navigateToMainTab();
      }
    } catch (e) {
      QLog('密码登录异常: $e');
      EasyLoading.showError('跳转失败');
    }
  }

  /// 忘记密码
  Future<void> forgotPassword() async {
    try {
      QLog('跳转到忘记密码页面');

      // 跳转到忘记密码页面
      final result = await Get.toNamed('/forgot_password');

      if (result != null && result['success'] == true) {
        QLog('密码重置成功: ${result['message']}');
        EasyLoading.showSuccess(result['message'] ?? '密码重置成功');

        // 如果返回了手机号，可以预填到登录表单中
        if (result['mobile'] != null) {
          phoneController.text = result['mobile'];
          validateForm();
        }
      }
    } catch (e) {
      QLog('忘记密码异常: $e');
      EasyLoading.showError('跳转失败');
    }
  }

  /// 导航到主Tab页面，支持hash导航（公共方法）
  void navigateToMainTab() {
    _navigateToMainTab();
  }

  /// 导航到主Tab页面，支持hash导航
  void _navigateToMainTab() {
    try {
      QLog('登录成功，导航到主Tab页面');

      // 跳转到主Tab页面，默认显示"我的"页面
      Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});

      // 延迟一下确保MainTabLogic已经初始化，然后刷新WebView token并触发H5导航
      Future.delayed(const Duration(milliseconds: 500), () {
        try {
          // 刷新车辆和服务页面的WebView token
          _refreshWebViewTokens();

          final mainTabLogic = Get.find<MainTabLogic>();
          if (mainTabLogic.currentIndex.value == 3) {
            QLog('登录成功后直接调用H5导航到我的页面');
            // 直接调用导航方法，模拟tab切换
            mainTabLogic.triggerH5Navigation(3);
          }
        } catch (e) {
          QLog('登录成功后触发H5导航失败: $e');
        }
      });
    } catch (e) {
      QLog('导航到主Tab页面失败: $e');
      // 异常情况下使用简单跳转
      Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
    }
  }

  /// 刷新车辆和服务页面的WebView token
  void _refreshWebViewTokens() {
    try {
      QLog('开始刷新车辆和服务页面的WebView token');

      // 刷新车辆页面token
      try {
        final homeLogic = Get.find<HomeLogic>();
        homeLogic.refreshToken();
        QLog('✅ 车辆页面token刷新成功');
      } catch (e) {
        QLog('⚠️ 车辆页面token刷新失败: $e');
      }

      // 刷新服务页面token
      try {
        final serviceLogic = Get.find<ServiceLogic>();
        serviceLogic.refreshToken();
        QLog('✅ 服务页面token刷新成功');
      } catch (e) {
        QLog('⚠️ 服务页面token刷新失败: $e');
      }

    } catch (e) {
      QLog('刷新WebView token异常: $e');
    }
  }
}
