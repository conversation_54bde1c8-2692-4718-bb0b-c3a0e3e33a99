import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:lima/common/bridge_controller.dart';
import 'package:lima/common/jsapi.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/q_log.dart';

import '../../../utils/jsbridge/message.dart';
import '../../../utils/jsbridge/native_bridge_helper.dart';
import '../../../utils/jsbridge/native_bridge_impl.dart';

final scriptReceive = UserScript(source: '''
function receiveMessage(jsonStr) {
    if(jsonStr !== undefined && jsonStr !== "") {
        let data = JSON.parse(JSON.stringify(jsonStr));
        window.jsBridgeHelper.receiveMessage(data);
    }
}
''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

Future<String> get jsAsserts async =>
    rootBundle.loadString('assets/jsBridgeHelper.js');

// const remoteConsoleScript =
//     '<script crossorigin="anonymous" src="http://***********:6752/page-spy/index.min.js"></script>';

// const remoteConsoleScriptRequest = '''
//   if(window.\$pageSpy == undefined || window.\$pageSpy == null){
//     console.log("bridge:pageSpy");
//   }
//  ''';
// const remoteConsoleScriptAction = '''
//   if(window.\$pageSpy == undefined || window.\$pageSpy == null){
//     window.\$pageSpy = new PageSpy();
//   }
//  ''';

UserScript? bridgeScript;

@Deprecated('use WebPage instead')
class WebLogic extends GetxController {
  RxString url = ''.obs;

  ///webview 控制器

  InAppWebViewController? controller;

  RxBool showError = false.obs;

  ///向外拓展的controller
  WebController? myWebController;

  WebUri? runningUrl;

  CookieManager cookieManager = CookieManager.instance();


  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    // QLog('读取的js资源: ${await jsAsserts}');
  }

  Future webviewCreated(InAppWebViewController c) async {
    controller = c;
    /// 设置cookie
    var token = await Global.getToken();
    var phone  = await Global.getUserPhone();
    if (token != null) {
      await cookieManager.setCookie(
          url: WebUri(url.value), name: 'token', value: token);
    }
    if (phone != null) {
      await cookieManager.setCookie(
          url: WebUri(url.value), name: 'phone', value: phone);
    }

    await controller?.addUserScript(userScript: scriptReceive);

    bridgeScript = UserScript(source: '''
    window.jsBridgeHelper = ${await jsAsserts};
    ''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

    await controller?.addUserScript(userScript: bridgeScript!);

    controller?.addJavaScriptHandler(
        handlerName: 'receiveMessage', callback: callback);
  }

  void onPageLoadFinish() async {
     QLog('加载bridge插件, ${controller?.hasUserScript(userScript: bridgeScript!)}');
    if (bridgeScript != null &&
        controller?.hasUserScript(userScript: bridgeScript!) == false) {
      QLog('加载bridge插件');
      await controller?.addUserScript(userScript: bridgeScript!);
    }
    /// 2023年12月01日 移除加载远程控制台的逻辑

    // /// 触发加载远程控制台的逻辑
    // var currentUrl = await controller?.getUrl();
    // var hostUrl = WebUri(url.value);
    // if (currentUrl?.host == hostUrl.host) {
    //   controller?.evaluateJavascript(source: remoteConsoleScriptRequest);
    // }
  }

  ///远程控制台SDK。
  /// 在webview onLoadStop时触发

  @Deprecated('移除加载远程控制台的逻辑')
  void loadPageSpy() async {
    // QLog('加载console插件');
    // await controller?.injectJavascriptFileFromUrl(
    //     urlFile: WebUri('http://***********:6752/page-spy/index.min.js'),
    //     scriptHtmlTagAttributes: ScriptHtmlTagAttributes(
    //       id: 'page-spy',
    //       crossOrigin: CrossOrigin.fromValue('anonymous'),
    //       onLoad: () {
    //         QLog('插件加载成功');
    //         Future.delayed(Duration(milliseconds: 500)).then((value) =>
    //             controller?.evaluateJavascript(
    //                 source: remoteConsoleScriptAction));
    //       },
    //       onError: () {
    //         QLog('插件加载失败');
    //       },
    //     ));
  }

  void webviewReload() async {
    await controller?.clearHistory();
    controller?.loadUrl(urlRequest: URLRequest(url: runningUrl));
  }

  ///处理消息回调
  dynamic callback(List<dynamic> arguments) async {
    for (var element in arguments) {
      QLog('$element',type: LogInfoType.app);
      if (!NativeBridgeHelper.parseReceiveMessage(element)) {
        var res = await JSApi.parseJsRequest(element);
        return res;
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}

/// js数据: {"api":"startRecord","callbackId":"startRecord_1","responseFlag":false}

class WebController with NativeBridgeImpl {
  InAppWebViewController? controller;

  WebController(InAppWebViewController c) {
    controller = c;
  }

  void reload() => controller?.reload();

  Future? loadUrl(String url) async =>
      await controller?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));

  Future sendMessageToJs(String api,
      {dynamic data, bool? nativeResponseFlag}) async {
    Message message = Message(
        api: api, data: data, nativeResponseFlag: nativeResponseFlag ?? false);
    return NativeBridgeHelper.sendMessage(message, this).future;
  }

  @override
  void runJavaScript(String javaScriptString) {
    controller?.evaluateJavascript(source: javaScriptString);
  }

  @override
  Future<Object> runJavaScriptReturningResult(String javaScriptString) {
    throw UnimplementedError();
  }
}
