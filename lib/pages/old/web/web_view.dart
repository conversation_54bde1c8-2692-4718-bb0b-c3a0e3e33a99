import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:url_launcher/url_launcher.dart';

import 'web_logic.dart';

@Deprecated('use WebPage instead')
class WebPage extends StatelessWidget {
  final logic = Get.put(WebLogic());

  var content = '''
  <!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    </head>
    <body>
        <h1>JavaScript Handlers</h1>
        <script>
            window.addEventListener("flutterInAppWebViewPlatformReady", function(event) {
                window.flutter_inappwebview.callHandler('receiveMessage')
                  .then(function(result) {
                    // print to the console the data coming
                    // from the Flutter side.
                    console.log(JSON.stringify(result));
                    
                    window.flutter_inappwebview
                      .callHandler('receiveMessage', 1, true, ['bar', 5], {foo: 'baz'}, result);
                });
            });
        </script>
    </body>
</html>
  ''';

  WebPage({super.key, this.homeUrl, this.onWebCreated, this.onLoadFinished}) {
    logic.url.value = homeUrl ?? '';
  }

  final String? homeUrl;
  final void Function(WebController webController)? onWebCreated;
  final void Function(String? loadUrl)? onLoadFinished;

  @override
  Widget build(BuildContext context) {
    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(homeUrl ?? '')),
      // initialData: InAppWebViewInitialData(data: content),
      onLongPressHitTestResult: (c, r) {
        ///禁止长按弹出浏览器默认menu的方式就是在webPage外面拦截长按
        ///
        QLog('禁止长按 =>  onLongPressHitTestResult, $r',type: LogInfoType.webview);
      },
      initialSettings: InAppWebViewSettings(
        disableDefaultErrorPage: true,
        verticalScrollBarEnabled: false,
        horizontalScrollBarEnabled: false,
        supportZoom: false,
      ),
      onWebViewCreated: (c) async{
        QLog('onWebViewCreated',type: LogInfoType.webview);
        await logic.webviewCreated(c);
        logic.myWebController = WebController(c);
        if (onWebCreated != null) {
          onWebCreated!(logic.myWebController!);
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        QLog('shouldOverrideUrlLoading $navigationAction',type: LogInfoType.webview);
        var uri = navigationAction.request.url ?? Uri();
        if (!["http", "https", "file", "chrome", "data", "javascript", "about"]
            .contains(uri.scheme)) {
          if (await canLaunchUrl(uri)) {
            // Launch the App
            await launchUrl(
              uri,
            );
            // and cancel the request
            return NavigationActionPolicy.CANCEL;
          }
        }

        return NavigationActionPolicy.ALLOW;
      },
      onLoadStart: (controller, url) {
        QLog('正在加载 $url',type: LogInfoType.webview);
      },
      onLoadStop: (controller, url) async {
        QLog('url加载完成: ${url?.uriValue.toString()}',type: LogInfoType.webview);

        if (onLoadFinished != null) {
          onLoadFinished?.call(url?.uriValue.toString());
        }
        logic.onPageLoadFinish();
        // if (logic.url.isEmpty) {
        //   logic.url.value = '1';
        //   controller.loadData(data: await errorHtml());
        //   logic.showError.value = true;
        // }
      },
      onReceivedError: (controller, resourceRequest, webResourceError) async {
        QLog(
            'onReceivedError: ${resourceRequest.url} -- ${webResourceError.description}',type: LogInfoType.webview);
        var isForMainFrame = resourceRequest.isForMainFrame ?? false;
        if (!isForMainFrame ||
            (defaultTargetPlatform == TargetPlatform.iOS &&
                webResourceError.type == WebResourceErrorType.CANCELLED)) {
          return;
        }
        logic.runningUrl = resourceRequest.url;
        controller.loadData(data: await errorHtml());
      },
      onConsoleMessage: (controller, consoleMessage) {
        QLog(consoleMessage.message,type: LogInfoType.web);
        if (consoleMessage.message.contains('blank:reload')) {
          logic.webviewReload();
        }
      },
    );
  }

  Widget EmptyWidget(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('> …… < 暂无数据'),
            TextButton(
              onPressed: () {
                logic.webviewReload();
              },
              child: Text('重新加载'),
            ),
          ],
        ),
      ),
    );
  }

  Future<String> errorHtml() async => '''
   <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
              <meta http-equiv="X-UA-Compatible" content="ie=edge">
              <style>
              ${await InAppWebViewController.tRexRunnerCss}
              </style>
              <style>
              .interstitial-wrapper {
                  box-sizing: border-box;
                  font-size: 1em;
                  line-height: 1.6em;
                  margin: 0 auto 0;
                  max-width: 600px;
                  width: 100%;
                  height: 100%;
                 min-height: 100vh;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
              }
              </style>
              <script>
                function refresh() {
                  console.log('blank:reload');
                }
              </script>
          </head>
          <body>
              <div class="interstitial-wrapper" onclick="refresh()">
                <p> > …… < 似乎与网络失去了连接</p>
                <p>点击屏幕刷新</p>
              </div>
          </body>
  ''';
}
