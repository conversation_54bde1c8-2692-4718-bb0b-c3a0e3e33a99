import 'package:flutter/material.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'package:get/get.dart';
import 'package:lima/pages/web/web_page.dart';

import '../../../utils/q_log.dart';

class WebviewPage extends StatefulWidget {
  WebviewPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<WebviewPage> {
  bool? hasNavBar = false;
  String url = '';
  String? title = '';
  WebController? myWebController;

  @override
  void initState() {
    super.initState();
    var arguments = Get.arguments;
    if (arguments != null) {
      hasNavBar = arguments['navBar'] ?? false;
      url = arguments['url'] ?? '';
      title = arguments['title'] ?? '';
      QLog('当前网址 $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (hasNavBar == true) {
      return Scaffold(
        body: SafeArea(child: WebWidget()),
      );
    }
    return BaseScaffold(title: title, body: WebWidget());
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget WebWidget() {
    return Container(
      child: WebPage(
          homeUrl: url,
          onWebCreated: (c) {
            myWebController = c;
          }),
    );
  }
}
