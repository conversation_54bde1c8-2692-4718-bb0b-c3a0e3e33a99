import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_container.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/icons.dart';
import 'package:video_player/video_player.dart';

import 'splash_logic.dart';

class SplashPage extends StatelessWidget {
  final logic = Get.put(SplashLogic());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
          body: Stack(
        children: [
          logic.advReady.value
              ? ImgSplashWidget(context)
              : NewSplashWidget(context),
          Visibility(
            visible: logic.advReady.value,
            child: SafeArea(
              child: Align(
                alignment: Alignment.topRight,
                child: InkWell(
                  onTap: (){
                    logic.skip();
                  },
                  child: QContainerCircle(
                    width: 60.w,
                    height: 25.h,
                    radius: 10.r,
                    color: Colors.white,
                    margin: EdgeInsets.only(top: 10.h, right: 10.w),
                    child: Center(
                      child: Text(
                        '${logic.count.value}s跳过',
                        style: TextStyle(
                          fontSize: 12.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ));
    });
  }

  Widget NewSplashWidget(BuildContext context) {
    return Container(
      // color: Colors.red,
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,

      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 150.w,
            height: 150.h,
            child: Image.asset(BIcons.splashLogoPng, fit: BoxFit.fitHeight),
          ).marginOnly(top: 220.h),
          Spacer(),
          Align(
            alignment: Alignment.bottomCenter,
            child: QText(
              '小微出行全球领导者',
              style: TextStyle(
                fontSize: 16.sp,
                fontFamily: 'Source Han Sans CN',
                fontWeight: FontWeight.w400,
                color: Color(0xFF444343),
              ),
            ),
          ).marginOnly(bottom: 80.h)
        ],
      ),
    );
  }

  Widget ImgSplashWidget(BuildContext context) {
    if (logic.localImgPath.isVideoFileName) {
      return Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: AspectRatio(
            aspectRatio: logic.videoController!.value.aspectRatio,
            child: VideoPlayer(logic.videoController!),
          ));
    }
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: Image.file(
        File(logic.localImgPath),
        fit: BoxFit.fill,
      ),
    );
  }
}
