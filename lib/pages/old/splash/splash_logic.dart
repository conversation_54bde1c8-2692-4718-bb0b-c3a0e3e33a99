import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:video_player/video_player.dart';

import '../../../components/q_alert.dart';
import '../../../global.dart';
import '../../../utils/q_log.dart';

class SplashLogic extends GetxController {
  int logoShowPeriod = 2;
  ///广告剩余时间
  RxInt count = 2.obs;
  Timer? _timer;

  ///广告播放完毕
  bool advFinished = false;
  ///主页加载完毕
  bool homeFinished = false;

  ///本地图片路径
  String localImgPath = Global.localAdvPath;


  StreamSubscription? subscription ,connectivitySubscription;

  ///默认网络是连通的
  bool netConnected = true;

  ///视频播放器
  VideoPlayerController? videoController;
  RxBool advReady = false.obs;

  @override
  void onInit() async{
    super.onInit();
    connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((event) {
      QLog('当前网络状态:${event}');
      if (event == ConnectivityResult.none) {
        netConnected = false;
      }
      if (event == ConnectivityResult.wifi ||
          event == ConnectivityResult.mobile) {
        if (!netConnected) {
          Get.find<HomeLogic>().refreshUrl(Config.BASE_WEBSITE);
        }
      }
    });
    if(localImgPath.isVideoFileName){
      videoController = VideoPlayerController.file(File(localImgPath))..initialize().then((value) async{
        count.value = videoController!.value.duration.inSeconds;

        await Future.delayed(Duration(seconds: logoShowPeriod));
        advReady.value = true;
        videoController?.play();
        startCount();
      },onError: (e){
        QLog('视频播放失败:${e}');
        skip();
      });
    }else{
      if(localImgPath.isNotEmpty) {
        await Future.delayed(Duration(seconds: logoShowPeriod));
        advReady.value = true;
      }
      startCount();
    }
  }

  void startCount() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (count.value == 0) {
        _timer?.cancel();
        advFinished = true;
        checkExit();
      } else {
        count.value--;
      }
    });
  }

  void skip() {
    _timer?.cancel();
    advFinished = true;
    checkExit();
  }

  void changeWebDialog() {
    if(Config.BASE_WEBSITE.isNotEmpty){
      Get.find<HomeLogic>().fistComeIn = false;
      return;
    }

    Get.dialog(
      QInputAlert(
        title: "修改网址",
        hintText: "请输入网址",
        text: Config.BASE_WEBSITE.isEmpty ? 'http://' : Config.BASE_WEBSITE,
        onConfim: (s) async {
          if (s.isNotEmpty && s != Config.BASE_WEBSITE) {
            Get.find<HomeLogic>().refreshUrl(s);

          }
          Get.find<HomeLogic>().fistComeIn = false;
          await Global.updateWebSite(url: s);
          checkExit();
        },
        onCancel: () {},
        tapDismiss: false,
      ),
      barrierDismissible: false,
    );
  }

  @override
  void onReady() {
    super.onReady();
    subscription = Get.find<HomeLogic>().loadFinished.listen((p0) {
      homeFinished = p0;
      checkExit();
    });
    Get.find<HomeLogic>().initWidget();


  }

  Future checkExit() async{
    if(homeFinished && advFinished){
      await videoController?.pause();
      await videoController?.dispose();
      await subscription?.cancel();
      Get.back(closeOverlays: true);
    }
  }

  @override
  void onClose() {
    super.onClose();
    _timer?.cancel();
    subscription?.cancel();
    connectivitySubscription?.cancel();
    videoController?.dispose();
  }
}
