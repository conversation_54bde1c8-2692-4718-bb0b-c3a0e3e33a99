import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lima/common/jsapi.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../common/ble/permissions.dart';
import '../../../utils/q_log.dart';

class QrScanLogic extends GetxController {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  StreamSubscription? subscription;

  String lightTitle = '打开手电筒';

  MobileScannerController mobileScannerController = MobileScannerController();
  ImagePicker imagePicker = ImagePicker();

  ///是否显示输入框
  bool showInput = false;

  String pageTitle = '扫一扫';
  String description = '将二维码/条码放入框内，即可自动扫描';

  @override
  void onInit() {
    super.onInit();
    var arg = Get.arguments;
    if (arg != null) {
      showInput = arg['showInput'] ?? false;
      pageTitle = arg['title'] ?? '扫一扫';
      description = arg['description'] ?? '将二维码/条码放入框内，即可自动扫描';
    }
  }

  void inputTap() {
    Get.back(result: {'code': 301});
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    subscription?.cancel();
    mobileScannerController.dispose();
    super.onClose();
  }

  void barCodeDetect(BarcodeCapture capture) async {
    final List<Barcode> barcodes = capture.barcodes;
    final Uint8List? image = capture.image;
    for (final barcode in barcodes) {
      debugPrint('Barcode found! ${barcode.rawValue}');
      await mobileScannerController.stop();
      Get.back(result: barcode.rawValue);
    }
  }

  void barcodeFromGalleryListen() {
    subscription?.cancel();
    subscription = mobileScannerController.barcodes.listen((capture) async {
      final List<Barcode> barcodes = capture.barcodes;
      for (final barcode in barcodes) {
        debugPrint('gallery found:  ${barcode.rawValue}');
        await mobileScannerController.stop();
        Get.back(result: barcode.rawValue);
      }
    });
  }

  void controlLight() async {
    mobileScannerController.toggleTorch();
  }

  void chooseFromAlbum() async {
    mobileScannerController.stop();
    await actChooseFromAlbum();

    // if (GetPlatform.isIOS) {
    //   await actChooseFromAlbum();
    // }else{
    //   QLog(await Permission.storage.status);
    //   if(await Permission.storage.status!=PermissionStatus.granted){
    //     await PermissionManager.questPermsInfoDialog(
    //         title: '打开相册',
    //         msg: '''
    //         立马需要使用您的 相册 权限，以便您通过相册选择图片。
    //         如您关闭或拒绝权限调取，将导致上述功能无法正常使用，但不影响使用本应用的基本功能。具体请参阅''',
    //         onConfirm: () async {
    //           await actChooseFromAlbum();
    //
    //         },
    //         onCancel: () => {  mobileScannerController.start()});
    //   }else{
    //     await actChooseFromAlbum();
    //   }
    //
    // }
  }

  Future actChooseFromAlbum() async {
    XFile? image = await imagePicker.pickImage(
      source: ImageSource.gallery,
    );
    if (image != null) {
      var res = await mobileScannerController.analyzeImage(image.path);
      if (res == false) {
        EasyLoading.showToast('未识别到二维码');
      }
      mobileScannerController.start();
    }
  }
}
