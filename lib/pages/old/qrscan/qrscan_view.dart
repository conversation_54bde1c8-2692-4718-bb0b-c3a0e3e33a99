import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lima/res/icons.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../components/custom_scaffold.dart';
import '../../../res/colors.dart';


class QrScanPage extends StatefulWidget{
  QrScanPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _QrScanPageState();
}

class _QrScanPageState extends State<QrScanPage> with SingleTickerProviderStateMixin  {
  String lightTitle = '打开手电筒';

  MobileScannerController mobileScannerController = MobileScannerController();
  ImagePicker imagePicker = ImagePicker();
  ///是否显示输入框
  bool showInput = false;

  String pageTitle = '扫一扫';
  String description = '将二维码/条码放入框内，即可自动扫描';
  StreamSubscription? subscription;


  @override
  void initState() {
    super.initState();
    var arg = Get.arguments;
    if (arg != null) {
      showInput = arg['showInput'] ?? false;
      pageTitle = arg['title'] ?? '扫一扫';
      description = arg['description'] ?? '将二维码/条码放入框内，即可自动扫描';
    }
    barcodeFromGalleryListen();
  }
  @override
  void dispose() {
    mobileScannerController.dispose();
    subscription?.cancel();
    super.dispose();
  }

  void barcodeFromGalleryListen() {
    subscription?.cancel();
    subscription = mobileScannerController.barcodes.listen((capture) async {
      final List<Barcode> barcodes = capture.barcodes;
      for (final barcode in barcodes) {
        debugPrint('gallery11 found:  ${barcode.rawValue}');
        await mobileScannerController.stop();
        Get.back(result: barcode.rawValue);
      }
    });
  }

  void barCodeDetect(BarcodeCapture capture) async {
    final List<Barcode> barcodes = capture.barcodes;
    for (final barcode in barcodes) {
      debugPrint('Barcode222 found! ${barcode.rawValue}');
      await mobileScannerController.stop();
      Get.back(result: barcode.rawValue);
    }
  }

  void chooseFromAlbum() async {
    mobileScannerController.stop();
    await actChooseFromAlbum();

  }
  Future actChooseFromAlbum() async {
    XFile? image = await imagePicker.pickImage(
      source: ImageSource.gallery,
    );
    if (image != null) {
      var res = await mobileScannerController.analyzeImage(image.path);
      if (res == false) {
        EasyLoading.showToast('未识别到二维码');
      }
      // mobileScannerController.start();
    }
  }
  void inputTap() {
    Get.back(result: {'code': 301});
  }
  void controlLight() async {
    mobileScannerController.toggleTorch();
  }


  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
        title: "${pageTitle}",
        titleColor: AppColors.color_FFFFFFFF,
        extendBodyBehindAppBar: true,
        appBarBackgroundColor: Colors.transparent,
        backBtnColor: AppColors.color_FFFFFFFF,
        body: Container(
          color: Colors.black,
          child: Stack(
            children: [
              QrViewWidget(context),
              Align(
                alignment: Alignment.bottomCenter,
                child: BottomFunctionWidget(context),
              )
            ],
          ),
        ),
      );
  }

  Widget QrViewWidget(BuildContext context) {
    var scanWindow = Rect.fromCenter(
      center: MediaQuery.of(context).size.topCenter(Offset(0, 300.h)),
      width: 250.w,
      height: 290.h,
    );

    return Stack(
      fit: StackFit.expand,
      children: [
        MobileScanner(
          scanWindow: scanWindow,

          // onScannerStarted: (a){
          //   logic.barcodeStart();
          // },
        onDetect: (_){},
          controller: mobileScannerController,
        ),
        CustomPaint(
          painter: ScannerOverlay(scanWindow),
        ),
      ],
    );
  }


  Widget BottomFunctionWidget(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 250.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            // height: 80.h,
            padding: const EdgeInsets.all(5),
            alignment: AlignmentDirectional.center,

            child: Text(
              '${description}',
              style: TextStyle(
                color: Colors.white,
                fontSize: 15.sp,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Flexible(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: (){
                        chooseFromAlbum();
                      },
                      child: Container(
                        width: 60.r,
                        height: 60.r,
                        decoration: const ShapeDecoration(
                          gradient: LinearGradient(
                            begin: Alignment(0.62, -0.79),
                            end: Alignment(-0.62, 0.79),
                            colors: [Color(0xFF2FB8FF), Color(0xFF9DEBD9)],
                          ),
                          shape: CircleBorder(),
                        ),
                        child: Center(
                          child: Image.asset(
                            BIcons.iconAlbum,
                            height: 20.h,
                            fit: BoxFit.fitHeight,
                          ),
                        ),
                      ),
                    ),
                    Text(
                      '相册',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                      ),
                    ).marginOnly(top: 10.h)
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        controlLight();
                      },
                      child: Container(
                        width: 60.r,
                        height: 60.r,
                        decoration: ShapeDecoration(
                            shape: CircleBorder(
                              side: BorderSide(
                                  width: 1.r, style: BorderStyle.none),
                            ),
                            color: Colors.white,
                            shadows: const [
                              BoxShadow(
                                  // color: MyColor().boxShadowColor,
                                  color: Color(0x26000000),
                                  offset: Offset(0, 2),
                                  //阴影xy轴偏移量
                                  blurRadius: 4.0,
                                  //阴影模糊程度
                                  spreadRadius: 2,
                                  //阴影扩散程度,
                                  blurStyle: BlurStyle.normal)
                            ]),
                        child: Center(
                          child: Image.asset(
                            BIcons.iconLight,
                            height: 20.h,
                            fit: BoxFit.fitHeight,
                          ),
                        ),
                      ),
                    ),
                    ValueListenableBuilder(
                        valueListenable:
                            mobileScannerController.torchState,
                        builder: (context, value, child) {
                          lightTitle =
                              value == TorchState.on ? '关闭手电筒' : '打开手电筒';
                          return Text(
                            lightTitle,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14.sp,
                            ),
                          ).marginOnly(top: 10.h);
                        }),
                  ],
                ),
              ),
            ],
          ).marginOnly(top: 10.h),
          Visibility(
            visible: showInput,
            child: InkWell(
              onTap: (){
                inputTap();
              },
              child: Container(
                width: 100.w,
                height: 44.h,
                margin: EdgeInsets.only(top: 10.h),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(20.r),

                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(BIcons.iconInput,height: 20.h,fit: BoxFit.fitHeight,),
                    Text('手动输入',
                    style: TextStyle(
                      color: Colors.white,
                    ),).marginOnly(left: 5.w)
                  ],
                )
              ),
            ),
          )
        ],
      ),
    );
  }
}

class ScannerOverlay extends CustomPainter {
  ScannerOverlay(this.scanWindow);

  final Rect scanWindow;
  final double borderRadius = 8.0;
  final borderColor = AppColors.color_FF2B6BFF;

  @override
  void paint(Canvas canvas, Size size) {
    final backgroundPath = Path()..addRect(Rect.largest);
    final cutoutPath = Path()
      ..addRRect(
        RRect.fromRectAndCorners(
          scanWindow,
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
          bottomLeft: Radius.circular(borderRadius),
          bottomRight: Radius.circular(borderRadius),
        ),
      );

    final backgroundPaint = Paint()
      ..color = const Color.fromRGBO(0, 0, 0, 120)
      ..style = PaintingStyle.fill;

    final backgroundWithCutout = Path.combine(
      PathOperation.difference,
      backgroundPath,
      cutoutPath,
    );

    // Create a Paint object for the white border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8.0; // Adjust the border width as needed

    // Calculate the border rectangle with rounded corners
// Adjust the radius as needed
    final borderRect = RRect.fromRectAndCorners(
      scanWindow,
      topLeft: Radius.circular(borderRadius),
      topRight: Radius.circular(borderRadius),
      bottomLeft: Radius.circular(borderRadius),
      bottomRight: Radius.circular(borderRadius),
    );

    // Draw the white border
    // canvas.drawPath(backgroundWithCutout, backgroundPaint);

    final width = scanWindow.width;
    final borderWidthSize = width / 2;
    final height = scanWindow.height;
    final borderOffset = 3 / 2;
    final _cutOutWidth =
        scanWindow.width < width ? scanWindow.width : width - borderOffset;
    final cutOutBottomOffset = 0;
    final _cutOutHeight =
        scanWindow.height < height ? scanWindow.height : height - borderOffset;

    final _borderLength =
        40 > min(scanWindow.height, scanWindow.height) / 2 + 3 * 2
            ? borderWidthSize / 2
            : 40;

    final cutOutRect = Rect.fromLTWH(
      scanWindow.left + width / 2 - _cutOutWidth / 2 + borderOffset,
      -cutOutBottomOffset +
          scanWindow.top +
          height / 2 -
          _cutOutHeight / 2 +
          borderOffset,
      _cutOutWidth - borderOffset * 2,
      _cutOutHeight - borderOffset * 2,
    );

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;
    canvas.drawPath(backgroundWithCutout, backgroundPaint);

    canvas
      ..saveLayer(
        scanWindow,
        backgroundPaint,
      )
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.right - _borderLength,
          cutOutRect.top,
          cutOutRect.right,
          cutOutRect.top + _borderLength,
          topRight: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.left,
          cutOutRect.top,
          cutOutRect.left + _borderLength,
          cutOutRect.top + _borderLength,
          topLeft: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.right - _borderLength,
          cutOutRect.bottom - _borderLength,
          cutOutRect.right,
          cutOutRect.bottom,
          bottomRight: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      // Draw bottom left corner
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.left,
          cutOutRect.bottom - _borderLength,
          cutOutRect.left + _borderLength,
          cutOutRect.bottom,
          bottomLeft: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      ..drawRRect(
        RRect.fromRectAndRadius(
          cutOutRect,
          Radius.circular(borderRadius),
        ),
        boxPaint,
      )
      ..restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
