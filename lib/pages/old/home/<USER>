import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'home_logic.dart';

class HomePage extends StatelessWidget {
  final logic = Get.put(HomeLogic());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () {
        logic.tapTime = DateTime.now().millisecondsSinceEpoch;
        QLog('onLongPress ${DateTime.now()}');
      },
      onLongPressDown: (d) {
        // QLog('onLongPressDown:${d.globalPosition}');
      },
      onLongPressEnd: (e) {
        QLog('onLongPressEnd:${DateTime.now()}');
        // logic.checkTapTime();

      },
      child: WillPopScope(
        onWillPop: () {
          int currentTime = DateTime.now().millisecondsSinceEpoch;
          int time = logic.backTime > currentTime
              ? logic.backTime - currentTime
              : currentTime - logic.backTime;

          // QLog('时间为${time} === ${time < 3 * 1000}');
          logic.backTime = currentTime;
          if (time < 3 * 1000) {
            return Future.value(true);
          }
          EasyLoading.showToast('再按一次退出应用');
          return Future.value(false);
        },
        child: Obx(() {
          return AnnotatedRegion(
              value: logic.tabValue.value, child: logic.webWidget.value);
        }),
      ),
    );
  }
}
