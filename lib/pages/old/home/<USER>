import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:lima/global.dart';
import 'package:lima/pages/old/splash/splash_view.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/mqtt.dart';
import 'package:lima/utils/protocol_dialog.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../common/bridge_controller.dart';
import '../../../components/q_alert.dart';
import '../../../utils/q_log.dart';
import '../web/web_page.dart';

class HomeLogic extends GetxController {
  bool fistComeIn = true;

  /// 返回时间
  int backTime = 0;


  int tapCount = 0;
  int tapTime = 0;
  bool _dialogOpen = false;

  int safeAreaTop = 0;
  int safeAreaBottom = 0;

  WebController? myWebController;

  Rx<Widget> webWidget = Rx(Container());

  Rx<SystemUiOverlayStyle> tabValue = Rx(SystemUiOverlayStyle.dark);

  ///网页是否加载完成
  RxBool loadFinished = false.obs;

  @override
  void onInit() async{
    super.onInit();
    MQTT().connect();
    safeAreaTop = MediaQuery.of(Get.context!).padding.top.ceil();
    safeAreaBottom = MediaQuery.of(Get.context!).padding.bottom.ceil();

    ///判断隐私政策
    // var r = await Global.getPrivateProtocolPermission();
    // if (!r) {
    //   protocolDialog(onAgreePress: () {
    //     Global.setPrivateProtocolPermission(true);
    //     Global.thirdSdkInit();

        // Get.back();
        // if (fistComeIn) {
        //   Get.to(() => SplashPage());
        // }
        ///检查是否需要更新广告
        // _updateAdv();
    //   });
    // } else {
      // if (fistComeIn) {
      //   Get.to(() => SplashPage());
      // }
      ///检查是否需要更新广告
      // _updateAdv();
    // }

    // 初始化webWidget
    await initWidget();
  }

  Future initWidget() async{
    var token = await Global.get_lima_token();
    var phone  = '13676686680';
    var map = <Map>[];

    if (token != null && token.isNotEmpty) {
      QLog('HomeLogic: 获取到lima_token用于WebView注入');
      map.add({'key': 'token', 'value': token});
    } else {
      QLog('HomeLogic: 未获取到有效的lima_token');
    }
    if (phone != null) {
      map.add({'key': 'phone', 'value': phone});
    }
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    int buildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;
    var buildVersion = 'V${packageInfo.version}.$buildNumber';
    map.add({'key': 'versionCode', 'value': buildNumber});
    map.add({'key': 'buildVersion', 'value': buildVersion});

    bool installed = await JsBridgeController.instance.wxInstalled();
    map.add({'key': 'wxInstalled', 'value': installed});
    map.add({'key': 'aliPayInstalled', 'value': await JsBridgeController.instance.aliPayInstalled()});

    webWidget.value = WebPage(
      homeUrl: Config.BASE_WEBSITE,
      onWebCreated: (c) {
        myWebController = c;
      },
      cookies: map,
      onLoadFinished: (u) {
        // QLog('加载完成: $u');
        if(u!=null){
          Uri u1 = Uri.parse(u);
          Uri u2 = Uri.parse(Config.BASE_WEBSITE);
          QLog('加载完成对比: u1: ${u1.host} ,u2: ${u2.host} , 对比: ${u1.host == u2.host}');
          if (u1.host == u2.host) {
            if (u1.path == u2.path || u1.path == "/" || u1.path == "") {
              loadFinished.value = true;
              // 注入CSS隐藏.adm-tab-bar
              _injectHideTabBarCSS();
            }
          }
        }
      },
    );
  }

  void refreshUrl(String url) {
    loadFinished.value = false;
    myWebController?.loadUrl(url);
  }

  /// 刷新WebView的token（登录成功后调用）
  Future<void> refreshToken() async {
    QLog('HomeLogic: 开始刷新WebView token');
    await initWidget();
    QLog('HomeLogic: WebView token刷新完成');
  }

  /// 注入CSS隐藏.adm-tab-bar
  void _injectHideTabBarCSS() {
    if (myWebController != null) {
      final cssCode = '''
        var style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = '.adm-tab-bar { display: none !important; }';
        document.head.appendChild(style);
      ''';
      myWebController!.runJavaScript(cssCode);
      QLog('已注入CSS隐藏.adm-tab-bar');
    }
  }



  /// 处理导航请求（由MainTabLogic直接调用）
  void handleNavigation(Map<String, dynamic> navigationData) {
    QLog('车辆页面: 接收到导航请求: $navigationData');
    _notifyH5Navigation(navigationData);
  }

  /// 使用_notifyH5Navigation方法进行H5页面跳转
  /// 这是TabBar页面中车辆页面的核心导航方法
  void _notifyH5Navigation(Map<String, dynamic> navigationData) {
    QLog('H5页面管理器: 执行H5导航跳转: $navigationData');

    // 对于HomeLogic的WebView，我们需要使用JavaScript来进行导航
    if (myWebController != null) {
      String targetPage = navigationData['targetPage'] ?? '/';
      int tabIndex = navigationData['tabIndex'] ?? 0;

      // 如果是车辆页面的导航
      if (tabIndex == 1) {
        QLog('车辆页面: 执行车辆页面导航到: $targetPage');

        // 使用JavaScript通知H5页面进行导航
        final jsCode = '''
          if (window.appNavigate) {
            window.appNavigate('$targetPage');
          } else if (window.jsBridgeHelper && window.jsBridgeHelper.sendMessage) {
            window.jsBridgeHelper.sendMessage('receiveMessage', {
              action: 'navigate',
              targetPage: '$targetPage',
              tabIndex: $tabIndex
            });
          } else {
            console.log('车辆页面导航: 未找到导航方法，targetPage: $targetPage');
          }
        ''';

        myWebController!.runJavaScript(jsCode);
        QLog('车辆页面: 已发送导航JavaScript指令');
      }
    } else {
      QLog('车辆页面: WebController为空，无法执行导航');
    }
  }

  @override
  void onReady() async{
    super.onReady();
  }

  void changeWebDialog() {
    if(_dialogOpen) {
      return;
    }
    _dialogOpen = true;
    Get.dialog(
      QInputAlert(
        title: "修改网址",
        hintText: "请输入网址",
        text: Config.BASE_WEBSITE.isEmpty ? 'http://' : Config.BASE_WEBSITE,
        onConfim: (s) async {
          _dialogOpen = false;
          if (s.isNotEmpty && s != Config.BASE_WEBSITE) {
            refreshUrl(s);
          }
          await Global.updateWebSite(url: s);
        },
        onCancel: () {
          _dialogOpen = false;
        },
        tapDismiss: false,
      ),
      barrierDismissible: false,
    );

  }

  void checkTapTime(){
    var time = DateTime.now().millisecondsSinceEpoch;
    if(tapTime!=0 && time-tapTime>=10000){
      changeWebDialog();
      tapTime = 0;
    }
  }

  void _updateAdv() async{
    var res = await API.getAdv();
    if(res.code =='8001'){
      var data = res.data;
      if(data!=null && data is Map){
        var imgUrl = data['advetUrl'];
        var videoUrl = data['videoUrl'];
        if(imgUrl!=null){
          API.downloadFile(path: '${Config.BASE_IMG_URL}$imgUrl');
        }else{
          API.clearCacheAdv(1);
        }
        if(videoUrl!=null){
          API.downloadFile(path: '${Config.BASE_IMG_URL}$videoUrl');
        }else{
          API.clearCacheAdv(2);
        }
      }else{
        API.clearCacheAdv(0);
      }
    }
  }

  @override
  void onClose() {
    MQTT().close();
    super.onClose();
  }
}
