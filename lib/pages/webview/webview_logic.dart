import 'package:get/get.dart';

class WebviewLogic extends GetxController {
  bool hasNavBar = false;
  String url = '';
  String title = '';

  @override
  void onInit() {
    super.onInit();
    var arguments = Get.arguments;
    if (arguments != null) {
      hasNavBar = arguments['navBar'] ?? false;
      url = arguments['url'] ?? '';
      title = arguments['title'] ?? '';
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
