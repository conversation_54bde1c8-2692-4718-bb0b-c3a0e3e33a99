import 'package:flutter/material.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'package:get/get.dart';
import 'package:lima/pages/old/web/web_page.dart';
import 'package:lima/services/carrier_login_service.dart';

import '../../utils/q_log.dart';

class WebviewPage extends StatefulWidget {
  WebviewPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<WebviewPage> {
  bool? hasNavBar = false;
  String url = '';
  String? title = '';
  bool fromAliAuth = false; // 是否来自AliAuth
  WebController? myWebController;

  @override
  void initState() {
    super.initState();
    var arguments = Get.arguments;
    QLog('🔗 WebView页面初始化，接收到的参数: $arguments');
    if (arguments != null) {
      hasNavBar = arguments['navBar'] ?? false;
      url = arguments['url'] ?? '';
      title = arguments['title'] ?? '';
      fromAliAuth = arguments['AliAuth'] ?? false; // 检查AliAuth参数
      QLog('🔗 当前网址: $url');
      QLog('🔗 页面标题: $title');
      QLog('🔗 是否来自AliAuth: $fromAliAuth');
      QLog('🔗 AliAuth参数原始值: ${arguments['AliAuth']}');
    } else {
      QLog('🔗 ❌ 没有接收到任何参数');
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget webviewWidget;
    if (hasNavBar == true) {
      webviewWidget = Scaffold(
        body: SafeArea(child: WebWidget()),
      );
    } else {
      webviewWidget = BaseScaffold(title: title, body: WebWidget());
    }

    // 返回处理，如果参数有AliAuth则设置全局变量
    return PopScope(
      canPop: false, // 阻止默认返回，手动处理所有返回
      onPopInvokedWithResult: (didPop, result) {
        QLog('🔗 WebView PopScope触发: didPop=$didPop, fromAliAuth=$fromAliAuth');
        if (didPop) {
          // 手动处理返回
          if (fromAliAuth) {
            QLog('🔗 来自AliAuth，设置全局变量标记需要重新显示AliAuth');
            CarrierLoginService.setNeedShowAliAuth(true);
          } else {
            QLog('🔗 正常返回，不设置标记');
          }
          // Get.back(); // 简单返回，不传递参数
        }
      },
      child: webviewWidget,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget WebWidget() {
    return Container(
      child: WebPage(
          homeUrl: url,
          onWebCreated: (c) {
            myWebController = c;
          }),
    );
  }
}
