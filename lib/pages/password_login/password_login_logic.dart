import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/common/config.dart';
import 'package:lima/pages/login/login_logic.dart';
import 'package:lima/routers/app_router.dart';

class PasswordLoginLogic extends GetxController {
  // 控制器
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();

  // 响应式变量
  final isLoading = false.obs;
  final canLogin = false.obs;
  final isPasswordVisible = false.obs;
  final isAgreementAccepted = false.obs; // 用户协议同意状态

  // 设备clientId
  String clientId = '';

  @override
  void onInit() {
    super.onInit();
    _initializeClientId();
    validateForm();
  }

  @override
  void onClose() {
    phoneController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  /// 初始化设备clientId
  Future<void> _initializeClientId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        clientId = androidInfo.id; // Android设备ID
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        clientId = iosInfo.identifierForVendor ?? ''; // iOS设备ID
      }

      QLog('设备clientId: $clientId');
    } catch (e) {
      QLog('获取设备clientId失败: $e');
      // 如果获取失败，生成一个基于时间戳的ID作为备用
      clientId = 'client_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// 验证表单
  void validateForm() {
    final phone = phoneController.text.trim();
    final password = passwordController.text.trim();

    // 验证手机号格式
    final phoneValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);

    // 验证密码
    final passwordValid = password.length >= 6;

    // 更新登录按钮状态（只要手机号和密码有效就可以点击，不需要预先同意协议）
    canLogin.value = phoneValid && passwordValid && !isLoading.value;
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// 切换用户协议同意状态
  void toggleAgreement() {
    if (!isAgreementAccepted.value) {
      // 如果当前未同意，显示确认弹窗
      showAgreementConfirmDialog();
    } else {
      // 如果已同意，直接取消同意
      isAgreementAccepted.value = false;
      validateForm();
    }
  }

  /// 显示用户协议确认弹窗
  void showAgreementConfirmDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('用户协议确认'),
        content: const Text(
          '请仔细阅读并确认您同意《用户协议》和《隐私政策》的全部内容。\n\n点击"确认"表示您已阅读并同意相关条款。',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // 关闭弹窗，不同意协议
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back(); // 关闭弹窗
              isAgreementAccepted.value = true; // 同意协议
              validateForm(); // 重新验证表单
            },
            child: const Text(
              '确认',
              style: TextStyle(color: Color(0xFFC70E2D)),
            ),
          ),
        ],
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }

  /// 显示用户协议
  void showUserAgreement() {
    Get.to(() => WebviewPage(), arguments: {
      'url': '${Config.BASE_WEBSITE}/me/about/agreement?nav=false',
      'navBar': false,
      'title': '用户协议',
    });
  }

  /// 显示隐私政策
  void showPrivacyPolicy() {
    Get.to(() => WebviewPage(), arguments: {
      'url': Config.USER_PROTOCOL,
      'navBar': false,
      'title': '隐私政策',
    });
  }

  /// 忘记密码
  Future<void> forgotPassword() async {
    try {
      QLog('跳转到忘记密码页面');

      // 跳转到忘记密码页面
      final result = await Get.toNamed('/forgot_password');

      if (result != null && result['success'] == true) {
        QLog('密码重置成功: ${result['message']}');
        EasyLoading.showSuccess(result['message'] ?? '密码重置成功');

        // 如果返回了手机号，可以预填到登录表单中
        if (result['mobile'] != null) {
          phoneController.text = result['mobile'];
          validateForm();
        }
      }
    } catch (e) {
      QLog('忘记密码异常: $e');
      EasyLoading.showError('跳转失败');
    }
  }

  /// 密码登录
  Future<void> login() async {
    if (!canLogin.value) return;

    final phone = phoneController.text.trim();
    final password = passwordController.text.trim();

    // 如果没有同意隐私协议，显示确认弹窗
    if (!isAgreementAccepted.value) {
      _showPrivacyAgreementDialog(phone, password);
      return;
    }

    // 已同意协议，直接执行登录
    _doLogin(phone, password);
  }

  /// 显示隐私协议确认弹窗
  void _showPrivacyAgreementDialog(String phone, String password) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          '用户协议及隐私保护政策',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '我已阅读并同意',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: ' 和 ',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: '《隐私政策保护协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            children: [
              // 不同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(right: 8.w),
                  child: OutlinedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      QLog('用户不同意隐私协议');
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '不同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ),
              // 同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(left: 8.w),
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      // 自动勾选隐私协议
                      isAgreementAccepted.value = true;
                      QLog('用户同意隐私协议，自动勾选并执行登录');
                      // 执行登录
                      _doLogin(phone, password);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A4A4A),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
        contentPadding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
        titlePadding: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 16.h),
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }

  /// 执行密码登录
  Future<void> _doLogin(String phone, String password) async {
    try {
      isLoading.value = true;
      validateForm();

      EasyLoading.show(status: '登录中...');

      // 确定appType
      String appType = '1'; // 默认Android
      if (GetPlatform.isIOS) {
        appType = '2'; // iOS
      }

      QLog('密码登录 - 手机号: $phone, appType: $appType, clientId: $clientId');

      // 调用密码登录API
      final result = await API.secretLoginApi(
        mobile: phone,
        password: password,
        appType: appType,
        clientId: clientId,
      );

      QLog('密码登录API响应: ${result.toJson()}');

      if (result.code == '200' || result.code == 200) {
        QLog('密码登录成功');
        EasyLoading.showSuccess('登录成功');

        // 返回登录成功结果
        // Get.back(result: {
        //   'success': true,
        //   'message': '登录成功',
        //   'directLogin': true,
        //   'userData': result.data,
        // });
        _saveUserInfo(result.data);
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3});
      } else {
        QLog('密码登录失败: ${result.msg}');
        EasyLoading.showError(result.msg ?? '登录失败');
      }
    } catch (e) {
      QLog('密码登录异常: $e');
      EasyLoading.showError('登录失败，请重试');
    } finally {
      isLoading.value = false;
      validateForm();
    }
  }

    /// 保存用户信息
  Future<void> _saveUserInfo(Map<String, dynamic> userData) async {
    try {
      // 使用LoginLogic中的saveUserInfo方法
      final loginLogic = LoginLogic();
      await loginLogic.saveUserInfo(userData);
      QLog('✅ 使用LoginLogic保存用户信息成功');
    } catch (e) {
      QLog('保存用户信息异常: $e');
      rethrow;
    }
  }

  /// 微信登录
  Future<void> wechatLogin() async {
    // 检查隐私协议同意状态
    if (!isAgreementAccepted.value) {
      _showThirdPartyLoginAgreementDialog('微信登录', () => _doWechatLogin());
      return;
    }

    // 已同意协议，直接执行微信登录
    _doWechatLogin();
  }

  /// 执行微信登录
  Future<void> _doWechatLogin() async {
    try {
      QLog('开始微信登录');

      // 直接使用LoginLogic的微信登录方法
      final loginLogic = LoginLogic();
      await loginLogic.wechatLogin();
    } catch (e) {
      QLog('微信登录异常: $e');
      EasyLoading.showError('微信登录失败');
    }
  }

  /// Apple ID登录
  Future<void> appleLogin() async {
    if (!GetPlatform.isIOS) {
      QLog('Apple登录仅支持iOS平台');
      EasyLoading.showInfo('Apple登录仅支持iOS平台');
      return;
    }

    // 检查隐私协议同意状态
    if (!isAgreementAccepted.value) {
      _showThirdPartyLoginAgreementDialog('Apple登录', () => _doAppleLogin());
      return;
    }

    // 已同意协议，直接执行Apple登录
    _doAppleLogin();
  }

  /// 执行Apple登录
  Future<void> _doAppleLogin() async {
    try {
      QLog('开始Apple ID登录');

      // 直接使用LoginLogic的Apple登录方法
      final loginLogic = LoginLogic();
      await loginLogic.appleLogin();
    } catch (e) {
      QLog('Apple登录异常: $e');
      EasyLoading.showError('Apple登录失败');
    }
  }

  /// 显示第三方登录隐私协议确认弹窗
  void _showThirdPartyLoginAgreementDialog(String loginType, VoidCallback onConfirm) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          '用户协议及隐私保护政策',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '使用$loginType需要同意以下协议',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: ' 和 ',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: '《隐私政策保护协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            children: [
              // 不同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(right: 8.w),
                  child: OutlinedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      QLog('用户不同意$loginType隐私协议');
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '不同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ),
              // 同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(left: 8.w),
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      // 自动勾选隐私协议
                      isAgreementAccepted.value = true;
                      QLog('用户同意$loginType隐私协议，自动勾选并启动登录');
                      // 执行第三方登录
                      onConfirm();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A4A4A),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
        contentPadding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
        titlePadding: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 16.h),
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }
}
