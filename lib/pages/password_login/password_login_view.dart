import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'package:lima/res/colors.dart';

import 'password_login_logic.dart';

class PasswordLoginPage extends StatelessWidget {
  const PasswordLoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(PasswordLoginLogic());

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login-bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: BaseScaffold(
        backgroundColor: Colors.transparent,
        appBarBackgroundColor: Colors.transparent,
        // title: '手机号密码登录',
        body: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 
                         MediaQuery.of(context).padding.top - 
                         kToolbarHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // 主要内容区域
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),

                          // Logo
                          _buildLogo(),

                          SizedBox(height: 24.h),

                          // 标题
                          _buildTitle(),

                          SizedBox(height: 40.h),

                          // 手机号输入框
                          _buildPhoneInput(logic),

                          SizedBox(height: 20.h),

                          // 密码输入框
                          _buildPasswordInput(logic),

                          SizedBox(height: 12.h),

                          // 忘记密码链接
                          _buildForgotPassword(logic),

                          SizedBox(height: 32.h),

                          // 登录按钮
                          _buildLoginButton(logic),

                          SizedBox(height: 24.h),

                          // 隐私协议
                          _buildPrivacyAgreement(logic),

                          // 弹性空间，将第三方登录推到底部
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),

                  // 底部第三方登录
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Column(
                      children: [
                        SizedBox(height: 20.h),
                        _buildThirdPartyLogin(logic),
                        SizedBox(height: 60.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      child: Image.asset(
        'assets/images/logo.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Text(
      '密码登录',
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput(PasswordLoginLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // +86前缀
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '+86',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20.h,
            color: const Color(0xFFE5E5E5),
          ),
          // 手机号输入
          Expanded(
            child: TextField(
              controller: logic.phoneController,
              keyboardType: TextInputType.phone,
              maxLength: 11,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                hintText: '输入手机号',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),
              ),
              onChanged: (value) => logic.validateForm(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordInput(PasswordLoginLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Obx(() => TextField(
        controller: logic.passwordController,
        obscureText: !logic.isPasswordVisible.value,
        style: TextStyle(
          fontSize: 16.sp,
          color: const Color(0xFF333333),
        ),
        decoration: InputDecoration(
          hintText: '输入密码',
          hintStyle: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFFCCCCCC),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 16.h,
          ),
          suffixIcon: IconButton(
            icon: Icon(
              logic.isPasswordVisible.value ? Icons.visibility : Icons.visibility_off,
              color: const Color(0xFFCCCCCC),
              size: 20.sp,
            ),
            onPressed: logic.togglePasswordVisibility,
          ),
        ),
        onChanged: (value) => logic.validateForm(),
      )),
    );
  }

  /// 构建忘记密码链接
  Widget _buildForgotPassword(PasswordLoginLogic logic) {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: logic.forgotPassword,
        child: Text(
          '忘记密码',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ),
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton(PasswordLoginLogic logic) {
    return Obx(() => SizedBox(
      width: double.infinity,
      height: 56.h,
      child: ElevatedButton(
        onPressed: logic.canLogin.value ? logic.login : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4A4A4A),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28.r),
          ),
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: logic.isLoading.value
            ? SizedBox(
                width: 20.w,
                height: 20.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                '登录',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    ));
  }

  /// 构建隐私协议
  Widget _buildPrivacyAgreement(PasswordLoginLogic logic) {
    return Obx(() => Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: logic.toggleAgreement,
            child: Container(
              width: 18.w,
              height: 18.w,
              decoration: BoxDecoration(
                color: logic.isAgreementAccepted.value
                    ? AppColors.primary
                    : Colors.transparent,
                border: Border.all(
                  color: logic.isAgreementAccepted.value
                      ? AppColors.primary
                      : const Color(0xFFCCCCCC),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: logic.isAgreementAccepted.value
                  ? Icon(
                      Icons.check,
                      size: 14.sp,
                      color: Colors.white,
                    )
                  : null,
            ),
          ),
          SizedBox(width: 8.w),
          Text.rich(
            TextSpan(
              text: '我已阅读并同意 ',
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF666666),
              ),
              children: [
                WidgetSpan(
                  child: GestureDetector(
                    onTap: logic.showUserAgreement,
                    child: Text(
                      '《用户协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.primary,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
                TextSpan(
                  text: ' 和 ',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: logic.showPrivacyPolicy,
                    child: Text(
                      '《隐私政策》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.primary,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ));
  }

  /// 构建第三方登录
  Widget _buildThirdPartyLogin(PasswordLoginLogic logic) {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            Expanded(child: Divider(color: Colors.grey[400])),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                '第三方平台登录',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[700],
                ),
              ),
            ),
            Expanded(child: Divider(color: Colors.grey[400])),
          ],
        ),

        SizedBox(height: 24.h),

        // 第三方登录图标
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 微信登录
            GestureDetector(
              onTap: logic.wechatLogin,
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF07C160),
                  borderRadius: BorderRadius.circular(25.r),
                ),
                child: Icon(
                  Icons.wechat,
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
            ),

            if (GetPlatform.isIOS) SizedBox(width: 40.w),

            // Apple ID登录 (仅iOS显示)
            if (GetPlatform.isIOS)
              GestureDetector(
                onTap: logic.appleLogin,
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                  child: Icon(
                    Icons.apple,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}