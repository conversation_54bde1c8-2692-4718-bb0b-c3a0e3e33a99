import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/services/third_party_login_service.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/pages/login/login_logic.dart';
import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/common/config.dart';
import 'package:lima/res/colors.dart';

/// 手机号输入页面控制器
class PhoneInputLogic extends GetxController {
  final TextEditingController phoneController = TextEditingController();
  final FocusNode phoneFocusNode = FocusNode();
  
  // 响应式变量
  final RxString phoneNumber = ''.obs;
  final RxString errorText = ''.obs;
  final RxBool isPhoneValid = false.obs;
  final RxBool isPrivacyAgreed = false.obs; // 隐私协议同意状态
  final RxBool canSendCode = true.obs; // 是否可以发送验证码
  final RxInt countdown = 0.obs; // 倒计时
  
  // 从路由参数获取的数据（响应式变量）
  final RxString bindKey = ''.obs;
  final RxString loginType = ''.obs;
  final Rx<Map<String, dynamic>?> originalData = Rx<Map<String, dynamic>?>(null);

  @override
  void onInit() {
    super.onInit();
    _initializeData();
    
    // 监听手机号输入
    phoneController.addListener(() {
      onPhoneChanged(phoneController.text);
    });
  }

  @override
  void onClose() {
    phoneController.dispose();
    phoneFocusNode.dispose();
    super.onClose();
  }

  /// 初始化页面数据
  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      bindKey.value = arguments['bindKey'] ?? '';
      loginType.value = arguments['loginType'] ?? '';
      originalData.value = arguments['originalData'];
      QLog('手机号输入页面初始化 - bindKey: ${bindKey.value}, loginType: ${loginType.value}');
    }
  }

  /// 动态获取页面标题
  String getPageTitle() {
    if (bindKey.value.isNotEmpty) {
      return '绑定手机号'; // 有bindKey时显示绑定手机号
    } else {
      return '手机号登录'; // 没有bindKey时显示手机号登录
    }
  }

  /// 动态获取页面主标题
  String getMainTitle() {
    if (bindKey.value.isNotEmpty) {
      return '绑定手机号'; // 有bindKey时显示绑定手机号
    } else {
      return '手机号登录'; // 没有bindKey时显示手机号登录
    }
  }

  /// 动态获取页面描述
  String getPageDescription() {
    if (bindKey.value.isNotEmpty) {
      return '为了完成登录，请输入您的手机号\n我们将发送验证码到您的手机';
    } else {
      return '请输入您的手机号码\n我们将发送验证码进行身份验证';
    }
  }

  /// 手机号输入变化处理
  void onPhoneChanged(String value) {
    phoneNumber.value = value;

    // 清除错误信息
    if (errorText.value.isNotEmpty) {
      errorText.value = '';
    }

    // 验证手机号格式
    final isValid = _validatePhoneNumber(value);
    isPhoneValid.value = isValid;

    QLog('手机号输入变化: $value, 长度: ${value.length}, 有效性: $isValid');
  }

  /// 验证手机号格式
  bool _validatePhoneNumber(String phone) {
    if (phone.length != 11) return false;
    if (!phone.startsWith(RegExp(r'^1[3-9]'))) return false;
    if (!RegExp(r'^\d+$').hasMatch(phone)) return false;
    return true;
  }

  /// 确认手机号
  void confirmPhone() {
    final phone = phoneController.text.trim();
    
    if (!_validatePhoneNumber(phone)) {
      errorText.value = '请输入正确的11位手机号';
      return;
    }
    
    QLog('用户确认手机号: $phone');
    
    // 跳转到验证码页面
    _navigateToVerification(phone);
  }

  /// 跳转到验证码页面
  void _navigateToVerification(String phoneNumber) async {
    QLog('跳转到验证码页面，手机号: $phoneNumber');
    QLog('bindKey: ${bindKey.value}, loginType: ${loginType.value}');

    final result = await Get.toNamed(AppRoute.verification, arguments: {
      'bindKey': bindKey.value, // 使用响应式变量的值
      'phoneNumber': phoneNumber,
      'loginType': loginType.value,
      'originalData': originalData.value,
      'clientId': await _getClientId(), // 传递clientId
    });

    QLog('验证码页面返回结果: $result');

    // 处理验证结果 - 由于验证码页面已经直接跳转到Demo页面，这里不需要额外处理
    QLog('验证码页面已处理跳转，手机号输入页面任务完成');
  }

  /// 获取clientId
  Future<String?> _getClientId() async {
    try {
      // 从路由参数中获取clientId
      final arguments = Get.arguments as Map<String, dynamic>?;
      if (arguments != null && arguments['clientId'] != null) {
        return arguments['clientId'];
      }

      // 如果没有传递clientId，可以从其他地方获取
      // 这里可以根据实际情况调整
      return null;
    } catch (e) {
      QLog('获取clientId异常: $e');
      return null;
    }
  }

  /// 格式化手机号显示（用于显示）
  String get formattedPhoneNumber {
    final phone = phoneNumber.value;
    if (phone.length <= 3) return phone;
    if (phone.length <= 7) {
      return '${phone.substring(0, 3)} ${phone.substring(3)}';
    }
    return '${phone.substring(0, 3)} ${phone.substring(3, 7)} ${phone.substring(7)}';
  }

  /// 获取手机号验证提示
  String get phoneValidationHint {
    final phone = phoneNumber.value;
    if (phone.isEmpty) return '';
    if (phone.length < 11) return '请输入完整的11位手机号';
    if (!phone.startsWith(RegExp(r'^1[3-9]'))) return '请输入正确的手机号格式';
    return '';
  }

  /// 忘记密码
  Future<void> forgotPassword() async {
    try {
      QLog('跳转到忘记密码页面');

      // 跳转到忘记密码页面
      final result = await Get.toNamed('/forgot_password');

      if (result != null && result['success'] == true) {
        QLog('密码重置成功: ${result['message']}');

        // 如果返回了手机号，可以预填到登录表单中
        if (result['mobile'] != null) {
          phoneController.text = result['mobile'];
          onPhoneChanged(result['mobile']);
        }
      }
    } catch (e) {
      QLog('忘记密码异常: $e');
    }
  }

  /// 密码登录
  Future<void> passwordLogin() async {
    try {
      QLog('跳转到密码登录页面');

      // 跳转到密码登录页面
      final result = await Get.toNamed('/password_login');

      if (result != null && result['success'] == true) {
        QLog('密码登录成功，返回登录结果');
        // 返回登录成功结果给上级页面
        Get.back(result: result);
      }
    } catch (e) {
      QLog('密码登录异常: $e');
    }
  }

  /// 微信登录
  Future<void> wechatLogin() async {
    // 如果当前页面已经是绑定模式（有bindKey），则不允许再次微信登录
    if (bindKey.value.isNotEmpty) {
      QLog('当前已在绑定模式，不允许重复微信登录');
      EasyLoading.showInfo('请完成当前手机号绑定流程');
      return;
    }

    // 检查当前页面的隐私协议同意状态
    if (!isPrivacyAgreed.value) {
      _showThirdPartyLoginAgreementDialog('微信登录', () => _doWechatLogin());
      return;
    }

    // 已同意协议，直接执行微信登录
    _doWechatLogin();
  }

  /// 执行微信登录
  Future<void> _doWechatLogin() async {
    try {
      QLog('手机号输入页面 - 开始微信登录');
      
      // 创建LoginLogic实例用于保存用户信息
      final loginLogic = LoginLogic();

      EasyLoading.show(status: '微信登录中...');

      // 调用第三方登录服务进行微信登录
      final thirdPartyService = ThirdPartyLoginService.instance;
      final loginResult = await thirdPartyService.wechatLogin();
      
      EasyLoading.dismiss();
      
      if (loginResult.success) {
        final userData = loginResult.userData;
        QLog('手机号输入页面 - 微信登录成功，用户数据: $userData');

        // 检查是否需要绑定手机号
        if (userData != null && userData['bindKey'] != null && userData['bindKey'].toString().isNotEmpty) {
          QLog('手机号输入页面 - 检测到bindKey: ${userData['bindKey']}，跳转到绑定手机号页面');
         
         // 跳转到绑定手机号页面
         final bindResult = await Get.toNamed(AppRoute.bindPhone, arguments: {
           'bindKey': userData['bindKey'].toString(),
           'loginType': 'wechat',
           'originalData': userData,
         });
         
         // 处理绑定结果
         if (bindResult != null) {
           Get.back(result: bindResult);
         }
         
         EasyLoading.showSuccess('微信授权成功，请绑定手机号');
       } else {
         QLog('手机号输入页面 - 微信登录完成，无需手机号绑定');
         await loginLogic.saveUserInfo(userData);
         
         // 返回登录成功结果
         Get.back(result: {
           'success': true,
           'directLogin': true,
           'userData': userData,
         });
       }
     } else {
       QLog('手机号输入页面 - 微信登录失败: ${loginResult.errorMessage}');
       EasyLoading.showError(loginResult.errorMessage ?? '微信登录失败');
     }



    } catch (e) {
      EasyLoading.dismiss();
      QLog('手机号输入页面 - 微信登录异常: $e');
      EasyLoading.showError('微信登录失败，请重试');
    }
  }

  /// Apple ID登录
  Future<void> appleLogin() async {
    if (!GetPlatform.isIOS) {
      QLog('Apple登录仅支持iOS平台');
      EasyLoading.showInfo('Apple登录仅支持iOS平台');
      return;
    }

    // 如果当前页面已经是绑定模式（有bindKey），则不允许再次Apple登录
    if (bindKey.value.isNotEmpty) {
      QLog('当前已在绑定模式，不允许重复Apple登录');
      EasyLoading.showInfo('请完成当前手机号绑定流程');
      return;
    }

    // 检查当前页面的隐私协议同意状态
    if (!isPrivacyAgreed.value) {
      _showThirdPartyLoginAgreementDialog('Apple登录', () => _doAppleLogin());
      return;
    }

    // 已同意协议，直接执行Apple登录
    _doAppleLogin();
  }

  /// 执行Apple登录
  Future<void> _doAppleLogin() async {
    try {
      QLog('手机号输入页面 - 开始Apple ID登录');
      
      // 创建LoginLogic实例用于保存用户信息
      final loginLogic = LoginLogic();

      EasyLoading.show(status: 'Apple登录中...');

      // 调用第三方登录服务进行Apple登录
      final thirdPartyService = ThirdPartyLoginService.instance;
      final loginResult = await thirdPartyService.appleLogin();
      
      EasyLoading.dismiss();
      
      if (loginResult.success) {
        final userData = loginResult.userData;
        QLog('手机号输入页面 - Apple登录成功，用户数据: $userData');

        // 检查是否需要绑定手机号
        if (userData != null && userData['bindKey'] != null && userData['bindKey'].toString().isNotEmpty) {
          QLog('手机号输入页面 - 检测到bindKey: ${userData['bindKey']}，跳转到绑定手机号页面');
         
         // 跳转到绑定手机号页面
         final bindResult = await Get.toNamed(AppRoute.bindPhone, arguments: {
           'bindKey': userData['bindKey'].toString(),
           'loginType': 'apple',
           'originalData': userData,
         });
         
         // 处理绑定结果
         if (bindResult != null) {
           Get.back(result: bindResult);
         }
         
         EasyLoading.showSuccess('Apple授权成功，请绑定手机号');
       } else {
         QLog('手机号输入页面 - Apple登录完成，无需手机号绑定');
         await loginLogic.saveUserInfo(userData);
         
         // 返回登录成功结果
         Get.back(result: {
           'success': true,
           'directLogin': true,
           'userData': userData,
         });
       }
     } else {
       QLog('手机号输入页面 - Apple登录失败: ${loginResult.errorMessage}');
       EasyLoading.showError(loginResult.errorMessage ?? 'Apple登录失败');
     }



    } catch (e) {
      EasyLoading.dismiss();
      QLog('手机号输入页面 - Apple登录异常: $e');
      EasyLoading.showError('Apple登录失败，请重试');
    }
  }

  /// 显示用户协议
  void showUserAgreement() {
    Get.to(() => WebviewPage(), arguments: {
      'url': '${Config.BASE_WEBSITE}/me/about/agreement?nav=false',
      'navBar': false,
      'title': '用户协议',
    });
  }

  /// 显示隐私政策
  void showPrivacyPolicy() {
    Get.to(() => WebviewPage(), arguments: {
      'url': Config.USER_PROTOCOL,
      'navBar': false,
      'title': '隐私政策',
    });
  }

  /// 切换隐私协议同意状态
  void togglePrivacyAgreement() {
    isPrivacyAgreed.value = !isPrivacyAgreed.value;
    QLog('隐私协议同意状态: ${isPrivacyAgreed.value}');
  }

  /// 发送验证码
  Future<void> sendVerificationCode() async {
    final phone = phoneController.text.trim();

    if (!_validatePhoneNumber(phone)) {
      errorText.value = '请输入正确的11位手机号';
      return;
    }

    // 如果没有同意隐私协议，显示确认弹窗
    if (!isPrivacyAgreed.value) {
      _showPrivacyAgreementDialog(phone);
      return;
    }

    // 已同意协议，直接发送验证码
    _doSendVerificationCode(phone);
  }

  /// 显示隐私协议确认弹窗
  void _showPrivacyAgreementDialog(String phone) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          '用户协议及隐私保护政策',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '我已阅读并同意',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: ' 和 ',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: '《隐私政策保护协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            children: [
              // 不同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(right: 8.w),
                  child: OutlinedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      QLog('用户不同意隐私协议');
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '不同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ),
              // 同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(left: 8.w),
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      // 自动勾选隐私协议
                      isPrivacyAgreed.value = true;
                      QLog('用户同意隐私协议，自动勾选并发送验证码');
                      // 发送验证码
                      _doSendVerificationCode(phone);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A4A4A),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
        contentPadding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
        titlePadding: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 16.h),
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }

  /// 执行发送验证码
  Future<void> _doSendVerificationCode(String phone) async {
    try {
      QLog('发送验证码到: $phone');

      // TODO: 调用发送验证码API
      // await Api.sendVerificationCode(phone);

      QLog('验证码发送成功，跳转到验证码页面');

      // 跳转到验证码页面
      _navigateToVerification(phone);

    } catch (e) {
      QLog('发送验证码失败: $e');
      errorText.value = '发送验证码失败，请重试';
    }
  }

  /// 显示第三方登录隐私协议确认弹窗
  void _showThirdPartyLoginAgreementDialog(String loginType, VoidCallback onConfirm) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          '用户协议及隐私保护政策',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '使用$loginType需要同意以下协议',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: ' 和 ',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    TextSpan(
                      text: '《隐私政策保护协议》',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            children: [
              // 不同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(right: 8.w),
                  child: OutlinedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      QLog('用户不同意$loginType隐私协议');
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '不同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ),
              // 同意按钮
              Expanded(
                child: Container(
                  height: 36.h,
                  margin: EdgeInsets.only(left: 8.w),
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(); // 关闭弹窗
                      // 自动勾选隐私协议
                      isPrivacyAgreed.value = true;
                      QLog('用户同意$loginType隐私协议，自动勾选并启动登录');
                      // 执行第三方登录
                      onConfirm();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A4A4A),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      '同意',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
        contentPadding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
        titlePadding: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 16.h),
      ),
      barrierDismissible: false, // 不允许点击外部关闭
    );
  }

  /// 更新页面内容（标题和描述）
  void _updatePageContent() {
    // 触发页面重新构建，让页面根据新的bindKey状态更新UI
    update();
  }


}
