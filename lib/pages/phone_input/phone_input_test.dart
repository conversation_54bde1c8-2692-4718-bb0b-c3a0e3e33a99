import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/pages/phone_input/phone_input_view.dart';

/// 手机号输入页面测试
class PhoneInputTest {
  
  /// 测试路由跳转
  static Future<void> testRouteNavigation() async {
    try {
      QLog('=== 测试手机号输入页面路由跳转 ===');
      
      // 测试参数
      final testArguments = {
        'bindKey': 'test-bind-key-12345',
        'loginType': 'wechat',
        'originalData': {
          'userInfo': {
            'openid': 'test_openid',
            'nickname': '测试用户',
          }
        },
      };
      
      QLog('测试参数: $testArguments');
      QLog('路由名称: ${AppRoute.phoneInput}');
      
      // 尝试跳转
      final result = await Get.toNamed(AppRoute.phoneInput, arguments: testArguments);
      
      QLog('路由跳转结果: $result');
      
      if (result != null) {
        QLog('✅ 路由跳转成功');
      } else {
        QLog('⚠️ 路由跳转返回null（可能是用户取消）');
      }
      
    } catch (e, stackTrace) {
      QLog('❌ 路由跳转失败: $e');
      QLog('异常堆栈: $stackTrace');
    }
  }
  
  /// 测试页面创建
  static void testPageCreation() {
    try {
      QLog('=== 测试手机号输入页面创建 ===');

      // 尝试导入页面类
      QLog('尝试引用PhoneInputPage类...');

      // 这里只是验证类是否可以被引用，不实际创建实例
      final pageType = const PhoneInputPage().runtimeType;
      QLog('✅ PhoneInputPage类引用成功: $pageType');

    } catch (e, stackTrace) {
      QLog('❌ 页面类引用失败: $e');
      QLog('异常堆栈: $stackTrace');
    }
  }
  
  /// 验证路由配置
  static void validateRouteConfig() {
    QLog('=== 验证路由配置 ===');

    try {
      // 检查路由常量
      QLog('phoneInput路由: ${AppRoute.phoneInput}');
      QLog('verification路由: ${AppRoute.verification}');

      // 检查当前路由状态
      if (Get.currentRoute.isNotEmpty) {
        QLog('当前路由: ${Get.currentRoute}');
      }

      // 简单验证路由常量是否定义
      if (AppRoute.phoneInput.isNotEmpty) {
        QLog('✅ phoneInput路由常量已定义');
      } else {
        QLog('❌ phoneInput路由常量未定义');
      }

      if (AppRoute.verification.isNotEmpty) {
        QLog('✅ verification路由常量已定义');
      } else {
        QLog('❌ verification路由常量未定义');
      }

      QLog('✅ 路由配置验证完成');

    } catch (e) {
      QLog('❌ 验证路由配置失败: $e');
    }
  }
  
  /// 完整测试流程
  static Future<void> runFullTest() async {
    QLog('🚀 开始完整测试流程');

    // 1. 验证路由配置
    validateRouteConfig();

    // 2. 测试页面类引用
    try {
      testPageCreation();
      QLog('✅ 页面类引用测试通过');
    } catch (e) {
      QLog('❌ 页面类引用测试失败: $e');
    }

    // 3. 测试路由跳转
    await testRouteNavigation();

    QLog('🏁 完整测试流程结束');
  }
}

/// 使用示例：
/// 
/// ```dart
/// // 在需要测试的地方调用
/// await PhoneInputTest.runFullTest();
/// 
/// // 或者单独测试某个功能
/// await PhoneInputTest.testRouteNavigation();
/// PhoneInputTest.validateRouteConfig();
/// ```
