import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/custom_scaffold.dart';
import 'package:lima/res/colors.dart';
import 'phone_input_logic.dart';

/// 手机号输入页面
class PhoneInputPage extends StatelessWidget {
  const PhoneInputPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(PhoneInputLogic());

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login-bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: BaseScaffold(
        backgroundColor: Colors.transparent,
        appBarBackgroundColor: Colors.transparent,
        // title: '密码登录',
        actions: [
          // 只有在没有bindKey时才显示密码登录按钮
          if (logic.bindKey == null || logic.bindKey!.isEmpty)
            GestureDetector(
              onTap: () {
                // 跳转到密码登录页面
                Get.toNamed('/password_login');
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                margin: EdgeInsets.only(right: 16.w),
                child: Text(
                  '密码登录',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
        body: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  kToolbarHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // 主要内容区域
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),

                          // Logo
                          _buildLogo(),

                          SizedBox(height: 24.h),

                          // 标题
                          _buildTitle(logic),

                          SizedBox(height: 40.h),

                          // 手机号输入框
                          _buildPhoneInput(logic),

                          SizedBox(height: 32.h),

                          // 获取验证码按钮
                          _buildGetCodeButton(logic),

                          SizedBox(height: 24.h),

                          // 隐私协议
                          _buildPrivacyAgreement(logic),

                          // 弹性空间，将第三方登录推到底部
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),

                  // 底部第三方登录 - 只有在没有bindKey时才显示
                  Obx(() {
                    if (logic.bindKey.value.isNotEmpty) {
                      return SizedBox.shrink(); // 有bindKey时不显示第三方登录
                    }
                    return Container(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(height: 20.h),
                          _buildThirdPartyLogin(logic),
                          SizedBox(height: 60.h),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      child: Image.asset(
        'assets/images/logo.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle(PhoneInputLogic logic) {
    return Text(
      logic.getMainTitle(),
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput(PhoneInputLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // +86前缀
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '+86',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20.h,
            color: const Color(0xFFE5E5E5),
          ),
          // 手机号输入
          Expanded(
            child: TextField(
              controller: logic.phoneController,
              focusNode: logic.phoneFocusNode,
              keyboardType: TextInputType.phone,
              maxLength: 11,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                hintText: '输入手机号',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),
              ),
              onChanged: logic.onPhoneChanged,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建获取验证码按钮
  Widget _buildGetCodeButton(PhoneInputLogic logic) {
    return Obx(() => SizedBox(
          width: double.infinity,
          height: 56.h,
          child: ElevatedButton(
            onPressed:
                logic.isPhoneValid.value ? logic.sendVerificationCode : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A4A4A),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28.r),
              ),
              disabledBackgroundColor: Colors.grey[300],
              disabledForegroundColor: Colors.grey[600],
            ),
            child: Text(
              '获取验证码',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ));
  }

  /// 构建隐私协议
  Widget _buildPrivacyAgreement(PhoneInputLogic logic) {
    return Obx(() => Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: logic.togglePrivacyAgreement,
                child: Container(
                  width: 18.w,
                  height: 18.w,
                  decoration: BoxDecoration(
                    color: logic.isPrivacyAgreed.value
                        ? AppColors.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: logic.isPrivacyAgreed.value
                          ? AppColors.primary
                          : const Color(0xFFCCCCCC),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: logic.isPrivacyAgreed.value
                      ? Icon(
                          Icons.check,
                          size: 14.sp,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),
              SizedBox(width: 8.w),
              Text.rich(
                TextSpan(
                  text: '我已阅读并同意 ',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                  children: [
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: logic.showUserAgreement,
                        child: Text(
                          '《用户协议》',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                    TextSpan(
                      text: ' 和 ',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: logic.showPrivacyPolicy,
                        child: Text(
                          '《隐私政策》',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  /// 构建第三方登录
  Widget _buildThirdPartyLogin(PhoneInputLogic logic) {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            Expanded(child: Divider(color: Colors.grey[400])),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                '第三方平台登录',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[700],
                ),
              ),
            ),
            Expanded(child: Divider(color: Colors.grey[400])),
          ],
        ),

        SizedBox(height: 24.h),

        // 第三方登录图标
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 微信登录
            GestureDetector(
              onTap: logic.wechatLogin,
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF07C160),
                  borderRadius: BorderRadius.circular(25.r),
                ),
                child: Icon(
                  Icons.wechat,
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
            ),

            if (GetPlatform.isIOS) SizedBox(width: 40.w),
            // SizedBox(width: 40.w),

            // Apple ID登录 (仅iOS显示)
            if (GetPlatform.isIOS)
              GestureDetector(
                onTap: logic.appleLogin,
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                  child: Icon(
                    Icons.apple,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}
