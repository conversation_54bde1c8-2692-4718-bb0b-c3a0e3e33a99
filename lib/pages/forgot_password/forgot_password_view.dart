import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/custom_scaffold.dart';

import 'forgot_password_logic.dart';

class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(ForgotPasswordLogic());

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login-bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: BaseScaffold(
        backgroundColor: Colors.transparent,
        appBarBackgroundColor: Colors.transparent,
        // title: '密码登录',
        body: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 
                         MediaQuery.of(context).padding.top - 
                         kToolbarHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // 主要内容区域
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),

                          // Logo
                          _buildLogo(),

                          SizedBox(height: 24.h),

                          // 标题
                          _buildTitle(),

                          SizedBox(height: 40.h),

                          // 手机号输入框
                          _buildPhoneInput(logic),

                          SizedBox(height: 20.h),

                          // 验证码输入框
                          _buildCodeInput(logic),

                          SizedBox(height: 20.h),

                          // 新密码输入框
                          _buildPasswordInput(logic),

                          SizedBox(height: 32.h),

                          // 重置密码按钮
                          _buildResetButton(logic),

                          // 弹性空间
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),

                  // 底部间距
                  SizedBox(height: 40.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      child: Image.asset(
        'assets/images/logo.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Text(
      '找回密码',
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput(ForgotPasswordLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // +86前缀
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '+86',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20.h,
            color: const Color(0xFFE5E5E5),
          ),
          // 手机号输入
          Expanded(
            child: TextField(
              controller: logic.phoneController,
              keyboardType: TextInputType.phone,
              maxLength: 11,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                hintText: '输入手机号',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),
              ),
              onChanged: (value) => logic.validateForm(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建验证码输入框
  Widget _buildCodeInput(ForgotPasswordLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 验证码输入
          Expanded(
            child: TextField(
              controller: logic.codeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                hintText: '输入验证码',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),
              ),
              onChanged: (value) => logic.validateForm(),
            ),
          ),
          // 获取验证码按钮
          Obx(() => Container(
                margin: EdgeInsets.only(right: 8.w),
                child: ElevatedButton(
                  onPressed: logic.canSendCode.value
                      ? logic.sendVerificationCode
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A4A4A),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    disabledBackgroundColor: Colors.grey[300],
                    disabledForegroundColor: Colors.grey[600],
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    minimumSize: Size(80.w, 40.h),
                  ),
                  child: Text(
                    logic.countdown.value > 0
                        ? '${logic.countdown.value}s'
                        : '获取验证码',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  /// 构建新密码输入框
  Widget _buildPasswordInput(ForgotPasswordLogic logic) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1,
        ),
      ),
      child: Obx(() => TextField(
            controller: logic.passwordController,
            obscureText: !logic.isPasswordVisible.value,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            decoration: InputDecoration(
              hintText: '输入新密码',
              hintStyle: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFCCCCCC),
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 16.h,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  logic.isPasswordVisible.value
                      ? Icons.visibility
                      : Icons.visibility_off,
                  color: const Color(0xFFCCCCCC),
                  size: 20.sp,
                ),
                onPressed: logic.togglePasswordVisibility,
              ),
            ),
            onChanged: (value) => logic.validateForm(),
          )),
    );
  }

  /// 构建重置密码按钮
  Widget _buildResetButton(ForgotPasswordLogic logic) {
    return Obx(() => SizedBox(
          width: double.infinity,
          height: 56.h,
          child: ElevatedButton(
            onPressed:
                logic.canResetPassword.value ? logic.resetPassword : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A4A4A),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28.r),
              ),
              disabledBackgroundColor: Colors.grey[300],
              disabledForegroundColor: Colors.grey[600],
            ),
            child: logic.isLoading.value
                ? SizedBox(
                    width: 20.w,
                    height: 20.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    '重置密码',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ));
  }
}
