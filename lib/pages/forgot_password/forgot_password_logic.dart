import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/q_log.dart';

class ForgotPasswordLogic extends GetxController {
  // 控制器
  final phoneController = TextEditingController();
  final codeController = TextEditingController();
  final passwordController = TextEditingController();

  // 响应式变量
  final isLoading = false.obs;
  final canSendCode = false.obs;
  final canResetPassword = false.obs;
  final countdown = 0.obs;
  final isPasswordVisible = false.obs;

  // 设备clientId
  String clientId = '';

  // 定时器
  Timer? _countdownTimer;
  
  @override
  void onInit() {
    super.onInit();
    _initializeClientId();
    validateForm();
  }
  
  @override
  void onClose() {
    phoneController.dispose();
    codeController.dispose();
    passwordController.dispose();
    _countdownTimer?.cancel();
    super.onClose();
  }

  /// 初始化设备clientId
  Future<void> _initializeClientId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        clientId = androidInfo.id; // Android设备ID
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        clientId = iosInfo.identifierForVendor ?? ''; // iOS设备ID
      }

      QLog('设备clientId: $clientId');
    } catch (e) {
      QLog('获取设备clientId失败: $e');
      // 如果获取失败，生成一个基于时间戳的ID作为备用
      clientId = 'client_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// 验证表单
  void validateForm() {
    final phone = phoneController.text.trim();
    final code = codeController.text.trim();
    final password = passwordController.text.trim();

    // 验证手机号格式
    final phoneValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);

    // 验证验证码
    final codeValid = code.length == 6;

    // 验证密码
    final passwordValid = password.length >= 6;

    // 更新发送验证码按钮状态
    canSendCode.value = phoneValid && countdown.value == 0;

    // 更新重置密码按钮状态
    canResetPassword.value = phoneValid && codeValid && passwordValid && !isLoading.value;
  }

  /// 发送验证码
  Future<void> sendVerificationCode() async {
    if (!canSendCode.value) return;
    
    final phone = phoneController.text.trim();
    
    try {
      EasyLoading.show(status: '发送中...');
      
      // 调用获取验证码API
      final result = await API.getSmsCodeApi(
        mobile: phone,
      );
      
      if (result.code == '200' || result.code == 200) {
        EasyLoading.showSuccess('验证码已发送');
        startCountdown();
      } else {
        EasyLoading.showError(result.msg ?? '发送失败');
      }
    } catch (e) {
      QLog('发送验证码失败: $e');
      EasyLoading.showError('发送失败，请重试');
    }
  }

  /// 开始倒计时
  void startCountdown() {
    countdown.value = 60;
    canSendCode.value = false;
    
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        validateForm();
      }
    });
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// 重置密码
  Future<void> resetPassword() async {
    if (!canResetPassword.value) return;

    final phone = phoneController.text.trim();
    final code = codeController.text.trim();
    final password = passwordController.text.trim();

    try {
      isLoading.value = true;
      validateForm();

      EasyLoading.show(status: '重置中...');

      QLog('重置密码 - 手机号: $phone, 验证码: $code');

      // 调用重置密码API
      final result = await API.resetLoginPwdApi(
        mobile: phone,
        password: password,
        smsCode: code,
      );

      QLog('重置密码API响应: ${result.toJson()}');

      if (result.code == '200' || result.code == 200) {
        QLog('重置密码成功');
        EasyLoading.showSuccess('密码重置成功');
        
        // 延迟一下再返回登录页面
        Future.delayed(Duration(seconds: 1), () {
          Get.back(result: {
            'success': true,
            'message': '密码重置成功，请使用新密码登录',
            'mobile': phone,
          });
        });
      } else {
        QLog('重置密码失败: ${result.msg}');
        EasyLoading.showError(result.msg ?? '重置失败');
      }
    } catch (e) {
      QLog('重置密码异常: $e');
      EasyLoading.showError('重置失败，请重试');
    } finally {
      isLoading.value = false;
      validateForm();
    }
  }
}
