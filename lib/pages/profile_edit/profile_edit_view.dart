import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/colors.dart';
import 'profile_edit_logic.dart';

class ProfileEditPage extends StatelessWidget {
  const ProfileEditPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(ProfileEditLogic());

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: QText(
          '编辑个人信息',
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20.sp,
          ),
          onPressed: () => Get.back(),
        ),
        actions: [
          Obx(() => TextButton(
            onPressed: logic.isLoading.value ? null : logic.saveProfile,
            child: QText(
              '保存',
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: logic.isLoading.value ? Colors.grey : AppColors.primary,
            ),
          )),
        ],
      ),
      body: Obx(() {
        if (logic.isLoading.value && logic.memberData.value == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            children: [
              // 头像编辑
              _buildAvatarSection(logic),
              
              SizedBox(height: 30.h),
              
              // 表单字段
              _buildFormFields(logic),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildAvatarSection(ProfileEditLogic logic) {
    return Column(
      children: [
        Obx(() => Container(
          width: 100.w,
          height: 100.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.w),
            color: Colors.grey[200],
            border: Border.all(color: Colors.grey[300]!, width: 2),
          ),
          child: logic.avatarPath.value.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(50.w),
                  child: logic.avatarPath.value.startsWith('http')
                      ? Image.network(
                          logic.avatarPath.value,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.person,
                              size: 50.sp,
                              color: Colors.grey[600],
                            );
                          },
                        )
                      : Image.asset(
                          logic.avatarPath.value,
                          fit: BoxFit.cover,
                        ),
                )
              : Icon(
                  Icons.person,
                  size: 50.sp,
                  color: Colors.grey[600],
                ),
        )),
        SizedBox(height: 12.h),
        QText(
          '头像',
          fontSize: 14.sp,
          color: Colors.grey[600],
        ),
      ],
    );
  }

  Widget _buildFormFields(ProfileEditLogic logic) {
    return Column(
      children: [
        _buildTextField(
          label: '昵称',
          controller: logic.nicknameController,
          hintText: '请输入昵称',
        ),
        SizedBox(height: 20.h),

        _buildTextField(
          label: '真实姓名',
          controller: logic.realNameController,
          hintText: '请输入真实姓名',
        ),
        SizedBox(height: 20.h),

        _buildGenderSelector(logic),
      ],
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hintText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        QText(
          label,
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontSize: 16.sp,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.w),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.w),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.w),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderSelector(ProfileEditLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        QText(
          '性别',
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        SizedBox(height: 8.h),
        Obx(() => Row(
          children: [
            _buildGenderOption(logic, 0, '保密'),
            SizedBox(width: 20.w),
            _buildGenderOption(logic, 1, '男'),
            SizedBox(width: 20.w),
            _buildGenderOption(logic, 2, '女'),
          ],
        )),
      ],
    );
  }

  Widget _buildGenderOption(ProfileEditLogic logic, int value, String label) {
    final isSelected = logic.selectedGender.value == value;
    return GestureDetector(
      onTap: () => logic.selectedGender.value = value,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.grey[400]!,
                width: 2,
              ),
              color: isSelected ? AppColors.primary : Colors.transparent,
            ),
            child: isSelected
                ? Icon(
                    Icons.check,
                    size: 12.sp,
                    color: Colors.white,
                  )
                : null,
          ),
          SizedBox(width: 8.w),
          QText(
            label,
            fontSize: 16.sp,
            color: Colors.black87,
          ),
        ],
      ),
    );
  }


}
