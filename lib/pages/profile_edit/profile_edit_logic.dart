import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/pages/profile/profile_logic.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/toast_util.dart';
import 'package:lima/routers/app_router.dart';

class ProfileEditLogic extends GetxController {
  // 表单控制器
  final TextEditingController nicknameController = TextEditingController();
  final TextEditingController realNameController = TextEditingController();
  
  // 响应式变量
  final RxBool isLoading = false.obs;
  final RxInt selectedGender = 0.obs; // 0=保密, 1=男, 2=女
  final RxString selectedBirthday = ''.obs;
  final RxString avatarPath = ''.obs;
  final Rx<Map<String, dynamic>?> memberData = Rx<Map<String, dynamic>?>(null);

  @override
  void onInit() {
    super.onInit();
    loadUserInfo();
  }

  @override
  void onClose() {
    nicknameController.dispose();
    realNameController.dispose();
    super.onClose();
  }

  /// 加载用户信息
  Future<void> loadUserInfo() async {
    try {
      isLoading.value = true;

      final result = await API.getMemberInfoApi();

      if (result.state == 200 && result.data != null) {
        memberData.value = result.data as Map<String, dynamic>;
        final data = result.data as Map<String, dynamic>;

        // 填充表单数据
        nicknameController.text = data['memberNickName'] ?? '';
        realNameController.text = data['memberTrueName'] ?? '';
        selectedGender.value = data['gender'] ?? 0;
        avatarPath.value = data['memberAvatar'] ?? '';

        QLog('用户信息加载成功: ${result.data}');
      } else {
        ToastUtil.showError('加载用户信息失败: ${result.msg}');
      }
    } catch (e) {
      QLog('加载用户信息异常: $e');
      ToastUtil.showError('加载用户信息失败');
    } finally {
      isLoading.value = false;
    }
  }



  /// 保存个人信息
  Future<void> saveProfile() async {
    try {
      isLoading.value = true;

      // 准备请求参数
      String? memberNickName;
      String? memberTrueName;

      if (nicknameController.text.isNotEmpty) {
        memberNickName = nicknameController.text.trim();
      }

      if (realNameController.text.isNotEmpty) {
        memberTrueName = realNameController.text.trim();
      }

      QLog('保存个人信息参数: gender=${selectedGender.value}, memberNickName=$memberNickName, memberTrueName=$memberTrueName, memberAvatar=${avatarPath.value}');

      final result = await API.updateMemberInfoApi(
        gender: selectedGender.value,
        memberNickName: memberNickName,
        memberTrueName: memberTrueName,
        memberAvatar: avatarPath.value.isNotEmpty ? avatarPath.value : null,
      );

      if (result.state == 200) {
        QLog('✅ 个人信息保存成功，准备返回个人页面');
        ToastUtil.showSuccess('个人信息保存成功');

        // 更新本地缓存的用户信息，避免重复请求API
        try {
          final profileLogic = Get.find<ProfileLogic>();
          // 直接更新本地数据，而不是重新请求API
          final updatedData = Map<String, dynamic>.from(memberData.value ?? {});
          updatedData['gender'] = selectedGender.value;
          updatedData['memberNickName'] = memberNickName;
          updatedData['memberTrueName'] = memberTrueName;
          if (avatarPath.value.isNotEmpty) {
            updatedData['memberAvatar'] = avatarPath.value;
          }

          profileLogic.updateUserInfo(updatedData);
          QLog('✅ 已更新本地用户信息，避免重复API请求');
        } catch (e) {
          QLog('⚠️ 无法找到个人页面逻辑控制器: $e');
        }

        // 直接跳转到个人页面（我的页面）
        QLog('🔄 直接跳转到个人页面');
        Get.offAllNamed(AppRoute.mainTab, arguments: {'selectedTab': 3}); // 跳转到第4个tab（个人页面）
        QLog('✅ 已跳转到个人页面');
      } else {
        QLog('❌ 个人信息保存失败: ${result.msg}');
        ToastUtil.showError('保存失败: ${result.msg}');
      }
    } catch (e) {
      QLog('保存个人信息异常: $e');
      ToastUtil.showError('保存失败');
    } finally {
      isLoading.value = false;
    }
  }


}
