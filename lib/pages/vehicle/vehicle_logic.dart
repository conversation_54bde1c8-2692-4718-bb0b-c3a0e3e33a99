import 'package:get/get.dart';

class VehicleLogic extends GetxController {
  // 车辆基本信息
  var vehicleName = '立马极限PLUS'.obs;
  var batteryLevel = 48.obs;
  var remainingRange = 40.obs;
  var isConnected = true.obs;
  
  // 车辆状态
  var isLocked = true.obs;
  var isEngineOn = false.obs;
  var isAlarmOn = false.obs;
  
  // 里程信息
  var recentMileage = 15.2.obs;
  var totalMileage = 15721.obs;
  
  @override
  void onInit() {
    super.onInit();
    // 初始化车辆数据
    loadVehicleData();
  }
  
  /// 加载车辆数据
  void loadVehicleData() {
    // TODO: 从API获取车辆数据
    // 这里可以添加实际的API调用
  }
  
  /// 切换车辆锁定状态
  void toggleLock() {
    isLocked.value = !isLocked.value;
    // TODO: 调用车辆控制API
  }
  
  /// 启动/关闭引擎
  void toggleEngine() {
    isEngineOn.value = !isEngineOn.value;
    // TODO: 调用车辆控制API
  }
  
  /// 触发寻车铃
  void triggerAlarm() {
    isAlarmOn.value = true;
    // TODO: 调用车辆控制API
    
    // 3秒后自动关闭
    Future.delayed(const Duration(seconds: 3), () {
      isAlarmOn.value = false;
    });
  }
  
  /// 发起导航
  void startNavigation() {
    // TODO: 打开地图应用进行导航
    Get.snackbar('导航', '正在启动导航...');
  }
  
  /// 刷新车辆数据
  void refreshVehicleData() {
    loadVehicleData();
  }
}
