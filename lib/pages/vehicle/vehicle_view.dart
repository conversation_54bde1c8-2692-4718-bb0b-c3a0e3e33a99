import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/colors.dart';
import 'vehicle_logic.dart';

class VehiclePage extends StatelessWidget {
  const VehiclePage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(VehicleLogic());

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFE8B5FF),
              Color(0xFFFFB5E8),
              Color(0xFFB5E8FF),
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // 顶部状态栏和车辆信息
                _buildTopSection(),

                // 车辆3D模型展示
                _buildVehicleModel(),

                // 控制按钮区域
                _buildControlButtons(),

                // 底部安全间距
                SizedBox(height: 100.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建顶部区域（状态栏+车辆信息）
  Widget _buildTopSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Column(
        children: [
          // 车辆名称和菜单
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  QText(
                    '立马极限PLUS',
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.bluetooth,
                    size: 16.sp,
                    color: Colors.blue,
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8.w),
                ),
                child: Icon(
                  Icons.menu,
                  size: 20.sp,
                  color: Colors.black87,
                ),
              ),
            ],
          ),

          SizedBox(height: 30.h),

          // 电量和续航信息
          Row(
            children: [
              QText(
                '48',
                fontSize: 48.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              SizedBox(width: 8.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  QText(
                    '%',
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      QText(
                        '40km',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      SizedBox(width: 4.w),
                      QText(
                        '续航',
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              // 右侧图标
              Column(
                children: [
                  Icon(Icons.gps_fixed, size: 20.sp, color: Colors.black54),
                  SizedBox(height: 8.h),
                  Icon(Icons.signal_cellular_4_bar, size: 16.sp, color: Colors.black54),
                  SizedBox(height: 4.h),
                  Icon(Icons.bluetooth, size: 16.sp, color: Colors.blue),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建车辆3D模型展示
  Widget _buildVehicleModel() {
    return Container(
      width: double.infinity,
      height: 250.h,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Center(
        child: Stack(
          children: [
            // 车辆阴影
            Positioned(
              bottom: 20.h,
              left: 60.w,
              right: 60.w,
              child: Container(
                height: 30.h,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      Colors.black.withValues(alpha: 0.3),
                      Colors.transparent,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(50.w),
                ),
              ),
            ),
            // 车辆图片占位符
            Center(
              child: Container(
                width: 300.w,
                height: 200.h,
                child: Icon(
                  Icons.electric_scooter,
                  size: 150.sp,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建控制按钮区域
  Widget _buildControlButtons() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(20.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 第一行按钮 - 主要功能
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(Icons.shield_outlined, '设防', false),
              _buildControlButton(Icons.power_settings_new, '启动', true),
              _buildControlButton(Icons.notifications_outlined, '寻车铃', false),
            ],
          ),

          SizedBox(height: 20.h),

          // 第二行按钮 - 辅助功能
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(Icons.lock_open_outlined, '感应解锁', false),
              _buildControlButton(Icons.battery_charging_full, '坐桶盖', false),
              _buildControlButton(Icons.directions_run, '助力推行', false),
              _buildControlButton(Icons.apps, '更多功能', false),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建单个控制按钮
  Widget _buildControlButton(IconData icon, String label, bool isActive) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          Get.snackbar('功能', '$label 功能');
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                color: isActive ? const Color(0xFF2D3748) : Colors.grey[100],
                borderRadius: BorderRadius.circular(28.w),
                boxShadow: isActive ? [
                  BoxShadow(
                    color: const Color(0xFF2D3748).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 24.sp,
                color: isActive ? Colors.white : Colors.grey[600],
              ),
            ),
            SizedBox(height: 6.h),
            QText(
              label,
              fontSize: 11.sp,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
