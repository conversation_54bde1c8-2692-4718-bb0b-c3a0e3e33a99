import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lima/pages/simple_webview/simple_webview_page.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/common/config.dart';

class ServicePage extends StatefulWidget {
  const ServicePage({Key? key}) : super(key: key);

  @override
  State<ServicePage> createState() => _ServicePageState();
}

class _ServicePageState extends State<ServicePage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    QLog("服务页面: 构建页面，路径: /#/pages/service/home");
    
    return SimpleWebViewPage(
      key: ValueKey('webview_service'),
      baseUrl:  Config.BASE_URL,
      pagePath: '/#/pages/service/home',
      pageTitle: '服务',
      enableCache: true,
    );
  }

  @override
  void dispose() {
    QLog('服务页面: 开始清理资源');
    super.dispose();
  }
}