import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/global.dart';
import 'package:lima/pages/web/web_page.dart';
import 'package:lima/common/config.dart';

class ServiceLogic extends GetxController {
  /// WebView控制器
  WebController? myWebController;

  /// WebView组件
  Rx<Widget> webWidget = Rx(Container());

  /// 页面加载完成状态
  var loadFinished = false.obs;

  @override
  void onInit() {
    super.onInit();
    QLog('ServiceLogic: 初始化服务页面');
    initWidget();
  }

  /// 初始化WebView组件
  void initWidget() async {
    QLog('ServiceLogic: 开始初始化WebView组件');
    
    // 获取用户token用于cookie设置
    List<Map<dynamic, dynamic>> map = [];
    String? token = await Global.get_lima_token();
    if (token != null && token.isNotEmpty) {
      map.add({'key': 'access_token', 'value': token});
      QLog('ServiceLogic: 已获取lima_token用于cookie设置');
    } else {
      QLog('ServiceLogic: 未获取到有效的lima_token');
    }

    // 构建服务页面URL - 使用正式环境的服务页面
    String serviceUrl = Config.BASE_WEBSITE + '/service?type=app';
    if (token != null && token.isNotEmpty) {
      serviceUrl += '&access_token=$token';
    }
    
    QLog('ServiceLogic: 服务页面URL: $serviceUrl');

    webWidget.value = WebPage(
      homeUrl: serviceUrl,
      onWebCreated: (c) {
        myWebController = c;
        QLog('ServiceLogic: WebView控制器已创建');
      },
      cookies: map,
      onLoadFinished: (u) {
        QLog('ServiceLogic: 页面加载完成: $u');
        if (u != null) {
          Uri u1 = Uri.parse(u);
          Uri u2 = Uri.parse(serviceUrl);
          QLog('ServiceLogic: 加载完成对比: u1: ${u1.host}, u2: ${u2.host}, 对比: ${u1.host == u2.host}');
          if (u1.host == u2.host) {
            loadFinished.value = true;
            // 注入CSS隐藏H5页面的底部导航栏
            _injectHideTabBarCSS();
          }
        }
      },
    );
  }

  /// 刷新页面URL
  void refreshUrl(String url) {
    loadFinished.value = false;
    myWebController?.loadUrl(url);
    QLog('ServiceLogic: 刷新页面URL: $url');
  }

  /// 刷新WebView的token（登录成功后调用）
  Future<void> refreshToken() async {
    QLog('ServiceLogic: 开始刷新WebView token');
    initWidget();
    QLog('ServiceLogic: WebView token刷新完成');
  }

  /// 注入CSS隐藏H5页面的底部导航栏
  void _injectHideTabBarCSS() {
    if (myWebController != null) {
      final cssCode = '''
        var style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = '.adm-tab-bar { display: none !important; }';
        document.head.appendChild(style);
      ''';
      myWebController!.runJavaScript(cssCode);
      QLog('ServiceLogic: 已注入CSS隐藏.adm-tab-bar');
    }
  }

  /// 处理导航请求（由MainTabLogic直接调用）
  void handleNavigation(Map<String, dynamic> navigationData) {
    QLog('ServiceLogic: 接收到导航请求: $navigationData');
    _notifyH5Navigation(navigationData);
  }

  /// 使用_notifyH5Navigation方法进行H5页面跳转
  void _notifyH5Navigation(Map<String, dynamic> navigationData) {
    QLog('ServiceLogic: 执行H5导航跳转: $navigationData');

    // 对于ServiceLogic的WebView，我们需要使用JavaScript来进行导航
    if (myWebController != null) {
      String targetPage = navigationData['targetPage'] ?? '/service';
      int tabIndex = navigationData['tabIndex'] ?? 2;

      // 如果是服务页面的导航
      if (tabIndex == 2) {
        QLog('ServiceLogic: 执行服务页面导航到: $targetPage');

        // 使用JavaScript通知H5页面进行导航
        final jsCode = '''
          if (window.appNavigate) {
            window.appNavigate('$targetPage');
          } else if (window.jsBridgeHelper && window.jsBridgeHelper.sendMessage) {
            window.jsBridgeHelper.sendMessage('receiveMessage', {
              action: 'navigate',
              targetPage: '$targetPage',
              tabIndex: $tabIndex
            });
          } else {
            console.log('服务页面导航: 未找到导航方法，targetPage: $targetPage');
          }
        ''';

        myWebController!.runJavaScript(jsCode);
        QLog('ServiceLogic: 已发送导航JavaScript指令');
      }
    } else {
      QLog('ServiceLogic: WebController为空，无法执行导航');
    }
  }

  @override
  void onReady() async {
    super.onReady();
    QLog('ServiceLogic: onReady完成');
  }

  @override
  void onClose() {
    super.onClose();
    QLog('ServiceLogic: 页面关闭，清理资源');
  }
}
