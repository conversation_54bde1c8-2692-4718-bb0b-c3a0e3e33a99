import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:lima/global.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/http/api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ProfileLogic extends GetxController {
  // 用户信息响应式变量
  var isLoggedIn = false.obs;
  var userInfo = Rxn<Map<String, dynamic>>();
  var token = Rxn<String>();
  
  // 用户显示信息
  var userName = ''.obs;
  var userLevel = ''.obs;
  var memberProgress = ''.obs;
  var followCount = '0'.obs;
  var collectCount = '0'.obs;
  var pointsCount = '0'.obs;
  var couponCount = '0'.obs;

  // API返回的完整用户信息
  var memberData = Rxn<Map<String, dynamic>>();
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadUserInfo();
  }



  /// 加载用户信息
  Future<void> loadUserInfo() async {
    try {
      isLoading.value = true;

      // 获取token
      final tokenValue = await Global.getToken();
      token.value = tokenValue;

      if (tokenValue != null) {
        // 已登录状态，调用API获取最新用户信息
        isLoggedIn.value = true;
        QLog('用户已登录，开始获取用户信息...');

        try {
          // 调用获取用户信息API
          final result = await API.getMemberInfoApi();

          if (result.state == 200 && result.data != null) {
            // API调用成功，使用API返回的数据
            memberData.value = result.data as Map<String, dynamic>;
            _updateDisplayInfoFromApi(memberData.value!);
            QLog('✅ API获取用户信息成功: ${memberData.value}');
          } else {
            // API调用失败，使用本地存储的信息作为备用
            QLog('⚠️ API获取用户信息失败: ${result.msg}，使用本地信息');
            await _loadLocalUserInfo();
          }
        } catch (e) {
          // API调用异常，使用本地存储的信息作为备用
          QLog('❌ API调用异常: $e，使用本地信息');
          await _loadLocalUserInfo();
        }
      } else {
        // 未登录状态
        isLoggedIn.value = false;
        userInfo.value = null;
        memberData.value = null;
        _setDefaultInfo();
        QLog('用户未登录，显示默认信息');
      }
    } catch (e) {
      QLog('加载用户信息失败: $e');
      isLoggedIn.value = false;
      _setDefaultInfo();
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载本地存储的用户信息（备用方案）
  Future<void> _loadLocalUserInfo() async {
    try {
      final sp = await SharedPreferences.getInstance();
      final userInfoStr = sp.getString(Config.USER_INFO);

      if (userInfoStr != null) {
        final userInfoMap = json.decode(userInfoStr) as Map<String, dynamic>;
        userInfo.value = userInfoMap;
        _updateDisplayInfo(userInfoMap);
        QLog('使用本地用户信息: ${userInfoMap.toString()}');
      } else {
        _setDefaultInfo();
        QLog('本地无用户信息，使用默认信息');
      }
    } catch (e) {
      QLog('加载本地用户信息失败: $e');
      _setDefaultInfo();
    }
  }

  /// 根据API返回的数据更新显示信息
  void _updateDisplayInfoFromApi(Map<String, dynamic> apiData) {
    // 使用API返回的字段更新显示信息
    userName.value = apiData['memberNickName']?.toString().isNotEmpty == true
        ? apiData['memberNickName']
        : (apiData['memberName']?.toString().isNotEmpty == true
            ? apiData['memberName']
            : (apiData['memberTrueName']?.toString().isNotEmpty == true
                ? apiData['memberTrueName']
                : '立小马'));

    // 会员等级逻辑（根据实际业务调整）
    final isSuper = apiData['isSuper'] ?? 0;
    if (isSuper == 1) {
      userLevel.value = '超级会员';
    } else {
      // 根据成长值判断等级
      final growthValue = apiData['memberGrowthValue'] ?? 0;
      if (growthValue >= 5000) {
        userLevel.value = '钻石会员';
      } else if (growthValue >= 3000) {
        userLevel.value = '黄金会员';
      } else if (growthValue >= 1000) {
        userLevel.value = '银牌会员';
      } else {
        userLevel.value = '普通会员';
      }
    }

    // 成长值进度
    final currentGrowth = apiData['memberGrowthValue'] ?? 0;
    final nextLevelGrowth = _getNextLevelGrowth(currentGrowth);
    memberProgress.value = '$currentGrowth/$nextLevelGrowth';

    // 统计数据（这些可能需要其他API获取，暂时使用默认值）
    followCount.value = '0'; // 关注数
    collectCount.value = '0'; // 收藏数
    pointsCount.value = currentGrowth.toString(); // 使用成长值作为积分
    couponCount.value = '0'; // 卡券数

    QLog('✅ 用户信息更新完成:');
    QLog('  - 用户名: ${userName.value}');
    QLog('  - 会员等级: ${userLevel.value}');
    QLog('  - 成长值: ${memberProgress.value}');
    QLog('  - 手机号: ${apiData['memberMobile'] ?? '未绑定'}');
    QLog('  - 邮箱: ${apiData['memberEmail'] ?? '未绑定'}');
  }

  /// 根据当前成长值计算下一等级所需成长值
  int _getNextLevelGrowth(int currentGrowth) {
    if (currentGrowth < 1000) return 1000;
    if (currentGrowth < 3000) return 3000;
    if (currentGrowth < 5000) return 5000;
    return 10000; // 钻石会员以上
  }

  /// 更新显示信息（本地数据备用方案）
  void _updateDisplayInfo(Map<String, dynamic> info) {
    // 根据本地存储的字段更新显示信息
    userName.value = info['nickname'] ?? info['name'] ?? info['userName'] ?? '立小马';

    // 会员等级逻辑（根据实际业务调整）
    final memberLevel = info['memberLevel'] ?? info['level'] ?? 1;
    switch (memberLevel) {
      case 1:
        userLevel.value = '普通会员';
        break;
      case 2:
        userLevel.value = '银牌会员';
        break;
      case 3:
        userLevel.value = '黄金会员';
        break;
      case 4:
        userLevel.value = '钻石会员';
        break;
      default:
        userLevel.value = '普通会员';
    }

    // 成长值
    final currentExp = info['currentExp'] ?? info['experience'] ?? 1200;
    final nextLevelExp = info['nextLevelExp'] ?? info['nextExperience'] ?? 3000;
    memberProgress.value = '$currentExp/$nextLevelExp';

    // 统计数据
    followCount.value = (info['followCount'] ?? info['follows'] ?? 0).toString();
    collectCount.value = (info['collectCount'] ?? info['collections'] ?? 0).toString();
    pointsCount.value = (info['points'] ?? info['score'] ?? 0).toString();
    couponCount.value = (info['couponCount'] ?? info['coupons'] ?? 0).toString();
  }

  /// 设置默认信息（未登录状态）
  void _setDefaultInfo() {
    userName.value = '';
    userLevel.value = '';
    memberProgress.value = '0';
    followCount.value = '0';
    collectCount.value = '0';
    pointsCount.value = '0';
    couponCount.value = '0';
  }

  /// 点击头像跳转登录
  void onAvatarTap() {
    if (!isLoggedIn.value) {
      // 未登录，跳转到登录页面
      // 注意：登录成功后会直接跳转到主Tab页面，所以这里不需要等待返回结果
      // 而是在onResume时重新加载用户信息
      Get.toNamed(AppRoute.login);
    } else {
      // 已登录，可以跳转到个人信息编辑页面或显示菜单
      _showUserMenu();
    }
  }

  /// 显示用户菜单
  void _showUserMenu() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('编辑个人信息'),
              onTap: () {
                Get.back();
                // TODO: 跳转到个人信息编辑页面
              },
            ),
            ListTile(
              leading: Icon(Icons.logout, color: Get.theme.colorScheme.error),
              title: Text('退出登录', style: TextStyle(color: Get.theme.colorScheme.error)),
              onTap: () {
                Get.back();
                logout();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      await Global.removeToken();
      await Global.removeUserInfo();
      
      // 重置状态
      isLoggedIn.value = false;
      userInfo.value = null;
      token.value = null;
      _setDefaultInfo();
      
      QLog('用户已退出登录');
      
      // 可选：显示退出成功提示
      Get.snackbar('提示', '已退出登录');
    } catch (e) {
      QLog('退出登录失败: $e');
      Get.snackbar('错误', '退出登录失败');
    }
  }

  /// 功能菜单点击事件
  void onMenuItemTap(String menuType) {
    // 定义菜单项的配置
    final Map<String, Map<String, dynamic>> menuConfig = {
      'my_vehicle': {
        'title': '我的车辆',
        'icon': Icons.directions_car,
      },
      'my_orders': {
        'title': '我的订单',
        'icon': Icons.receipt_long,
      },
      'my_service': {
        'title': '我的服务',
        'icon': Icons.build,
      },
      'member_benefits': {
        'title': '会员权益',
        'icon': Icons.card_giftcard,
      },
      'points_task': {
        'title': '积分任务',
        'icon': Icons.confirmation_number,
      },
      'points_mall': {
        'title': '积分商城',
        'icon': Icons.store,
      },
      'address_manage': {
        'title': '地址管理',
        'icon': Icons.location_on,
      },
      'my_policy': {
        'title': '我的保单',
        'icon': Icons.security,
      },
      'pending_orders': {
        'title': '待处理订单',
        'icon': Icons.receipt,
      },
      'feedback': {
        'title': '建议与反馈',
        'icon': Icons.feedback,
      },
    };

    // 获取菜单配置
    final config = menuConfig[menuType];
    if (config != null) {
      Get.toNamed('/coming_soon', arguments: {
        'title': config['title'],
        'subtitle': '即将上线',
        'icon': config['icon'],
      });
    } else {
      Get.toNamed('/coming_soon', arguments: {
        'title': '功能',
        'subtitle': '即将上线',
        'icon': Icons.construction,
      });
    }
  }

  /// 刷新用户信息
  Future<void> refreshUserInfo() async {
    await loadUserInfo();
  }

  /// 更新用户信息（不重新请求API）
  void updateUserInfo(Map<String, dynamic> updatedData) {
    memberData.value = updatedData;
    _updateDisplayInfoFromApi(updatedData);
  }
}
