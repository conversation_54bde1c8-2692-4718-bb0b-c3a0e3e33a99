import 'package:flutter/material.dart';
import 'package:lima/pages/simple_webview/simple_webview_page.dart';
import 'package:lima/utils/q_log.dart';

/// 预加载WebView包装器
class PreloaderWebView extends StatefulWidget {
  final String baseUrl;
  final String pagePath;
  final String pageTitle;
  final bool enableCache;

  const PreloaderWebView({
    Key? key,
    required this.baseUrl,
    required this.pagePath,
    required this.pageTitle,
    this.enableCache = true,
  }) : super(key: key);

  @override
  State<PreloaderWebView> createState() => _PreloaderWebViewState();
}

class _PreloaderWebViewState extends State<PreloaderWebView> with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  bool _hasError = false;

  @override
  bool get wantKeepAlive => widget.enableCache;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Stack(
      children: [
        // WebView内容
        SimpleWebViewPage(
          baseUrl: widget.baseUrl,
          pagePath: widget.pagePath,
          pageTitle: widget.pageTitle,
          enableCache: widget.enableCache,
          onLoadingChanged: (isLoading) {
            if (mounted && _isLoading != !isLoading) {
              setState(() {
                _isLoading = !isLoading;
                if (!isLoading) {
                  _hasError = false;
                }
              });
              QLog('PreloaderWebView: ${widget.pageTitle} 加载状态变化: $_isLoading');
            }
          },
        ),
        
        // 平滑过渡层
        if (_isLoading && !_hasError)
          AnimatedOpacity(
            opacity: _isLoading ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 400),
            child: Container(
              color: Colors.white,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 品牌色的加载指示器
                    const SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFC70E2D)),
                        strokeWidth: 3,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      '正在加载${widget.pageTitle}...',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF666666),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '请稍候',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF999999),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}