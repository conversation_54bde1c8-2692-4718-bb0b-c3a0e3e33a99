import 'package:flutter/material.dart';
import 'package:lima/pages/simple_webview/preloader_webview.dart';
import 'package:lima/utils/q_log.dart';

/// WebView缓存管理器
class WebViewCacheManager {
  static final Map<String, Widget> _cachedWebViews = {};
  
  /// 获取缓存的WebView，如果不存在则创建
  static Widget getCachedWebView({
    required String cacheKey,
    required String baseUrl,
    required String pagePath,
    required String pageTitle,
    bool enableCache = true,
    Function(bool isLoading)? onLoadingChanged,
  }) {
    if (!_cachedWebViews.containsKey(cacheKey)) {
      QLog('WebViewCacheManager: 创建新的WebView缓存，key: $cacheKey');
      
      _cachedWebViews[cacheKey] = PreloaderWebView(
        key: ValueKey(cacheKey),
        baseUrl: baseUrl,
        pagePath: pagePath,
        pageTitle: pageTitle,
        enableCache: enableCache,
      );
    } else {
      QLog('WebViewCacheManager: 使用缓存的WebView，key: $cacheKey');
    }
    
    return _cachedWebViews[cacheKey]!;
  }
  
  /// 清理指定的缓存
  static void clearCache(String cacheKey) {
    _cachedWebViews.remove(cacheKey);
    QLog('WebViewCacheManager: 清理缓存，key: $cacheKey');
  }
  
  /// 清理所有缓存
  static void clearAllCache() {
    _cachedWebViews.clear();
    QLog('WebViewCacheManager: 清理所有缓存');
  }
  
  /// 获取缓存数量
  static int getCacheCount() {
    return _cachedWebViews.length;
  }
  
  /// 检查是否有缓存
  static bool hasCache(String cacheKey) {
    return _cachedWebViews.containsKey(cacheKey);
  }
}