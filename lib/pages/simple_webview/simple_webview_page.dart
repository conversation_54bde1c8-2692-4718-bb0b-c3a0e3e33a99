import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/common/jsapi.dart';
import 'package:lima/pages/login/login_view.dart';
import 'package:lima/pages/login/login_logic.dart';
import 'package:lima/services/carrier_login_service.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/pages/main_tab/main_tab_logic.dart';
import 'package:get/get.dart';
import 'package:lima/utils/wxkit.dart';
// MobSDK 已移除
// import 'package:lima/utils/mob_sdk.dart';

class SimpleWebViewPage extends StatefulWidget {
  final String baseUrl;
  final String pagePath;
  final String pageTitle;
  final Function(bool isLoading)? onLoadingChanged;
  final bool enableCache;

  const SimpleWebViewPage({
    Key? key,
    required this.baseUrl,
    required this.pagePath,
    required this.pageTitle,
    this.onLoadingChanged,
    this.enableCache = true,
  }) : super(key: key);

  @override
  State<SimpleWebViewPage> createState() => _SimpleWebViewPageState();

  /// 静态方法：通知H5进行导航
  static void notifyH5Navigation(
      GlobalKey key, Map<String, dynamic> navigationData) {
    final state = key.currentState;
    if (state != null && state is _SimpleWebViewPageState) {
      state.notifyH5ToNavigate(navigationData);
    }
  }

  /// 全局导航方法：通知所有活跃的H5页面进行导航
  static void notifyAllH5Navigation(Map<String, dynamic> navigationData) {
    for (final instance in _SimpleWebViewPageState._activeInstances) {
      instance.notifyH5ToNavigate(navigationData);
    }
  }
}

class _SimpleWebViewPageState extends State<SimpleWebViewPage> with AutomaticKeepAliveClientMixin {
  // 静态列表，保存所有活跃的实例
  static final Set<_SimpleWebViewPageState> _activeInstances =
      <_SimpleWebViewPageState>{};

  InAppWebViewController? webViewController;
  String? currentUrl;
  bool isLoading = true;
  Timer? _timeoutTimer;
  bool _isInitialized = false; // 防止重复初始化
  bool _isNavigating = false; // 防止重复导航
  
  @override
  bool get wantKeepAlive => widget.enableCache;

  @override
  void initState() {
    super.initState();
    _activeInstances.add(this); // 添加到活跃实例列表
    _initializeWebView();
  }
  

  /// 初始化WebView
  Future<void> _initializeWebView() async {
    if (_isInitialized) {
      QLog('${widget.pageTitle}: WebView已经初始化，跳过重复初始化');
      return;
    }

    _isInitialized = true;
    QLog('${widget.pageTitle}: 开始初始化WebView');

    await _buildInitialUrl();

    // 如果是第一次加载，设置一个超时机制，防止一直卡在loading页面
    if (widget.onLoadingChanged != null) {
      _timeoutTimer = Timer(const Duration(seconds: 10), () {
        if (mounted && isLoading) {
          QLog('${widget.pageTitle}: WebView加载超时，强制标记为加载完成');
          setState(() {
            isLoading = false;
          });
          widget.onLoadingChanged?.call(false);
        }
      });
    }
  }

  /// 获取jsBridgeHelper.js内容
  Future<String> get jsAsserts async =>
      rootBundle.loadString('assets/jsBridgeHelper.js');

  /// 设置JavaScript桥接
  Future<void> _setupJavaScriptBridge(InAppWebViewController controller) async {
    try {
      QLog('${widget.pageTitle}: 开始设置JavaScript桥接');

      // 添加JavaScript处理器
      controller.addJavaScriptHandler(
        handlerName: 'receiveMessage',
        callback: _handleJavaScriptCallback,
      );

      // 注入receiveMessage函数
      final scriptReceive = UserScript(source: '''
        function receiveMessage(jsonStr) {
          if(jsonStr !== undefined && jsonStr !== "") {
            let data = JSON.parse(JSON.stringify(jsonStr));
            window.jsBridgeHelper.receiveMessage(data);
          }
        }
      ''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

      await controller.addUserScript(userScript: scriptReceive);

      // 注入jsBridgeHelper
      final bridgeHelper = UserScript(source: '''
        window.jsBridgeHelper = ${await jsAsserts};

        // 将callNativeLogin方法暴露到全局作用域，方便H5页面调用
        window.callNativeLogin = function() {
          console.log('全局callNativeLogin被调用');
          if (window.jsBridgeHelper && window.jsBridgeHelper.callNativeLogin) {
            return window.jsBridgeHelper.callNativeLogin();
          } else {
            console.error('jsBridgeHelper未初始化或callNativeLogin方法不存在');
            return Promise.reject('jsBridgeHelper未初始化');
          }
        };

        // 将callNativeLogout方法暴露到全局作用域，方便H5页面调用
        window.callNativeLogout = function() {
          console.log('全局callNativeLogout被调用');
          if (window.jsBridgeHelper && window.jsBridgeHelper.sendMessage) {
            return window.jsBridgeHelper.sendMessage('receiveMessage', {
              action: 'logout'
            });
          } else {
            console.error('jsBridgeHelper未初始化或sendMessage方法不存在');
            return Promise.reject('jsBridgeHelper未初始化');
          }
        };

        // 也可以通过window.lima命名空间调用
        window.lima = window.lima || {};
        window.lima.callNativeLogin = window.callNativeLogin;
        window.lima.callNativeLogout = window.callNativeLogout;

        console.log('JavaScript桥接初始化完成，callNativeLogin方法已暴露到全局作用域');
      ''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

      await controller.addUserScript(userScript: bridgeHelper);

      QLog('${widget.pageTitle}: JavaScript桥接设置完成');
    } catch (e) {
      QLog('${widget.pageTitle}: JavaScript桥接设置失败: $e');
    }
  }

  /// 重新注入JavaScript桥接（用于页面路由切换后）
  Future<void> _reinjectJavaScriptBridge(
      InAppWebViewController controller) async {
    try {
      QLog('${widget.pageTitle}: 重新注入JavaScript桥接');

      // 检查jsBridgeHelper是否存在，如果不存在则重新注入
      final checkResult = await controller.evaluateJavascript(source: '''
        (function() {
          if (typeof window.jsBridgeHelper === 'undefined') {
            console.log('jsBridgeHelper未定义，需要重新注入');
            return false;
          } else {
            console.log('jsBridgeHelper已存在');
            return true;
          }
        })();
      ''');

      QLog('${widget.pageTitle}: jsBridgeHelper检查结果: $checkResult');

      // 如果jsBridgeHelper不存在，重新注入
      if (checkResult == false || checkResult == null) {
        QLog('${widget.pageTitle}: jsBridgeHelper不存在，开始重新注入');

        // 重新注入jsBridgeHelper
        await controller.evaluateJavascript(source: '''
          window.jsBridgeHelper = ${await jsAsserts};

          // 重新设置全局方法
          window.callNativeLogin = function() {
            console.log('全局callNativeLogin被调用');
            if (window.jsBridgeHelper && window.jsBridgeHelper.callNativeLogin) {
              return window.jsBridgeHelper.callNativeLogin();
            } else {
              console.error('jsBridgeHelper未初始化或callNativeLogin方法不存在');
              return Promise.reject('jsBridgeHelper未初始化');
            }
          };

          window.callNativeLogout = function() {
            console.log('全局callNativeLogout被调用');
            if (window.jsBridgeHelper && window.jsBridgeHelper.sendMessage) {
              return window.jsBridgeHelper.sendMessage('receiveMessage', {
                action: 'logout'
              });
            } else {
              console.error('jsBridgeHelper未初始化或sendMessage方法不存在');
              return Promise.reject('jsBridgeHelper未初始化');
            }
          };

          // 重新设置lima命名空间
          window.lima = window.lima || {};
          window.lima.callNativeLogin = window.callNativeLogin;
          window.lima.callNativeLogout = window.callNativeLogout;

          console.log('jsBridgeHelper重新注入完成');
        ''');

        QLog('${widget.pageTitle}: jsBridgeHelper重新注入完成');
      }
    } catch (e) {
      QLog('${widget.pageTitle}: 重新注入JavaScript桥接失败: $e');
    }
  }

  /// JavaScript桥接回调处理
  Future<dynamic> _handleJavaScriptCallback(dynamic data) async {
    QLog('${widget.pageTitle}: 收到JavaScript回调: $data');

    try {
      // 处理jsBridgeHelper发送的数据格式
      // 数据格式: [{"api":"receiveMessage","data":{"action":"login"},"callbackId":"...","nativeResponseFlag":false}]

      String jsonString;
      Map<String, dynamic> messageData;

      if (data is List && data.isNotEmpty) {
        // 如果是数组，取第一个元素
        var firstElement = data[0];
        if (firstElement is Map<String, dynamic>) {
          messageData = firstElement;
          jsonString = jsonEncode(messageData);
          QLog('${widget.pageTitle}: 解析数组数据: $messageData');
        } else if (firstElement is String) {
          jsonString = firstElement;
          messageData = jsonDecode(firstElement) as Map<String, dynamic>;
        } else {
          throw Exception('数组第一个元素类型不支持: ${firstElement.runtimeType}');
        }
      } else if (data is String) {
        // 如果已经是字符串，直接使用
        jsonString = data;
        messageData = jsonDecode(data) as Map<String, dynamic>;
      } else if (data is Map<String, dynamic>) {
        // 如果是Map，转换为JSON字符串
        messageData = data;
        jsonString = jsonEncode(messageData);
      } else {
        throw Exception('不支持的数据格式: ${data.runtimeType}');
      }

      // 检查是否是登录请求
      if (messageData['data'] is Map &&
          messageData['data']['action'] == 'login') {
        QLog('${widget.pageTitle}: 检测到登录请求，跳转到原生登录页面');
        return await _handleLoginRequest();
      }

      // 检查是否是登出请求
      if (messageData['data'] is Map &&
          messageData['data']['action'] == 'logout') {
        QLog('${widget.pageTitle}: 检测到登出请求，执行原生登出');
        return await _handleLogoutRequest();
      }

      // 使用现有的JSApi解析其他请求
      // JSApi.parseJsRequest期望的是字符串格式
      final result = await JSApi.parseJsRequest(jsonString);
      QLog('${widget.pageTitle}: JavaScript回调处理结果: $result');
      return result;
    } catch (e) {
      QLog('${widget.pageTitle}: JavaScript回调处理失败: $e');
      return null;
    }
  }

  /// 处理登录请求
  Future<Map<String, dynamic>> _handleLoginRequest() async {
    try {
      QLog('${widget.pageTitle}: 开始处理原生登录请求');

      // 首先尝试运营商一键登录
      final carrierLoginResult = await _tryCarrierLogin();

      if (carrierLoginResult['success'] == true) {
        QLog('${widget.pageTitle}: 运营商一键登录成功');

        // 获取token并注入到H5页面
        if (webViewController != null) {
          final token = await Global.getToken();
          if (token != null && token.isNotEmpty) {
            QLog('${widget.pageTitle}: 注入运营商登录token到H5页面');
            await webViewController!.evaluateJavascript(source: '''
              (function() {
                console.log('原生App: 运营商登录成功，注入token');
                // 将token存储到localStorage
                if (typeof(Storage) !== "undefined") {
                  localStorage.setItem('access_token', '$token');
                  console.log('Token已存储到localStorage');
                }
                
                // 触发登录成功事件，让H5页面刷新用户状态
                if (window.onNativeLoginSuccess && typeof window.onNativeLoginSuccess === 'function') {
                  window.onNativeLoginSuccess('$token');
                } else {
                  console.log('H5页面未定义onNativeLoginSuccess方法');
                  // 尝试刷新页面状态
                  if (window.location.reload) {
                    window.location.reload();
                  }
                }
              })();
            ''');
          }
        }

        return {'success': true, 'message': '运营商一键登录成功'};
      }

      // 运营商一键登录失败，跳转到手机验证码登录
      QLog('${widget.pageTitle}: 运营商一键登录失败: ${carrierLoginResult['message']}');
      QLog('${widget.pageTitle}: 跳转到手机验证码登录页面');

      return await _testPhoneVerificationLogin();

    } catch (e, stackTrace) {
      QLog('${widget.pageTitle}: 处理登录请求异常: $e');
      QLog('${widget.pageTitle}: 异常堆栈: $stackTrace');

      // 异常情况下也跳转到手机验证码登录
      return await _testPhoneVerificationLogin();
    }
  }

  /// 尝试运营商一键登录
  Future<Map<String, dynamic>> _tryCarrierLogin() async {
    try {
      QLog('${widget.pageTitle}: 开始尝试运营商一键登录');

      // 获取运营商登录服务实例
      final carrierService = CarrierLoginService.instance;

      // 先初始化服务（这会自动设置监听器）
      QLog('${widget.pageTitle}: 初始化运营商登录服务...');
      final initResult = await carrierService.initialize();
      if (!initResult) {
        QLog('${widget.pageTitle}: 运营商登录服务初始化失败');
        return {'success': false, 'message': '运营商登录服务初始化失败'};
      }

      // 创建登录逻辑实例来执行运营商登录
      final loginLogic = LoginLogic();

      // 调用运营商一键登录（这会显示授权页面）
      await loginLogic.carrierLogin();

      // 等待一段时间让用户完成登录操作
      // 这里我们等待最多15秒，如果用户在这期间完成登录，token会被保存
      QLog('${widget.pageTitle}: 等待用户完成运营商一键登录操作...');

      // 轮询检查登录状态，最多等待15秒（缩短等待时间）
      for (int i = 0; i < 15; i++) {
        await Future.delayed(const Duration(seconds: 1));

        // 检查是否获得了token
        final token = await Global.getToken();
        if (token != null && token.isNotEmpty) {
          QLog('${widget.pageTitle}: 运营商一键登录成功，获得token');
          return {'success': true, 'message': '运营商一键登录成功'};
        }

        // 检查是否有错误发生（通过检查当前页面是否还在运营商登录流程中）
        // 如果运营商登录已经失败，CarrierLoginService会自动跳转到手机验证码登录
        if (i >= 5) {
          QLog('${widget.pageTitle}: 运营商一键登录等待中，检查是否有错误...');

          // 如果等待时间超过5秒还没有结果，可能是网络问题或其他错误
          // 这时应该让CarrierLoginService的错误处理机制来处理
        }
      }

      // 15秒后仍未获得token，认为登录失败
      QLog('${widget.pageTitle}: 运营商一键登录超时，未获得token');
      return {'success': false, 'message': '登录超时或用户取消'};

    } catch (e) {
      QLog('${widget.pageTitle}: 运营商一键登录异常: $e');
      return {'success': false, 'message': '运营商一键登录异常: $e'};
    }
  }

  /// 手机验证码登录
  Future<Map<String, dynamic>> _testPhoneVerificationLogin() async {
    try {
      QLog('${widget.pageTitle}: 开始手机验证码登录流程');

      // 尝试跳转到登录页面，添加WebView标识
      dynamic result;
      try {
        result = await Get.toNamed(AppRoute.login,
            arguments: {'fromWebView': true, 'webViewTitle': widget.pageTitle});
      } catch (e) {
        QLog('${widget.pageTitle}: Get.toNamed失败，尝试使用Get.to: $e');
        // 如果Get.toNamed失败，尝试使用Get.to
        try {
          result = await Get.to(() => const LoginPage(), arguments: {
            'fromWebView': true,
            'webViewTitle': widget.pageTitle
          });
        } catch (e2) {
          QLog('${widget.pageTitle}: Get.to也失败: $e2');
          throw Exception('无法跳转到登录页面: $e2');
        }
      }

      QLog('${widget.pageTitle}: 登录页面返回结果: $result');
      QLog('${widget.pageTitle}: 结果类型: ${result.runtimeType}');

      if (result != null && result is Map && result['success'] == true) {
        QLog('${widget.pageTitle}: 手机验证码登录成功，token: ${result['token']}');

        // 只需要注入token到当前页面，不需要重新导航
        if (webViewController != null && result['token'] != null) {
          QLog('${widget.pageTitle}: 注入token到H5页面');
          await webViewController!.evaluateJavascript(source: '''
            (function() {
              console.log('原生App: 登录成功，注入token');
              // 将token存储到localStorage
              if (typeof(Storage) !== "undefined") {
                localStorage.setItem('access_token', '${result['token']}');
                console.log('Token已存储到localStorage');
              }
              
              // 触发登录成功事件，让H5页面刷新用户状态
              if (window.onNativeLoginSuccess && typeof window.onNativeLoginSuccess === 'function') {
                window.onNativeLoginSuccess('${result['token']}');
              } else {
                console.log('H5页面未定义onNativeLoginSuccess方法');
                // 尝试刷新页面状态
                if (window.location.reload) {
                  window.location.reload();
                }
              }
            })();
          ''');
        }

        return {'success': true, 'message': '手机验证码登录成功'};
      } else {
        QLog('${widget.pageTitle}: 手机验证码登录失败或取消，返回结果: $result');
        return {'success': false, 'message': '登录失败或取消'};
      }
    } catch (e, stackTrace) {
      QLog('${widget.pageTitle}: 手机验证码登录异常: $e');
      QLog('${widget.pageTitle}: 异常堆栈: $stackTrace');
      return {'success': false, 'message': '登录处理异常: $e'};
    }
  }

  /// 处理登出请求
  Future<Map<String, dynamic>> _handleLogoutRequest() async {
    try {
      QLog('${widget.pageTitle}: 开始处理原生登出请求');

      // 调用登出API
      final logoutResult = await _performLogout();

      if (logoutResult['success'] == true) {
        QLog('${widget.pageTitle}: 登出成功，准备刷新页面');

        // 登出成功后重新构建URL（不包含token）并刷新
        // await _buildInitialUrl();
        // await _safeReloadWebView();
        QLog('${widget.pageTitle}: 登出成功后通知H5导航到我的页面');
        await notifyH5ToNavigate({
          'action': 'navigate',
          'targetPage': '/pages/user/user',
          'forceRefresh': true,
          'fromNative': false,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });

        return {'success': true, 'message': '登出成功'};
      } else {
        QLog('${widget.pageTitle}: 登出失败: ${logoutResult['message']}');
        return {'success': false, 'message': logoutResult['message'] ?? '登出失败'};
      }
    } catch (e, stackTrace) {
      QLog('${widget.pageTitle}: 处理登出请求异常: $e');
      QLog('${widget.pageTitle}: 异常堆栈: $stackTrace');
      return {'success': false, 'message': '登出处理异常: $e'};
    }
  }

  /// 执行登出操作
  Future<Map<String, dynamic>> _performLogout() async {
    try {
      QLog('${widget.pageTitle}: 开始执行登出操作');

      // 1. 调用登出API
      try {
        QLog('${widget.pageTitle}: 调用登出API');

        // 获取clientId（如果有的话）
        String? clientId;
        try {
          // 这里可以从登录逻辑中获取clientId，或者从本地存储获取
          // 暂时使用简单的设备标识
          clientId = 'app_webview_${DateTime.now().millisecondsSinceEpoch}';
        } catch (e) {
          QLog('${widget.pageTitle}: 获取clientId失败: $e');
        }

        final result = await API.logoutApi(
          alias: 'app', // 客户端身份别名
          clientId: clientId, // 客户端身份ID
          // refreshKey 暂时不传，如果需要可以从本地存储获取
        );

        if (result.state == 200) {
          QLog('${widget.pageTitle}: 登出API调用成功');
        } else {
          QLog(
              '${widget.pageTitle}: 登出API调用失败: state=${result.state}, msg=${result.msg}');
          // 即使API失败，也继续清除本地数据
        }
      } catch (e) {
        QLog('${widget.pageTitle}: 登出API调用异常: $e');
        // 即使API异常，也继续清除本地数据
      }

      // 2. 清除本地token和用户信息
      QLog('${widget.pageTitle}: 清除本地token和用户信息');
      await Global.removeToken();
      await Global.removeUserInfo();

      QLog('${widget.pageTitle}: 本地数据清除完成');

      return {'success': true, 'message': '登出成功'};
    } catch (e) {
      QLog('${widget.pageTitle}: 执行登出操作异常: $e');
      return {'success': false, 'message': '登出操作异常: $e'};
    }
  }

  /// 构建初始URL
  Future<void> _buildInitialUrl() async {
    try {
      final token = await Global.getToken();
      String url = '${widget.baseUrl}${widget.pagePath}?type=app';

      if (token != null && token.isNotEmpty) {
        url += '&access_token=$token';
      }

      // 为社区页面添加默认的hash路径
      if (widget.pageTitle == '社区') {
        url += '#/';
      }
      QLog("页面名${widget.pageTitle}");

      setState(() {
        currentUrl = url;
      });

      QLog('${widget.pageTitle}: 初始URL构建完成: $url');
    } catch (e) {
      QLog('${widget.pageTitle}: 构建URL失败: $e');
      String fallbackUrl = '${widget.baseUrl}${widget.pagePath}?type=app';
      if (widget.pageTitle == '社区') {
        fallbackUrl += '#/';
      }
      setState(() {
        currentUrl = fallbackUrl;
      });
    }
  }

  /// 通知H5进行导航
  Future<void> notifyH5ToNavigate(Map<String, dynamic> navigationData) async {
    if (webViewController == null) {
      QLog('${widget.pageTitle}: WebView控制器不可用，无法通知H5导航');
      return;
    }

    if (_isNavigating) {
      QLog('${widget.pageTitle}: 正在导航中，跳过H5导航通知: $navigationData');
      return;
    }

    _isNavigating = true;

    try {
      final targetPage = navigationData['targetPage'] ?? '/pages/index/index';
      final fromNative = navigationData['fromNative'] ?? false;
      final previousIndex = navigationData['previousIndex'] ?? -1;
      final timestamp =
          navigationData['timestamp'] ?? DateTime.now().millisecondsSinceEpoch;

      QLog(
          '${widget.pageTitle}: 导航数据详情 - targetPage: $targetPage, fromNative: $fromNative, previousIndex: $previousIndex');

      // 获取当前token并构建最终的目标页面
      String finalTargetPage = targetPage;

      QLog(
          '${widget.pageTitle}: 通知H5导航到: $finalTargetPage, 来自原生: $fromNative, 上一个页面: $previousIndex');

      // 构建JavaScript代码，统一使用window.appNavigate
      // 确保布尔值正确传递到JavaScript
      final fromNativeStr = fromNative.toString();

      final jsCode = '''
        (function() {
          console.log('原生App: 通知H5导航');
          console.log('目标页面: $finalTargetPage');
          console.log('来自原生: $fromNativeStr');
          console.log('上一个页面索引: $previousIndex');
          console.log('时间戳: $timestamp');

          try {
            // 检查是否是从原生页面切换且目标页面包含完整URL
            var isFromNativeWithFullUrl = $fromNativeStr === 'true' && '$finalTargetPage'.startsWith('http');

            if (isFromNativeWithFullUrl) {
              // 从原生页面切换到我的页面，直接跳转到完整URL
              console.log('从原生页面切换，直接跳转到完整URL: $finalTargetPage');
              window.location.href = '$finalTargetPage';
            } else if (window.appNavigate && typeof window.appNavigate === 'function') {
              // H5页面间切换，使用appNavigate方法
              console.log('调用H5的appNavigate方法');
              window.appNavigate({
                targetPage: '$finalTargetPage',
                fromNative: $fromNativeStr,
                previousIndex: $previousIndex,
                timestamp: $timestamp
              });
            } else {
              console.error('H5未提供appNavigate方法，请在H5页面中实现window.appNavigate');
              // 降级到hash导航
              console.log('降级到hash导航');
              window.location.hash = '$finalTargetPage';
            }
          } catch (error) {
            console.error('H5导航失败:', error);
            // 异常情况下使用hash导航
            window.location.hash = '$finalTargetPage';
          }
        })();
      ''';

      try {
        final result =
            await webViewController!.evaluateJavascript(source: jsCode);
        QLog('${widget.pageTitle}: H5导航通知执行结果: $result');
      } catch (e) {
        QLog('${widget.pageTitle}: H5导航通知执行失败: $e');
      }
    } finally {
      _isNavigating = false;
    }
  }

  /// 处理登录成功后的导航
  Future<void> _handlePostLoginNavigation() async {
    if (_isNavigating) {
      QLog('${widget.pageTitle}: 正在导航中，跳过登录后导航');
      return;
    }

    try {
      QLog('${widget.pageTitle}: 处理登录成功后的导航');

      // 尝试获取MainTabLogic来确定当前应该显示的页面
      try {
        final mainTabLogic = Get.find<MainTabLogic>();
        final currentIndex = mainTabLogic.currentIndex.value;

        QLog('${widget.pageTitle}: 当前tab索引: $currentIndex');

        // 登录成功后，通知H5进行相应的页面跳转
        if (currentIndex == 3) {
          QLog('${widget.pageTitle}: 登录成功后通知H5导航到我的页面');
          await notifyH5ToNavigate({
            'action': 'navigate',
            'targetPage': '/pages/user/user',
            'forceRefresh': true,
            'fromNative': false,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
        } else if (currentIndex == 0) {
          QLog('${widget.pageTitle}: 登录成功后通知H5导航到社区首页');
          await notifyH5ToNavigate({
            'action': 'navigate',
            'targetPage': '/pages/index/index',
            'forceRefresh': true,
            'fromNative': false,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
        } else {
          QLog('${widget.pageTitle}: 当前不在H5导航页面，使用简单刷新');
          // 对于车辆和服务页面，使用简单的页面刷新
          await _safeReloadWebView();
        }
      } catch (e) {
        QLog('${widget.pageTitle}: 无法获取MainTabLogic，使用默认刷新: $e');
        // 如果无法获取MainTabLogic，使用简单刷新
        await _safeReloadWebView();
      }
    } catch (e) {
      QLog('${widget.pageTitle}: 处理登录后导航异常: $e');
      // 异常情况下使用简单刷新
      await _safeReloadWebView();
    }
  }

  /// 安全地重新加载WebView（防止重复加载）
  Future<void> _safeReloadWebView() async {
    if (_isNavigating) {
      QLog('${widget.pageTitle}: 正在导航中，跳过WebView重新加载');
      return;
    }

    if (webViewController != null && currentUrl != null) {
      QLog('${widget.pageTitle}: 安全重新加载WebView: $currentUrl');
      _isNavigating = true;
      try {
        await webViewController!
            .loadUrl(urlRequest: URLRequest(url: WebUri(currentUrl!)));
      } finally {
        _isNavigating = false;
      }
    }
  }

  /// 显示分享对话框
  void _showShareDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  '分享到',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildShareItem(
                      icon: Icons.person,
                      label: '微信好友',
                      onTap: () => _shareToWeChatSession(),
                    ),
                    _buildShareItem(
                      icon: Icons.group,
                      label: '朋友圈',
                      onTap: () => _shareToWeChatTimeline(),
                    ),
                    _buildShareItem(
                      icon: Icons.copy,
                      label: '复制链接',
                      onTap: () => _copyLink(),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
            ],
          ),
        );
      },
    );
  }

  /// 构建分享选项
  Widget _buildShareItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFFC70E2D),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// 分享到微信好友
  Future<void> _shareToWeChatSession() async {
    try {
      // 检查微信是否安装
      final bool isInstalled = await wxkit.instance.wxInstalled();
      if (!isInstalled) {
        _showMessage('请先安装微信');
        return;
      }

      // 使用wxkit分享链接到微信好友
      final result = await wxkit.instance.wechatShare({
        'type': 1, // 1表示分享链接
        'url': 'https://shanghailima.com/',
        'title': '利马科技',
        'desc': '欢迎使用利马科技应用',
        'platform': 'session', // 分享到好友
      });

      if (result == true) {
        _showMessage('分享成功');
      } else if (result == -1) {
        _showMessage('请先安装微信');
      } else {
        _showMessage('分享取消或失败');
      }
    } catch (e) {
      QLog('分享到微信好友失败: $e');
      _showMessage('分享失败，请稍后重试');
    }
  }

  /// 分享到微信朋友圈
  Future<void> _shareToWeChatTimeline() async {
    try {
      // 检查微信是否安装
      final bool isInstalled = await wxkit.instance.wxInstalled();
      if (!isInstalled) {
        _showMessage('请先安装微信');
        return;
      }

      // 使用wxkit分享链接到微信朋友圈
      final result = await wxkit.instance.wechatShare({
        'type': 1, // 1表示分享链接
        'url': 'https://shanghailima.com/',
        'title': '利马科技',
        'desc': '欢迎使用利马科技应用',
        'platform': 'timeline', // 分享到朋友圈
      });

      if (result == true) {
        _showMessage('分享成功');
      } else if (result == -1) {
        _showMessage('请先安装微信');
      } else {
        _showMessage('分享取消或失败');
      }
    } catch (e) {
      QLog('分享到微信朋友圈失败: $e');
      _showMessage('分享失败，请稍后重试');
    }
  }

  /// 复制链接
  Future<void> _copyLink() async {
    try {
      await Clipboard.setData(
          const ClipboardData(text: 'https://shanghailima.com/'));
      _showMessage('链接已复制到剪贴板');
    } catch (e) {
      QLog('复制链接失败: $e');
      _showMessage('复制失败');
    }
  }

  /// 创建 MOB 短链接并复制 - 已移除MobSDK
  Future<void> _createAndCopyShortLink() async {
    try {
      _showMessage('短链接功能已移除');
      QLog('MobSDK短链接功能已移除');
    } catch (e) {
      QLog('短链接功能已移除: $e');
      _showMessage('短链接功能已移除');
    }
  }

  /// 加载测试页面
  Future<void> _loadTestPage() async {
    try {
      if (webViewController != null) {
        // 加载本地测试页面
        await webViewController!.loadFile(assetFilePath: "assets/test_deeplink.html");
        _showMessage('测试页面已加载');
        QLog('${widget.pageTitle}: 已加载深度链接测试页面');
      } else {
        _showMessage('WebView 未初始化');
      }
    } catch (e) {
      QLog('加载测试页面失败: $e');
      _showMessage('加载测试页面失败');
    }
  }

  /// 测试自定义 scheme 处理
  Future<void> _testCustomScheme() async {
    try {
      // 模拟您提供的深度链接
      const testUrl = 'limavehicle://com.lima.scooter/11101859070967394304?params=QH5p7f%2BqzF06TMh5w9mAHPVNmmdd5Lx90BNKC848%2BNv7FGkbM64Or%2BptbgUib3u6%2FIhWh3vJTurPaXVlRhlibtGvESuR4TbTsQVKwmLYYoKfqTXCvG9G1MtrseIj%2FTHjoWEmDAcEaOuXZEG%2BkwY9JBpxsEDv1Qa9wwsX6N61LPZqjlhO5x57gwZcxOPkU2OYGUruAgn0HEHqebk6nNBKrhNgfwlP3aPS4Eyz1x3CoXott73w0A%2B8TjQZYfVVxBjasIZFDf3MAcabhWJqwT7LbxJwhHd8NEiphJx7YNqptQdFR%2F4mfz6gC492%2BXk7PC23s6Sjkju9UN7P1yuIugJBhWgYIEUwdY%2FsCxPHryqMmnzGTTPytrgauwTyBXXeZnTyq5erH9AMIpi3CyNuAQ5X2x%2FQzSM3JHAq8IhPsfG1ew2vY%2F3QvyqyhrS2GONF6HzZ';

      QLog('测试自定义 scheme: $testUrl');

      // MobSDK处理方法已移除
      _showMessage('深度链接处理功能已移除（MobSDK已移除）');
      QLog('MobSDK深度链接处理功能已移除');
    } catch (e) {
      QLog('测试自定义 scheme 失败: $e');
      _showMessage('测试失败: $e');
    }
  }

  /// 显示提示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFFC70E2D),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以支持AutomaticKeepAliveClientMixin
    
    if (currentUrl == null) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.pageTitle)),
        body: const Center(child: Text('正在初始化...')),
      );
    }

    QLog('${widget.pageTitle}: 构建WebView，URL: $currentUrl');

    return Scaffold(
      // 使用 Stack 让 AppBar 悬浮在 WebView 上方，不占用布局空间
      body: Stack(
        children: [
          // WebView 全屏显示，添加顶部空间避免被 AppBar 遮挡
          Padding(
            padding: EdgeInsets.only(
              top: 0, // 状态栏高度 + AppBar高度
            ),
            child: Column(
              children: [
                // 加载状态指示器
                if (isLoading)
                  Container(
                    width: double.infinity,
                    height: 3,
                    child: const LinearProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFC70E2D)),
                      backgroundColor: Color(0xFFE0E0E0),
                    ),
                  ),
                // WebView容器
                Expanded(
                  child: InAppWebView(
              initialUrlRequest: URLRequest(url: WebUri(currentUrl!)),
              onWebViewCreated: (controller) async {
                webViewController = controller;
                QLog('${widget.pageTitle}: WebView创建完成');

                // 设置JavaScript桥接
                await _setupJavaScriptBridge(controller);
              },
              onLoadStart: (controller, url) {
                QLog('${widget.pageTitle}: 页面开始加载: $url');
                if (mounted) {
                  setState(() {
                    isLoading = true;
                  });
                }
              },
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                final url = navigationAction.request.url.toString();
                QLog('${widget.pageTitle}: 拦截URL: $url');

                // 处理自定义 scheme - MobSDK已移除
                if (url.startsWith('limavehicle://')) {
                  QLog('${widget.pageTitle}: 检测到自定义scheme，但MobSDK处理功能已移除: $url');
                  // MobSDK.handleCustomSchemeUrl 已移除
                  QLog('${widget.pageTitle}: 自定义scheme处理功能已移除');
                  return NavigationActionPolicy.CANCEL; // 阻止WebView加载
                }

                // 允许其他URL正常加载
                return NavigationActionPolicy.ALLOW;
              },
              onLoadStop: (controller, url) async {
                QLog('${widget.pageTitle}: 页面加载完成: $url');

                // 页面加载完成后重新注入JavaScript桥接，确保路由切换后jsBridgeHelper仍然可用
                await _reinjectJavaScriptBridge(controller);

                // 更新加载状态
                if (mounted) {
                  setState(() {
                    isLoading = false;
                  });
                }

                // 通知页面加载状态变化
                if (widget.onLoadingChanged != null) {
                  widget.onLoadingChanged!(false);
                }
              },
              onReceivedError: (controller, request, error) {
                QLog(
                    '${widget.pageTitle}: 页面加载错误: ${request.url}, 错误类型: ${error.type}, 错误信息: ${error.description}');

                // 更新加载状态
                if (mounted) {
                  setState(() {
                    isLoading = false;
                  });
                }

                // 通知页面加载状态变化
                if (widget.onLoadingChanged != null) {
                  widget.onLoadingChanged!(false);
                }
              }),
                ),
              ],
            ),
          ),
          // 悬浮的 AppBar，置顶显示，不占用布局空间
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xFFC70E2D),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    offset: Offset(0, 2),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _activeInstances.remove(this); // 从活跃实例列表中移除
    _timeoutTimer?.cancel();
    
    // 如果不启用缓存，清理webview
    if (!widget.enableCache) {
      webViewController = null;
    }
    
    super.dispose();
  }
}
