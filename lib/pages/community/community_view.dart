import 'package:flutter/material.dart';
import 'package:lima/pages/old/web/web_page.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/pages/main_tab/main_tab_logic.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> {
  String? homeUrl;
  WebController? webController;
  MainTabLogic? mainTabLogic;
  DateTime? lastRefreshTime; // 记录上次刷新时间，实现冷却机制

  @override
  void initState() {
    super.initState();
    _buildUrl();
  }

  /// 构建URL，添加type=app参数，如果有access_token则也添加到URL参数中
  Future<void> _buildUrl() async {
    const baseUrl = Config.BASE_URL + '#/';

    try {
      final token = await Global.getToken();
      if (token != null && token.isNotEmpty) {
        homeUrl = '$baseUrl?type=app&access_token=$token';
        QLog('社区页面URL（带token）: $homeUrl');
      } else {
        homeUrl = '$baseUrl?type=app';
        QLog('社区页面URL（无token）: $homeUrl');
      }
    } catch (e) {
      QLog('获取token失败: $e');
      homeUrl = '$baseUrl?type=app';
    }

    if (mounted) {
      setState(() {});
    }
  }

  /// 处理H5页面的JavaScript回调
  Future<dynamic> _handleJsCallback(dynamic jsRequest) async {
    QLog('社区页面收到JS回调: $jsRequest');

    if (jsRequest is Map && jsRequest['action'] == 'login') {
      QLog('H5页面请求登录，跳转到原生登录页');
      // 跳转到登录页面
      final result = await Get.toNamed(AppRoute.login);
      QLog('登录页面返回结果: $result');

      // 如果登录成功，刷新页面
      if (result != null && result['success'] == true) {
        QLog('登录成功，刷新社区页面');
        await _buildUrl(); // 重新构建URL（带新的token）
        if (webController != null) {
          webController!.loadUrl(homeUrl!);
        }
        return {'success': true, 'message': '登录成功'};
      }

      return {'success': false, 'message': '登录失败或取消'};
    }

    return {'success': false, 'message': '未知请求'};
  }

  @override
  Widget build(BuildContext context) {
    if (homeUrl == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: SafeArea(
        child: WebPage(
          homeUrl: homeUrl!,
          onWebCreated: (controller) {
            webController = controller;
          },
          jsApiCallback: _handleJsCallback,
        ),
      ),
    );
  }
}
