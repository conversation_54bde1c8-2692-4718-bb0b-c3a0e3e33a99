import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lima/pages/simple_webview/simple_webview_page.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/common/config.dart';

class NewCommunityPage extends StatefulWidget {
  final int tabIndex;

  const NewCommunityPage({Key? key, required this.tabIndex}) : super(key: key);

  @override
  State<NewCommunityPage> createState() => _NewCommunityPageState();
}

class _NewCommunityPageState extends State<NewCommunityPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    String pagePath;
    String pageTitle;
    
    if (widget.tabIndex == 0) {
      pagePath = '/';
      pageTitle = '社区';
    } else if (widget.tabIndex == 4) {
      pagePath = '/#/pages/user/user';
      pageTitle = '我的';
    } else if (widget.tabIndex == 1) {
      pagePath = '/#/pages/shop/home/<USER>';
      pageTitle = '商城';
    }  else {
      pagePath = '/';
      pageTitle = '社区';
    }
    
    QLog("新社区页面: 构建页面，索引: ${widget.tabIndex}，路径: $pagePath");
    
    return SimpleWebViewPage(
      key: ValueKey('webview_${widget.tabIndex}'),
      baseUrl: Config.BASE_URL,
      pagePath: pagePath,
      pageTitle: pageTitle,
      enableCache: true,
    );
  }

  @override
  void dispose() {
    QLog('H5页面管理器: 开始清理资源');
    super.dispose();
  }
}