import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/global.dart';
import 'package:lima/pages/login/login_view.dart';
import 'package:lima/res/colors.dart';
import 'package:lima/routers/app_router.dart';
import 'package:lima/utils/q_log.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DemoPage extends StatefulWidget {
  const DemoPage({Key? key}) : super(key: key);

  @override
  State<DemoPage> createState() => _DemoPageState();
}

class _DemoPageState extends State<DemoPage> {
  String? userInfo;
  String? token;

  @override
  void initState() {
    super.initState();
    loadUserInfo();
  }

  Future<void> loadUserInfo() async {
    // 使用Global类的方法获取token和用户信息
    final tokenValue = await Global.getToken();

    final sp = await SharedPreferences.getInstance();
    final userInfoValue = sp.getString(Config.USER_INFO);

    setState(() {
      token = tokenValue;
      userInfo = userInfoValue;
    });

    // 调试日志
    QLog('Demo页面加载用户信息:');
    if (tokenValue != null) {
      EasyLoading.showSuccess('获取用户信息成功');
      QLog('Token: ${tokenValue.substring(0, tokenValue.length > 20 ? 20 : tokenValue.length)}...');
    } else {
      QLog('Token: null');
    }
    QLog('UserInfo: $userInfoValue');
  }

  Future<void> logout() async {
    await Global.removeToken();
    await Global.removeUserInfo();
    setState(() {
      token = null;
      userInfo = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: QText('登录功能演示', fontSize: 18.sp, fontWeight: FontWeight.bold, color: Colors.white,),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(color: Colors.white),
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户状态卡片
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: token != null
                    ? AppColors.success.withOpacity(0.1)
                    : AppColors.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: token != null ? AppColors.success : AppColors.error,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        token != null ? Icons.check_circle : Icons.error,
                        color:
                            token != null ? AppColors.success : AppColors.error,
                        size: 24.sp,
                      ),
                      SizedBox(width: 8.w),
                      QText(
                        token != null ? '已登录' : '未登录',
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color:
                            token != null ? AppColors.success : AppColors.error,
                      ),
                    ],
                  ),
                  if (token != null) ...[
                    SizedBox(height: 16.h),
                    QText(
                      'Token:',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: QText(
                        token ?? '',
                        fontSize: 12.sp,
                        color: Colors.grey[800],
                        maxLines: 3,
                      ),
                    ),
                  ],
                  if (userInfo != null) ...[
                    SizedBox(height: 16.h),
                    QText(
                      '用户信息:',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: QText(
                        userInfo ?? '',
                        fontSize: 12.sp,
                        color: Colors.grey[800],
                        maxLines: 5,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            SizedBox(height: 40.h),

            // 登录按钮
            SizedBox(
              width: double.infinity,
              height: 48.h,
              child: ElevatedButton(
                onPressed: () async {
                  final result = await Get.toNamed(AppRoute.login);
                  if (result == true) {
                    loadUserInfo();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 0,
                ),
                child: QText(
                  '去登录',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),

            SizedBox(height: 16.h),

            // Tab导航演示按钮
            SizedBox(
              width: double.infinity,
              height: 48.h,
              child: ElevatedButton(
                onPressed: () {
                  Get.toNamed(AppRoute.tabDemo);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 0,
                ),
                child: QText(
                  'Tab导航演示',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),

            SizedBox(height: 16.h),
            // 操作按钮
            if (token != null) ...[
              // 退出登录按钮
              SizedBox(
                width: double.infinity,
                height: 48.h,
                child: ElevatedButton(
                  onPressed: logout,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 0,
                  ),
                  child: QText(
                    '退出登录',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),

              SizedBox(height: 16.h),

              // 刷新用户信息按钮
              SizedBox(
                width: double.infinity,
                height: 48.h,
                child: OutlinedButton(
                  onPressed: loadUserInfo,
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppColors.primary),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: QText(
                    '刷新用户信息',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],

            SizedBox(height: 40.h),

            // 说明文字
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  QText(
                    '功能说明：',
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                  SizedBox(height: 8.h),
                  QText(
                    '• 支持手机号+验证码登录\n• 支持微信登录\n• 支持苹果登录（仅iOS）\n• 自动保存登录状态\n• 支持退出登录',
                    fontSize: 12.sp,
                    color: Colors.blue[700],
                    lineHeight: 1.5,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
