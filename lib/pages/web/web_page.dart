import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/jsapi.dart';
import '../../../utils/jsbridge/message.dart';
import '../../../utils/jsbridge/native_bridge_helper.dart';
import '../../../utils/jsbridge/native_bridge_impl.dart';
import '../../../utils/q_log.dart';

class WebPage extends StatefulWidget {
  final String homeUrl;
  final Function(WebController webController)? onWebCreated;
  final Function(String? loadUrl)? onLoadFinished;
  final List<Map>? cookies;
  final dynamic Function(dynamic jsRequest)? jsApiCallback;

  WebPage({
    Key? key,
    required this.homeUrl,
    this.onLoadFinished,
    this.onWebCreated,
    this.cookies,
    this.jsApiCallback,
  }) : super(key: key);

  @override
  _WebPageState createState() => _WebPageState();
}

class _WebPageState extends State<WebPage> {
  var content = '''
  <!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    </head>
    <body>
        <h1>JavaScript Handlers</h1>
        <script>
            window.addEventListener("flutterInAppWebViewPlatformReady", function(event) {
                window.flutter_inappwebview.callHandler('receiveMessage')
                  .then(function(result) {
                    // print to the console the data coming
                    // from the Flutter side.
                    console.log(JSON.stringify(result));
                    
                    window.flutter_inappwebview
                      .callHandler('receiveMessage', 1, true, ['bar', 5], {foo: 'baz'}, result);
                });
            });
        </script>
    </body>
</html>
  ''';

  final scriptReceive = UserScript(source: '''
function receiveMessage(jsonStr) {
    if(jsonStr !== undefined && jsonStr !== "") {
        let data = JSON.parse(JSON.stringify(jsonStr));
        window.jsBridgeHelper.receiveMessage(data);
    }
}
''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

  Future<String> get jsAsserts async =>
      rootBundle.loadString('assets/jsBridgeHelper.js');

  ///webview 控制器

  InAppWebViewController? controller;

  bool showError = false;

  ///向外拓展的controller
  WebController? myWebController;

  WebUri? runningUrl;

  CookieManager cookieManager = CookieManager.instance();

  @override
  void initState() {
    super.initState();
    ///关闭webview调试日志
    PlatformInAppWebViewController.debugLoggingSettings.enabled = false;
  }

  Future webviewCreated(InAppWebViewController c) async {
    controller = c;
    
    controller?.addJavaScriptHandler(
        handlerName: 'receiveMessage', callback: callback);

    /// 设置cookie
    if (widget.cookies != null) {
      for (var i = 0; i < widget.cookies!.length; i++) {
        await cookieManager.setCookie(
          url: WebUri(widget.homeUrl),
          name: widget.cookies![i]['key'],
          value: '${widget.cookies![i]['value']}',
        );
      }
    }

    await controller?.addUserScript(userScript: scriptReceive);

    var bridgeHelper = UserScript(source: '''
    window.jsBridgeHelper = ${await jsAsserts};
    ''', injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START);

    await controller?.addUserScript(userScript: bridgeHelper);

  }

  void webviewReload() async {
    await controller?.clearHistory();
    controller?.loadUrl(urlRequest: URLRequest(url: runningUrl));
  }

  ///处理消息回调
  dynamic callback(List<dynamic> arguments) async {
    for (var element in arguments) {
      QLog('$element', type: LogInfoType.app);
      if (!NativeBridgeHelper.parseReceiveMessage(element)) {
        var res = await JSApi.parseJsRequest(element);
        return res;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(widget.homeUrl)),
      // initialData: InAppWebViewInitialData(data: content),
      onLongPressHitTestResult: (c, r) {
        ///禁止长按弹出浏览器默认menu的方式就是在webPage外面拦截长按
        ///
        QLog('禁止长按 =>  onLongPressHitTestResult, $r',
            type: LogInfoType.webview);
      },
      initialSettings: InAppWebViewSettings(
        disableDefaultErrorPage: true,
        verticalScrollBarEnabled: false,
        horizontalScrollBarEnabled: false,
        supportZoom: false,
        // 减少CORS相关错误的设置
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        allowsInlineMediaPlayback: true,
        // 禁用一些可能导致CORS错误的功能
        javaScriptCanOpenWindowsAutomatically: false,
      ),
      onWebViewCreated: (c) {
        QLog('onWebViewCreated', type: LogInfoType.webview);
        webviewCreated(c);
        myWebController = WebController(c);
        widget.onWebCreated?.call(myWebController!);
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        QLog('shouldOverrideUrlLoading $navigationAction',
            type: LogInfoType.webview);
        var uri = navigationAction.request.url ?? Uri();
        if (!["http", "https", "file", "chrome", "data", "javascript", "about"]
            .contains(uri.scheme)) {
          if (await canLaunchUrl(uri)) {
            // Launch the App
            await launchUrl(
              uri,
            );
            // and cancel the request
            return NavigationActionPolicy.CANCEL;
          }
        }

        return NavigationActionPolicy.ALLOW;
      },
      onLoadStart: (controller, url) {
        QLog('正在加载 $url', type: LogInfoType.webview);
      },
      onLoadStop: (controller, url) async {
        QLog('url加载完成: ${url?.uriValue.toString()}', type: LogInfoType.webview);
        widget.onLoadFinished?.call(url?.uriValue.toString());
      },
      onReceivedError: (controller, resourceRequest, webResourceError) async {
        QLog(
            'onReceivedError: ${resourceRequest.url} -- ${webResourceError.description}',
            type: LogInfoType.webview);
        var isForMainFrame = resourceRequest.isForMainFrame ?? false;
        if (!isForMainFrame ||
            (defaultTargetPlatform == TargetPlatform.iOS &&
                webResourceError.type == WebResourceErrorType.CANCELLED)) {
          return;
        }
        runningUrl = resourceRequest.url;
        controller.loadData(data: await errorHtml());
      },
      onConsoleMessage: (controller, consoleMessage) {
        // 过滤掉CORS和Sentry相关的错误信息
        final message = consoleMessage.message;
        if (!message.contains('CORS') &&
            !message.contains('sentry') &&
            !message.contains('Access-Control-Allow-Origin')) {
          QLog(message, type: LogInfoType.web);
        }

        if (message.contains('blank:reload')) {
          webviewReload();
        }
      },
    );
  }

  Widget EmptyWidget(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('> …… < 暂无数据'),
            TextButton(
              onPressed: () {
                webviewReload();
              },
              child: Text('重新加载'),
            ),
          ],
        ),
      ),
    );
  }

  Future<String> errorHtml() async => '''
   <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
              <meta http-equiv="X-UA-Compatible" content="ie=edge">
              <style>
              ${await InAppWebViewController.tRexRunnerCss}
              </style>
              <style>
              .interstitial-wrapper {
                  box-sizing: border-box;
                  font-size: 1em;
                  line-height: 1.6em;
                  margin: 0 auto 0;
                  max-width: 600px;
                  width: 100%;
                  height: 100%;
                 min-height: 100vh;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
              }
              </style>
              <script>
                function refresh() {
                  console.log('blank:reload');
                }
              </script>
          </head>
          <body>
              <div class="interstitial-wrapper" onclick="refresh()">
                <p> > …… < 似乎与网络失去了连接</p>
                <p>点击屏幕刷新</p>
              </div>
          </body>
  ''';
}

class WebController with NativeBridgeImpl {
  InAppWebViewController? controller;

  WebController(InAppWebViewController c) {
    controller = c;
  }

  void reload() => controller?.reload();

  Future? loadUrl(String url) async {
    await controller?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    // controller?.clearHistory();
  }

  Future sendMessageToJs(String api,
      {dynamic data, bool? nativeResponseFlag}) async {
    Message message = Message(
        api: api, data: data, nativeResponseFlag: nativeResponseFlag ?? false);
    return NativeBridgeHelper.sendMessage(message, this).future;
  }

  @override
  void runJavaScript(String javaScriptString) {
    controller?.evaluateJavascript(source: javaScriptString);
  }

  @override
  Future<Object> runJavaScriptReturningResult(String javaScriptString) {
    throw UnimplementedError();
  }
  void dispose(){
    controller?.dispose();
  }
}
