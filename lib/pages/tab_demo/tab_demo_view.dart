import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/components/q_text.dart';
import 'package:lima/res/colors.dart';
import 'package:lima/routers/app_router.dart';

class TabDemoPage extends StatelessWidget {
  const TabDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: QText(
          'Tab导航演示',
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.tab,
              size: 80.sp,
              color: AppColors.primary,
            ),
            SizedBox(height: 30.h),
            QText(
              '四个Tab页面已创建完成',
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
            ),
            SizedBox(height: 20.h),
            QText(
              '包含：社区、车辆、服务、我的',
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
            SizedBox(height: 40.h),
            ElevatedButton(
              onPressed: () {
                Get.toNamed(AppRoute.mainTab);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 15.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25.w),
                ),
              ),
              child: QText(
                '查看Tab导航',
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 20.h),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 40.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  QText(
                    '功能特点：',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  SizedBox(height: 10.h),
                  _buildFeatureItem('✅ 四个主要Tab页面'),
                  _buildFeatureItem('✅ 精美的"我的"页面设计'),
                  _buildFeatureItem('✅ 渐变背景和用户信息展示'),
                  _buildFeatureItem('✅ 统计数据和功能菜单'),
                  _buildFeatureItem('✅ 响应式布局设计'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 3.h),
      child: QText(
        text,
        fontSize: 14.sp,
        color: Colors.grey[700],
      ),
    );
  }
}
