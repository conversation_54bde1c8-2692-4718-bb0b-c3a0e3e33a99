import 'dart:convert';
import 'dart:io';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:lima/utils/gt_push.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/crypto_test.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'common/config.dart';

class Global {
  /// 是否授权
  static bool appPermissionGranted = false;
  static String wxappId = 'wxd92e6f7ab4454e9d';
  static String wxappSecret = '87681e9868ee3d3ca66b14ebfed6f122';
  static String wxappUniversalLink =
      'https://a5fd977ed5a03f35d04b8ecccff8e8b0.share2dlink.com/';
  static String qqappId = '102000225';
  static String qqappkey = 'FQO9h8ORKzQdcNyL';

  ///本地广告路径
  static String localAdvPath = '';

  ///广告缓存天数
  static final int advCacheDay = 7;
  static String prefScanResult = 'PREF_SCAN_RESULT';

  /// lima_token内存缓存
  static String? _limaTokenCache;

  /// init
  static Future init() async {
    // 测试密码加密功能
    CryptoTest.testPasswordEncryption();

// android 状态栏为透明的沉浸
    if (Platform.isAndroid) {
      SystemUiOverlayStyle systemUiOverlayStyle =
          SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);

      ///初始化webview Android配置
      InAppWebViewController.setWebContentsDebuggingEnabled(false);
      // AndroidInAppWebViewController.setWebContentsDebuggingEnabled(true);
    }

    /// 初始化API服务
    // ApiService.init();

    /// update url
    await updateWebSite();

    /// 三方库初始化
    thirdSdkInit();

    ///本地广告资源
    _checkLocalAdv();
  }


  /// 检查并初始化第三方SDK（应用启动时调用）
  static Future<void> checkAndInitThirdPartySdk() async {
    QLog("🔍 检查第三方SDK初始化状态...");
    
    SharedPreferences pref = await SharedPreferences.getInstance();
    appPermissionGranted = pref.getBool(Config.APP_PERMISSION) ?? false;
    
    QLog("📋 隐私协议状态: ${appPermissionGranted ? '已同意' : '未同意'}");
    
    if (appPermissionGranted) {
      QLog("✅ 用户已同意隐私协议，开始初始化第三方SDK");
      await initThirdPartySdkAfterConsent();
    } else {
      QLog("⏸️ 用户未同意隐私协议，等待用户同意后初始化");
    }
  }

  /// 立即初始化第三方SDK（在用户同意隐私政策后调用）
  static Future<void> initThirdPartySdkAfterConsent() async {
    QLog("🚀 用户同意隐私政策，开始初始化第三方SDK...");
    
    try {
      // 更新权限状态
      appPermissionGranted = true;
      
      // 初始化高德地图
      QLog("📍 初始化高德地图定位服务");
      AMapFlutterLocation.updatePrivacyAgree(true);
      AMapFlutterLocation.updatePrivacyShow(true, true);
      
      // 初始化个推推送服务
      QLog("📱 初始化个推推送服务");
      final pushResult = await GtPush().initWithRetry();
      if (pushResult) {
        QLog("✅ 个推推送服务初始化调用成功");
      } else {
        QLog("⚠️ 个推推送服务初始化调用失败，将自动重试");
      }
      
      // 注意：MobSDK 的初始化现在在用户同意隐私政策后进行
      // 可以在这里添加其他第三方SDK的初始化
      
      QLog("✅ 第三方SDK初始化完成");
      
    } catch (e) {
      QLog("❌ 第三方SDK初始化异常: $e");
    }
  }

  ///三方库初始化（保留原有方法，现在调用新的方法）
  static void thirdSdkInit() async {
    await checkAndInitThirdPartySdk();
  }

  /// 是否同意了隐私权限说明
  static Future<bool> getPrivateProtocolPermission() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    return pref.getBool(Config.APP_PERMISSION) ?? false;
  }

  static Future<void> setPrivateProtocolPermission(bool b) async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setBool(Config.APP_PERMISSION,b);
  }

  static Future<String?> getUserPhone() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    var res = pref.getString(Config.USER_INFO);
    if(res != null){
      return jsonDecode(res)['phone'];
    }
    return res;
  }

  static Future<String?> getToken() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    return pref.getString(Config.TOKEN);
  }

  /// 保存access_token
  static Future<bool> setToken(String token) async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    QLog('保存access_token: $token');
    return await pref.setString(Config.TOKEN, token);
  }

  static Future<String?> getGTID() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    return pref.getString(Config.GT_CID);
  }

  static Future removeToken() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    return await pref.remove(Config.TOKEN);
  }

  /// 保存lima_token
  static Future<bool> setLimaToken(String token) async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    _limaTokenCache = token; // 更新内存缓存
    QLog('保存lima_token: $token');
    return await pref.setString(Config.LIMA_TOKEN, token);
  }

  /// 获取lima_token
  static Future<String?> getLimaToken() async{
    // 优先使用内存缓存
    if (_limaTokenCache != null && _limaTokenCache!.isNotEmpty) {
      return _limaTokenCache;
    }

    SharedPreferences pref = await SharedPreferences.getInstance();
    _limaTokenCache = pref.getString(Config.LIMA_TOKEN);
    return _limaTokenCache;
  }

  /// 流程图要求的get_lima_token方法
  static Future<String?> get_lima_token() async{
    return await getLimaToken();
  }

  /// 清除lima_token
  static Future removeLimaToken() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    _limaTokenCache = null; // 清除内存缓存
    return await pref.remove(Config.LIMA_TOKEN);
  }

  /// 清除所有token
  static Future removeAllTokens() async {
    await removeToken(); // 清除access_token
    await removeLimaToken(); // 清除lima_token
    QLog('✅ 所有token已清除');
  }

  static Future removeUserInfo() async{
    SharedPreferences pref = await SharedPreferences.getInstance();
    return await pref.remove(Config.USER_INFO);
  }

  
  static Future saveBleScanResult(List list) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    if(list.isNotEmpty) {
      pref.setString(prefScanResult, jsonEncode(list));
    }
  }

  static Future<List> getBleScanResult() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String? r = pref.getString(prefScanResult);
    if (r == null) {
      return [];
    }
    return jsonDecode(r);
  }


  ///检查本地广告缓存
  static Future _checkLocalAdv() async{
    var dir = await getApplicationDocumentsDirectory();
    Directory advDir = Directory('${dir.path}/adv');
    if(advDir.existsSync()) {
      var listSync = advDir.listSync();
      DateTime currentFileModified = DateTime.now().subtract(Duration(days: advCacheDay));
      for (var element in listSync) {
        if (element is File) {
          QLog('本地缓存的广告 =>${element.path}');
          if(element.lastModifiedSync().isAfter(currentFileModified)){
            localAdvPath = element.path;
            currentFileModified = element.lastModifiedSync();
          }
        }
      }
    }
  }


  static Future updateWebSite({String? url}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    if (url == null || url.isEmpty) {
      var res = pref.getString('appWebsite');
      if (res != null) {
        Config.BASE_WEBSITE = res;
      }
    } else {
      await pref.setString('appWebsite', url);
      Config.BASE_WEBSITE = url;
    }
  }
}
