
import 'package:get/get.dart';
import 'package:lima/pages/demo/demo_view.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/pages/login/login_view.dart';
import 'package:lima/pages/main_tab/main_tab_view.dart';
import 'package:lima/pages/phone_input/phone_input_view.dart';
import 'package:lima/pages/tab_demo/tab_demo_view.dart';
import 'package:lima/pages/old/qrscan/qrscan_view.dart';
import 'package:lima/pages/old/splash/splash_view.dart';
import 'package:lima/pages/verification/verification_view.dart';
import 'package:lima/pages/webview/webview_view.dart';
import 'package:lima/pages/coming_soon/coming_soon_view.dart';
import 'package:lima/pages/profile_edit/profile_edit_view.dart';
import 'package:lima/pages/forgot_password/forgot_password_view.dart';
import 'package:lima/pages/password_login/password_login_view.dart';
import 'package:lima/pages/bind_phone/bind_phone_view.dart';

class AppRoute{
  static const String index = '/index';
  static const String home = '/home';
  static const String demo = '/demo';
  static const String login = '/login';
  static const String mainTab = '/main-tab';
  static const String tabDemo = '/tab-demo';
  static const String scanQr = '/scanQr';
  static const String phoneInput = '/phone-input';
  static const String verification = '/verification';
  static const String webview = '/webview';
  static const String comingSoon = '/coming_soon';
  static const String profileEdit = '/profile_edit';
  static const String forgotPassword = '/forgot_password';
  static const String passwordLogin = '/password_login';
  static const String bindPhone = '/bind_phone';

  static final pages=[
    GetPage(
      name: index,
      page: ()=> SplashPage(),
    ),
    GetPage(
      name: home,
      page: ()=> HomePage(),
      // transition:Transition.downToUp,
      // transitionDuration: const Duration(milliseconds: 500),
      // curve: Curves.easeInOut,
      preventDuplicates: true
    ),
    GetPage(
      name: demo,
      page: ()=> DemoPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: login,
      page: ()=> LoginPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: mainTab,
      page: ()=> const MainTabPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: tabDemo,
      page: ()=> const TabDemoPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: scanQr,
      page: ()=> QrScanPage(),
    ),
    GetPage(
      name: phoneInput,
      page: () => const PhoneInputPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: verification,
      page: ()=> const VerificationPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: webview,
      page: ()=> WebviewPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: comingSoon,
      page: ()=> const ComingSoonPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: profileEdit,
      page: ()=> const ProfileEditPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: forgotPassword,
      page: ()=> const ForgotPasswordPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: passwordLogin,
      page: ()=> const PasswordLoginPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bindPhone,
      page: ()=> const BindPhonePage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    )
  ];


}