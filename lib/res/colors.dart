import 'dart:ui';

class AppColors {
  static const Color color_FFFFFFFF = Color(0xFFFFFFFF);
  static const Color color_FFF8F8FA = Color(0xFFF8F8FA);
  static const Color color_FF1D1D1D = Color(0xFF1D1D1D);
  static const Color color_FF848484 = Color(0xFF848484);
  static const Color color_FF2B6BFF = Color(0xFF2B6BFF);
  static const Color color_00FFFFFF = Color(0x00FFFFFF);
  static const Color dividerColor = Color(0xFFE8E8E8);
  static const Color color_FFF4F6FA = Color(0xFFF4F6FA);
  static const Color color_FFC5C7C9 = Color(0xFFC5C7C9);
  /// 332B6BFF 蓝色 透明度 33
  static const Color color_332B6BFF = Color(0x332B6BFF);

  // 新增登录页面需要的颜色
  static const Color primary = Color(0xFFC70E2D);
  static const Color primaryDark = Color(0xFFB00C26);
  static const Color secondary = Color(0xFF50C878);
  static const Color accent = Color(0xFFFF6B35);

  static const Color background = Color(0xFFF8F9FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFE53E3E);
  static const Color warning = Color(0xFFED8936);
  static const Color success = Color(0xFF38A169);
  static const Color info = Color(0xFF3182CE);

  static const Color textPrimary = Color(0xFF2D3748);
  static const Color textSecondary = Color(0xFF4A5568);
  static const Color textTertiary = Color(0xFF718096);
  static const Color textDisabled = Color(0xFFA0AEC0);

  static const Color border = Color(0xFFE2E8F0);
  static const Color divider = Color(0xFFEDF2F7);

}
