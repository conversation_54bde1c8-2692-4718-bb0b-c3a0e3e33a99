import 'dart:async';
class Mutex {
  final StreamController _controller = StreamController.broadcast();
  int issued = 0;

  int execute = 0;

  Future<bool> take() async {
    int mine = issued;
    issued++;
    while (mine != execute) {
      await _controller.stream.first;
    }
    return true;
  }

  Future<bool> give() async {
    execute++;
    _controller.add(null);
    return false;
  }
}

class MutexFactory {
  static final Map<String, Mutex> _all = {};

  static Mutex getMutexForKey(String key) {
    _all[key] ??= Mutex();
    return _all[key]!;
  }
}

/// 工具集合
class Utils{

  Utils._();

  /// "" 和 null 都认为是空字符串
  static bool StringIsEmpty(dynamic str) {
    if (str == null) {
      return true;
    }
    return '$str'.trim().isEmpty;
  }

  static bool StringEqual(dynamic str1, dynamic str2) {
    if (str1 == null || str2 == null) {
      return false;
    }
    if (str1 is! String || str2 is! String) {
      return false;
    }
    return '$str1'.trim().toUpperCase() == '$str2'.trim().toUpperCase();
  }
}