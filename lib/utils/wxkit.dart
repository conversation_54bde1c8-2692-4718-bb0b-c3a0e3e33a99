import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/utils/q_log.dart';
import 'package:wechat_kit/wechat_kit.dart';

import 'http/request.dart';

class wxkit {
  wxkit._(){
    registerWx();
  }

  static wxkit instance = wxkit._();

  factory wxkit() {
    return instance;
  }

  Future registerWx() async{
    return WechatKitPlatform.instance.registerApp(
        appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
  }

  /// 获取默认分享缩略图
  Future<Uint8List?> _getDefaultThumbData() async {
    try {
      final ByteData data = await rootBundle.load('assets/images/logo.jpeg');
      return data.buffer.asUint8List();
    } catch (e) {
      QLog('获取默认缩略图失败: $e');
      return null;
    }
  }

  StreamSubscription<WechatResp>? _wxSubs;

  ///微信登录
  Future wxAuth() async {
    // WechatKitPlatform.instance.registerApp(
    //     appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
    var authMap = <String, dynamic>{
      'type': 1,
    };
    if(await WechatKitPlatform.instance.isInstalled() == false){
      return -1;
    }

    Completer completer = Completer();
    _wxSubs?.cancel();
    _wxSubs = null;
    _wxSubs = WechatKitPlatform.instance.respStream().listen((resp) async {
      QLog('completer.isCompleted: ${completer.isCompleted}');
      if (completer.isCompleted) {
        return;
      }
      if (resp is WechatAuthResp) {
        WechatAuthResp _authResp = resp;
        if (_authResp.isSuccessful) {
          ///通过CODE获取 access_token
          var res = await Request().dio.get(
              'https://api.weixin.qq.com/sns/oauth2/access_token',
              queryParameters: {
                'appid': Global.wxappId,
                'secret': Global.wxappSecret,
                'code': _authResp.code,
                'grant_type': 'authorization_code',
              }).catchError((e) {
            print(e);
            if (!completer.isCompleted) {
              completer.completeError(e);
            }
          });
          print('STEP1:  ${res.data}');
          if (res.data == null) {
            completer.complete(null);
            return;
          }
          var resData = jsonDecode(res.data);
          if (resData['openid'] != null) {
            authMap['wxOpenid'] = resData['openid'];
            authMap['wxUnionid'] = resData['unionid'];

            ///通过access_token获取用户信息
            res = await Request().dio.get(
                'https://api.weixin.qq.com/sns/userinfo',
                queryParameters: {
                  'access_token': resData['access_token'],
                  'openid': resData['openid'],
                }).catchError((e) {
              print(e);
              if (!completer.isCompleted) {
                completer.completeError(e);
              }
            });
            print('STEP2 userInfo:  $res');
            if (res.data != null) {
              resData = jsonDecode(res.data);
              authMap['nickname'] = resData['nickname'];
              authMap['img'] = resData['headimgurl'];
              completer.complete(authMap);
            } else {
              completer.complete(null);
            }
          } else {
            completer.complete(null);
          }
        } else {
          completer.complete(null);
        }
      }
    }, onError: (e) {
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(e);
    });
    WechatKitPlatform.instance.auth(
      scope: <String>[WechatScope.kSNSApiUserInfo],
      state: 'lima_auth_wx',
    ).catchError((e) {
      print(e);
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(e);
    });
    return completer.future.whenComplete(() {
      _wxSubs?.cancel();
      _wxSubs = null;
    });
  }

  Future wxInstalled() async {
    return await WechatKitPlatform.instance.isInstalled();
  }

  ///微信分享
  ///type: 0: 分享图片；1：分享链接
  Future wechatShare(dynamic data) async {
    if(data ==null || data is! Map){
      return null;
    }

    if(await WechatKitPlatform.instance.isInstalled() == false){
      return -1;
    }
    // var img = 'http://pic28.photophoto.cn/20130818/0020033143720852_b.jpg';
    var type = data['type'] ?? 0;
    String url = data['url'] ?? '';
    String title = data['title'] ?? '';
    String desc = data['desc'] ?? '';
    var img = '';
    if (data['img'] != null) {
      img = data['img'];
    }
    int scene = WechatScene.kSession;
    if (data['platform'] == 'timeline') {
      scene = WechatScene.kTimeline;
    }
    Completer completer = Completer();
    _wxSubs?.cancel();
    _wxSubs = null;
    _wxSubs = WechatKitPlatform.instance.respStream().listen((resp) {
      QLog(jsonEncode(resp.toJson()));
      if(resp is WechatShareMsgResp){
        completer.complete(true);
      }
    }, onError: (e) {
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(e);
    });
    // await WechatKitPlatform.instance.registerApp(
    //     appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
    if (type == 0) {
      String? path = await API.downloadShareFile(path: img);
      if (path == null) {
        completer.complete(null);
      } else {
        WechatKitPlatform.instance
            .shareImage(
          scene: scene,
          imageUri: Uri.file(path),
        )
            .catchError((e) {
          print(e);
          if (completer.isCompleted) {
            return;
          }
          completer.completeError(e);
        });
      }
    } else {
      if (url.isEmpty) {
        completer.complete(null);
      } else {
        // 获取默认缩略图
        final Uint8List? thumbData = await _getDefaultThumbData();

        WechatKitPlatform.instance
            .shareWebpage(
              scene: scene,
              webpageUrl: url,
              title: title,
              description: desc,
              thumbData: thumbData,
            )
            .catchError((e) {
          print(e);
          if (completer.isCompleted) {
            return;
          }
          completer.completeError(e);
        });
      }
    }

    return completer.future.whenComplete(() {
      _wxSubs?.cancel();
      _wxSubs = null;
    });
  }

  Future wxPay(dynamic data) async {
    if(data ==null || data is! Map){
      return null;
    }
    // data = {
    //   'package': 'Sign=WXPay',
    //   'appid': 'wxd92e6f7ab4454e9d',
    //   'sign': '53A1AA4A5CFE4AC7D3E8D743875A904C',
    //   'partnerid': '1494720242',
    //   'prepayid': 'wx1515290927579635aeb1d5e184a4c80000',
    //   'noncestr': 't3xx62wUabFOcnp5qBBqSYBhdjlvpWFa',
    //   'timestamp': 1705303755
    // };

    Completer completer = Completer();
    _wxSubs?.cancel();
    _wxSubs = null;
    _wxSubs =
        WechatKitPlatform.instance.respStream().listen((resp) {
          if (completer.isCompleted) {
            return;
          }
          if (resp is WechatPayResp) {
            QLog(jsonEncode(resp.toJson()));
            //{errorCode: -2, errorMsg: null, returnKey: null}
            completer.complete(resp.errorCode);
          }
        }, onError: (e) {
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(e);
    });
    // await WechatKitPlatform.instance.registerApp(
    //     appId: Global.wxappId, universalLink: Global.wxappUniversalLink);

    WechatKitPlatform.instance.pay(
      appId: '${data['appid']}',
      partnerId: '${data['partnerid']}',
      prepayId: '${data['prepayid']}',
      package: '${data['package']}',
      nonceStr: '${data['noncestr']}',
      timeStamp: '${data['timestamp']}',
      sign: '${data['sign']}',
    ).catchError((e){
      print(e);
      if (completer.isCompleted) {
        return;
      }
      completer.completeError(e);
    });
    return completer.future.whenComplete(() {
      _wxSubs?.cancel();
      _wxSubs = null;
    });
  }

/// --- fluwx 的 wxKit
// FluwxCancelable? _fluwxCancelable;
//
// ///微信登录
// Future wxAuth() async {
//   Completer completer = Completer();
//   _fluwxCancelable?.cancel();
//   _fluwxCancelable = null;
//   var authMap =<String,dynamic> {
//     'type': 1,
//   };
//
//   Fluwx fluwx = Fluwx();
//   await fluwx.registerApi(
//       appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
//   _fluwxCancelable = fluwx.addSubscriber((response) async {
//     if (completer.isCompleted) {
//       return;
//     }
//     if (response is WeChatAuthResponse) {
//       print('${response.code}');
//       if (response.errCode == 0) {
//         ///通过CODE获取 access_token
//         var res = await Request().dio.get(
//             'https://api.weixin.qq.com/sns/oauth2/access_token',
//             queryParameters: {
//               'appid': Global.wxappId,
//               'secret': Global.wxappSecret,
//               'code': response.code,
//               'grant_type': 'authorization_code',
//             }).catchError((e) {
//           print(e);
//           if (!completer.isCompleted) {
//             completer.completeError(e);
//           }
//         });
//         print('STEP1:  ${res.data}');
//         if(res.data == null){
//           completer.complete(null);
//           return;
//         }
//         var resData = jsonDecode(res.data);
//         if (resData['openid'] != null) {
//           authMap['wxOpenid'] = resData['openid'];
//           authMap['wxUnionid'] = resData['unionid'];
//           ///通过access_token获取用户信息
//            res = await Request().dio.get(
//               'https://api.weixin.qq.com/sns/userinfo',
//               queryParameters: {
//                 'access_token': resData['access_token'],
//                 'openid': resData['openid'],
//               }).catchError((e) {
//             print(e);
//             if (!completer.isCompleted) {
//               completer.completeError(e);
//             }
//           });
//           print('STEP2 userInfo:  $res');
//           if(res.data!=null){
//             resData = jsonDecode(res.data);
//             authMap['nickname'] = resData['nickname'];
//             authMap['img'] = resData['headimgurl'];
//             completer.complete(authMap);
//           }else{
//             completer.complete(null);
//           }
//         } else {
//           completer.complete(null);
//         }
//       } else {
//         completer.complete(null);
//       }
//     }
//   });
//   fluwx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: 'lima_auth_wx')).catchError((e){
//     print(e);
//     if(completer.isCompleted){
//       return Future.value(false);
//     }
//     completer.completeError(e);
//     return Future.value(false);
//   });
//   return completer.future.whenComplete(() {
//     _fluwxCancelable?.cancel();
//     _fluwxCancelable = null;
//   });
// }
//
// ///微信分享
// Future wechatShare(dynamic data) async {
//   var img = 'http://pic28.photophoto.cn/20130818/0020033143720852_b.jpg';
//   if (data['img'] != null) {
//     img = data['img'];
//   }
//   WeChatScene scene = WeChatScene.session;
//   if (data['platform'] == 'timeline') {
//     scene = WeChatScene.timeline;
//   }
//   Completer completer = Completer();
//   _fluwxCancelable?.cancel();
//   _fluwxCancelable = null;
//   Fluwx fluwx = Fluwx();
//   await fluwx.registerApi(
//       appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
//   _fluwxCancelable = fluwx.addSubscriber((response) {
//     if (completer.isCompleted) {
//       return;
//     }
//     // print(response.toString());
//     if(response is WeChatShareResponse){
//       completer.complete(true);
//     }
//   });
//   fluwx.share(WeChatShareImageModel(WeChatImage.network(img), scene: scene)).catchError((e){
//     print(e);
//     if (!completer.isCompleted) {
//       completer.completeError(e);
//     }
//     return Future.value(false);
//   });
//   return completer.future.whenComplete(() {
//     _fluwxCancelable?.cancel();
//     _fluwxCancelable = null;
//   });
// }
//
// Future wxPay(dynamic data) async{
//   data = {
//     'package': 'Sign=WXPay',
//     'appid': 'wxd92e6f7ab4454e9d',
//     'sign': '241CFD0C4757C7D54E365780B094B346',
//     'partnerid': '1494720242',
//     'prepayid': 'wx12164820206263366c1f31dd7b256b0000',
//     'noncestr': 'wwwTwWSgM43MpE7KJ9yL3nfml2FB07du',
//     'timestamp': 1705049300
//   };
//   if(data == null || data is! Map){
//     return null;
//   }
//   Completer completer = Completer();
//   _fluwxCancelable?.cancel();
//   _fluwxCancelable = null;
//   Fluwx fluwx = Fluwx();
//   await fluwx.registerApi(
//       appId: Global.wxappId, universalLink: Global.wxappUniversalLink);
//   _fluwxCancelable = fluwx.addSubscriber((response) {
//     if (completer.isCompleted) {
//       return;
//     }
//     if(response is WeChatPaymentResponse){
//       //-2, null,5,null
//         print('${response.errCode}, ${response.errStr},${response.type},${response.extData}');
//         ///-2	用户取消	无需处理。发生场景：用户不支付了，点击取消，返回App。
//         ///0	成功	展示成功页面
//         ///-1	错误	可能的原因：签名错误、未注册AppID、项目设置AppID不正确、注册的AppID与设置的不匹配、其他异常等。
//         ///
//         ///
//         completer.complete(response.errCode);
//     }
//   });
//   fluwx.pay(
//       which: Payment(
//     appId: '${data['appid']}',
//     partnerId: '${data['partnerid']}',
//     prepayId: '${data['prepayid']}',
//     packageValue: '${data['package']}',
//     nonceStr: '${data['noncestr']}',
//     timestamp: data['timestamp'],
//     sign: '${data['sign']}',
//   ));
//   return completer.future.whenComplete(() {
//     _fluwxCancelable?.cancel();
//     _fluwxCancelable = null;
//   });
// }





}
