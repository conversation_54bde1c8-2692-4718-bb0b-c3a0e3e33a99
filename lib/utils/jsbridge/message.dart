import 'dart:convert';

Message messageFromJson(String str) => Message.fromJson(json.decode(str));

String messageToJson(Message data) => json.encode(data.toJson());

///  Name: MessageElement
class Message {
  Message({
    required this.api,
    this.data,
    this.callbackId,
    this.nativeResponseFlag = false,
  });

  String api; // 调用方法api
  dynamic data; // 数据
  String? callbackId; // 回调方法名称
  bool? nativeResponseFlag; // true表示原生需要返回数据， false表示原生不需要返回数据

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        api: json["api"],
        data: json["data"],
        callbackId: json["callbackId"],
        nativeResponseFlag: json["nativeResponseFlag"],
      );

  Map<String, dynamic> toJson() => {
        "api": api,
        "data": data,
        "callbackId": callbackId,
        "nativeResponseFlag": nativeResponseFlag,
      };
}
