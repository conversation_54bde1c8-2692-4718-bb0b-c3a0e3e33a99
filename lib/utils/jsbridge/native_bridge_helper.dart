import 'dart:async';
import 'dart:convert';

import 'message.dart';
import 'native_bridge_impl.dart';

///  Name: 原生桥帮助类
///  Created by Fitem on 2022/7/20
class NativeBridgeHelper {
  static final Map<String, Completer?> _callbacks = <String, Completer>{};
  static int _callbackId = 1;

  /// 发送消息
  static Completer sendMessage(
      Message message, NativeBridgeImpl nativeBridgeImpl) {
    Completer completer = Completer();
    if (message.nativeResponseFlag == true) {
      var callbackId = _pushCallback(message.api, completer);
      message.callbackId = callbackId;
      // 增加回调异常容错机制，避免消息丢失导致一直阻塞
      Future.delayed(const Duration(milliseconds: 500), () {
        var completer = _popCallback(callbackId);
        completer?.complete(Future.value(null));
      });
    }
    // H5接受消息
    final res = messageToJson(message);
    nativeBridgeImpl.runJavaScript("receiveMessage($res)");
    return completer;
  }

  ///解析从js接受到的消息，如果在callback数组中查询到回调函数，则直接消化此次消息。 否则返回交给JsApi处理
  static bool parseReceiveMessage(String json) {
    var map = jsonDecode(json);
    var callbackId = map["callbackId"];
    var data = map["data"];
    var completer = _popCallback(callbackId);
    completer?.complete(Future.value(data));
    return completer != null;
  }

  /// 接收消息
  static void receiveMessage(String json) {
    var map = jsonDecode(json);
    var callbackId = map["callbackId"];
    var data = map["data"];
    var completer = _popCallback(callbackId);
    completer?.complete(Future.value(data));
  }

  /// 记录一个函数并返回其对应的记录id
  static String _pushCallback(String api, Completer completer) {
    int id = _callbackId++;
    String key = "${api}_$id";
    _callbacks[key] = completer;
    return key;
  }

  /// 删除id对应的函数
  static Completer? _popCallback(String id) {
    var completer = _callbacks[id];

    if (completer != null) {
      _callbacks.remove(id);
      return completer;
    }
    return null;
  }
}
