import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lima/pages/simple_webview/webview_cache_manager.dart';
import 'package:lima/utils/http/api.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/q_log.dart';

/// 缓存信息模型
class CacheInfo {
  final String type;
  final int sizeBytes;
  final int count;
  final DateTime? lastModified;
  final String description;

  CacheInfo({
    required this.type,
    required this.sizeBytes,
    required this.count,
    this.lastModified,
    required this.description,
  });

  Map<String, dynamic> toJson() => {
    'type': type,
    'sizeBytes': sizeBytes,
    'sizeMB': (sizeBytes / (1024 * 1024)).toStringAsFixed(2),
    'count': count,
    'lastModified': lastModified?.millisecondsSinceEpoch,
    'description': description,
  };
}

/// 统一缓存管理器
class CacheManager {
  static const String TAG = 'CacheManager';

  /// 缓存类型常量
  static const String CACHE_TYPE_WEBVIEW = 'webview';
  static const String CACHE_TYPE_TOKEN = 'token';
  static const String CACHE_TYPE_ADVERTISEMENT = 'advertisement';
  static const String CACHE_TYPE_USER_DATA = 'userdata';
  static const String CACHE_TYPE_TEMP_FILES = 'tempfiles';
  static const String CACHE_TYPE_ALL = 'all';

  /// 获取所有缓存信息
  static Future<Map<String, dynamic>> getAllCacheInfo() async {
    try {
      QLog('$TAG: 开始获取所有缓存信息');
      
      final List<CacheInfo> cacheInfoList = [];
      int totalSize = 0;
      int totalCount = 0;

      // 1. WebView缓存信息
      final webviewInfo = await _getWebViewCacheInfo();
      cacheInfoList.add(webviewInfo);
      totalSize += webviewInfo.sizeBytes;
      totalCount += webviewInfo.count;

      // 2. Token缓存信息
      final tokenInfo = await _getTokenCacheInfo();
      cacheInfoList.add(tokenInfo);
      totalSize += tokenInfo.sizeBytes;
      totalCount += tokenInfo.count;

      // 3. 广告缓存信息
      final advInfo = await _getAdvertisementCacheInfo();
      cacheInfoList.add(advInfo);
      totalSize += advInfo.sizeBytes;
      totalCount += advInfo.count;

      // 4. 用户数据缓存信息
      final userDataInfo = await _getUserDataCacheInfo();
      cacheInfoList.add(userDataInfo);
      totalSize += userDataInfo.sizeBytes;
      totalCount += userDataInfo.count;

      // 5. 临时文件缓存信息
      final tempInfo = await _getTempFilesCacheInfo();
      cacheInfoList.add(tempInfo);
      totalSize += tempInfo.sizeBytes;
      totalCount += tempInfo.count;

      final result = {
        'success': true,
        'data': {
          'caches': cacheInfoList.map((e) => e.toJson()).toList(),
          'summary': {
            'totalSizeBytes': totalSize,
            'totalSizeMB': (totalSize / (1024 * 1024)).toStringAsFixed(2),
            'totalCount': totalCount,
            'lastUpdated': DateTime.now().millisecondsSinceEpoch,
          }
        }
      };

      QLog('$TAG: 缓存信息获取完成，总大小: ${(totalSize / (1024 * 1024)).toStringAsFixed(2)}MB');
      return result;
    } catch (e) {
      QLog('$TAG: 获取缓存信息失败: $e');
      return {
        'success': false,
        'error': '获取缓存信息失败: $e'
      };
    }
  }

  /// 获取指定类型的缓存信息
  static Future<Map<String, dynamic>> getCacheInfo(String type) async {
    try {
      QLog('$TAG: 获取缓存信息，类型: $type');
      
      CacheInfo? cacheInfo;
      
      switch (type.toLowerCase()) {
        case CACHE_TYPE_WEBVIEW:
          cacheInfo = await _getWebViewCacheInfo();
          break;
        case CACHE_TYPE_TOKEN:
          cacheInfo = await _getTokenCacheInfo();
          break;
        case CACHE_TYPE_ADVERTISEMENT:
          cacheInfo = await _getAdvertisementCacheInfo();
          break;
        case CACHE_TYPE_USER_DATA:
          cacheInfo = await _getUserDataCacheInfo();
          break;
        case CACHE_TYPE_TEMP_FILES:
          cacheInfo = await _getTempFilesCacheInfo();
          break;
        case CACHE_TYPE_ALL:
          return await getAllCacheInfo();
        default:
          return {
            'success': false,
            'error': '不支持的缓存类型: $type'
          };
      }

      return {
        'success': true,
        'data': cacheInfo.toJson()
      };
    } catch (e) {
      QLog('$TAG: 获取缓存信息失败: $e');
      return {
        'success': false,
        'error': '获取缓存信息失败: $e'
      };
    }
  }

  /// 清除指定类型的缓存
  static Future<Map<String, dynamic>> clearCache(String type) async {
    try {
      QLog('$TAG: 开始清除缓存，类型: $type');
      
      bool success = false;
      String message = '';
      
      switch (type.toLowerCase()) {
        case CACHE_TYPE_WEBVIEW:
          success = await _clearWebViewCache();
          message = success ? 'WebView缓存清除成功' : 'WebView缓存清除失败';
          break;
        case CACHE_TYPE_TOKEN:
          success = await _clearTokenCache();
          message = success ? 'Token缓存清除成功' : 'Token缓存清除失败';
          break;
        case CACHE_TYPE_ADVERTISEMENT:
          success = await _clearAdvertisementCache();
          message = success ? '广告缓存清除成功' : '广告缓存清除失败';
          break;
        case CACHE_TYPE_USER_DATA:
          success = await _clearUserDataCache();
          message = success ? '用户数据缓存清除成功' : '用户数据缓存清除失败';
          break;
        case CACHE_TYPE_TEMP_FILES:
          success = await _clearTempFilesCache();
          message = success ? '临时文件缓存清除成功' : '临时文件缓存清除失败';
          break;
        case CACHE_TYPE_ALL:
          return await _clearAllCache();
        default:
          return {
            'success': false,
            'error': '不支持的缓存类型: $type'
          };
      }

      QLog('$TAG: $message');
      return {
        'success': success,
        'message': message
      };
    } catch (e) {
      QLog('$TAG: 清除缓存失败: $e');
      return {
        'success': false,
        'error': '清除缓存失败: $e'
      };
    }
  }

  /// 清除所有缓存
  static Future<Map<String, dynamic>> _clearAllCache() async {
    QLog('$TAG: 开始清除所有缓存');
    
    final results = <String, bool>{};
    
    results['webview'] = await _clearWebViewCache();
    results['token'] = await _clearTokenCache();
    results['advertisement'] = await _clearAdvertisementCache();
    results['userdata'] = await _clearUserDataCache();
    results['tempfiles'] = await _clearTempFilesCache();
    
    final successCount = results.values.where((v) => v).length;
    final totalCount = results.length;
    
    final message = '缓存清除完成: $successCount/$totalCount 成功';
    QLog('$TAG: $message');
    
    return {
      'success': successCount == totalCount,
      'message': message,
      'details': results
    };
  }

  // ==================== 私有方法：获取缓存信息 ====================

  static Future<CacheInfo> _getWebViewCacheInfo() async {
    final count = WebViewCacheManager.getCacheCount();
    // WebView缓存大小估算（每个WebView约5MB）
    final estimatedSize = count * 5 * 1024 * 1024;
    
    return CacheInfo(
      type: CACHE_TYPE_WEBVIEW,
      sizeBytes: estimatedSize,
      count: count,
      description: 'WebView页面缓存',
    );
  }

  static Future<CacheInfo> _getTokenCacheInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where((key) => 
      key.contains('token') || key.contains('TOKEN')).toList();
    
    int totalSize = 0;
    for (final key in keys) {
      final value = prefs.getString(key) ?? '';
      totalSize += value.length * 2; // UTF-8 估算
    }
    
    return CacheInfo(
      type: CACHE_TYPE_TOKEN,
      sizeBytes: totalSize,
      count: keys.length,
      description: '用户Token缓存',
    );
  }

  static Future<CacheInfo> _getAdvertisementCacheInfo() async {
    try {
      final localPath = await getApplicationDocumentsDirectory();
      final advDir = Directory('${localPath.path}/adv');
      
      if (!advDir.existsSync()) {
        return CacheInfo(
          type: CACHE_TYPE_ADVERTISEMENT,
          sizeBytes: 0,
          count: 0,
          description: '广告文件缓存',
        );
      }
      
      int totalSize = 0;
      int count = 0;
      DateTime? lastModified;
      
      await for (final entity in advDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
          count++;
          if (lastModified == null || stat.modified.isAfter(lastModified)) {
            lastModified = stat.modified;
          }
        }
      }
      
      return CacheInfo(
        type: CACHE_TYPE_ADVERTISEMENT,
        sizeBytes: totalSize,
        count: count,
        lastModified: lastModified,
        description: '广告文件缓存',
      );
    } catch (e) {
      QLog('$TAG: 获取广告缓存信息失败: $e');
      return CacheInfo(
        type: CACHE_TYPE_ADVERTISEMENT,
        sizeBytes: 0,
        count: 0,
        description: '广告文件缓存（获取失败）',
      );
    }
  }

  static Future<CacheInfo> _getUserDataCacheInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where((key) => 
      !key.contains('token') && !key.contains('TOKEN')).toList();
    
    int totalSize = 0;
    for (final key in keys) {
      final value = prefs.get(key);
      if (value is String) {
        totalSize += value.length * 2;
      } else {
        totalSize += value.toString().length * 2;
      }
    }
    
    return CacheInfo(
      type: CACHE_TYPE_USER_DATA,
      sizeBytes: totalSize,
      count: keys.length,
      description: '用户数据缓存',
    );
  }

  static Future<CacheInfo> _getTempFilesCacheInfo() async {
    try {
      final tempDir = await getTemporaryDirectory();
      
      int totalSize = 0;
      int count = 0;
      DateTime? lastModified;
      
      if (tempDir.existsSync()) {
        await for (final entity in tempDir.list(recursive: true)) {
          if (entity is File) {
            final stat = await entity.stat();
            totalSize += stat.size;
            count++;
            if (lastModified == null || stat.modified.isAfter(lastModified)) {
              lastModified = stat.modified;
            }
          }
        }
      }
      
      return CacheInfo(
        type: CACHE_TYPE_TEMP_FILES,
        sizeBytes: totalSize,
        count: count,
        lastModified: lastModified,
        description: '临时文件缓存',
      );
    } catch (e) {
      QLog('$TAG: 获取临时文件缓存信息失败: $e');
      return CacheInfo(
        type: CACHE_TYPE_TEMP_FILES,
        sizeBytes: 0,
        count: 0,
        description: '临时文件缓存（获取失败）',
      );
    }
  }

  // ==================== 私有方法：清除缓存 ====================

  static Future<bool> _clearWebViewCache() async {
    try {
      WebViewCacheManager.clearAllCache();
      return true;
    } catch (e) {
      QLog('$TAG: 清除WebView缓存失败: $e');
      return false;
    }
  }

  static Future<bool> _clearTokenCache() async {
    try {
      await Global.removeAllTokens();
      return true;
    } catch (e) {
      QLog('$TAG: 清除Token缓存失败: $e');
      return false;
    }
  }

  static Future<bool> _clearAdvertisementCache() async {
    try {
      await API.clearCacheAdv(0); // 0表示清除所有广告缓存
      return true;
    } catch (e) {
      QLog('$TAG: 清除广告缓存失败: $e');
      return false;
    }
  }

  static Future<bool> _clearUserDataCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
        !key.contains('token') && !key.contains('TOKEN')).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      return true;
    } catch (e) {
      QLog('$TAG: 清除用户数据缓存失败: $e');
      return false;
    }
  }

  static Future<bool> _clearTempFilesCache() async {
    try {
      final tempDir = await getTemporaryDirectory();
      if (tempDir.existsSync()) {
        await tempDir.delete(recursive: true);
        await tempDir.create(); // 重新创建目录
      }
      return true;
    } catch (e) {
      QLog('$TAG: 清除临时文件缓存失败: $e');
      return false;
    }
  }
}
