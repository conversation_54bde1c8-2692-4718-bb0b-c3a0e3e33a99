
import 'dart:async';
import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lima/common/config.dart';

import '../components/q_divider.dart';
import '../pages/webview/webview_view.dart';

typedef VoidCallback = void Function();

void protocolDialog({VoidCallback? onAgreePress}) {
  var appName = "立马科技";
  var content =
  '''（点击了解详细内容），并向您简单说明如下：\n1. 为向您提供应用基本功能，我们需要收集，使用必要的信息；\n2. 基于您的明示授权，我们可能会获取您的位置、与您的设备相关的信息。例如，硬件型号、唯一设备识别码等。我们收集设备信息主要是为了向您提供消息推送服务;\n3.未经过您的同意，我们不会与其他的任何公司、组织和个人分享您的个人信息；\n4. 您有权访问您的个人信息,并随时对您的个人信息进行修改、更新、删除等操作，我们也提供账户注销的渠道。 
    ''';

  showDialog(
      context: Get.context!,
      barrierDismissible: false,
      builder: (context) {
        return UnconstrainedBox(
          child: Container(
            width: 300.w,
            height: 500.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
                  child: Text(
                    '个人信息保护指引',
                    style: TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 17.sp,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                Divider(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.w, right: 10.w),
                      child: RichText(
                          text: TextSpan(
                              style: TextStyle(
                                color: Color(0xFF1D1D1D),
                                fontSize: 15.sp,
                                height: 1.6,
                              ),
                              children: [
                                TextSpan(
                                  text: '欢迎您使用${appName}!\n\n我们依据最新的监管要求更新了',
                                ),
                                TextSpan(
                                  text: '《隐私权政策》',
                                  style: TextStyle(
                                    color: Color(0xFF2B6BFF),
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Get.to(()=>WebviewPage(),arguments: {
                                        'url': Config.USER_PROTOCOL, //地址
                                        'navBar':false,// 网页是否自带导航栏
                                        'title':'隐私政策', //网页标题
                                      });
                                    },
                                ),
                                TextSpan(
                                  text: content,
                                ),
                              ])),
                    ),
                  ),
                ),
                QDivider(),
                Container(
                  height: 55.h,
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            // 退出应用
                            exit(0);
                          },
                          style: ButtonStyle(
                            overlayColor:
                            MaterialStateProperty.all(Color(0xFFAAC2FF)),
                            backgroundColor:
                            MaterialStateProperty.resolveWith((states) {
                              if (states.contains(MaterialState.pressed)) {
                                return Color(0xFFFFFFFF);
                              }
                              return Color(0xFFFFFFFF);
                            }),
                            animationDuration: Duration(milliseconds: 1),
                          ),
                          child: Container(
                            constraints: BoxConstraints(maxHeight: 62.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Center(
                              child: Text(
                                '不同意',
                                style: TextStyle(
                                    color: Color(0xFF666666),
                                    fontSize: 16.sp),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            // 同意协议
                            if (onAgreePress != null) {
                              onAgreePress.call();
                            }

                          },
                          style: ButtonStyle(
                            overlayColor:
                            MaterialStateProperty.all(Color(0xFFAAC2FF)),
                            backgroundColor:
                            MaterialStateProperty.resolveWith((states) {
                              if (states.contains(MaterialState.pressed)) {
                                return Color(0x332B6BFF);
                              }
                              return Color(0xFF2B6BFF);
                            }),
                            animationDuration: Duration(milliseconds: 10),
                            shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              )
                            )
                          ),
                          child: Container(
                            constraints: BoxConstraints(maxHeight: 62.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  bottomRight: Radius.circular(8.r)),
                            ),
                            child: Center(
                              child: Text(
                                '同意',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16.sp),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      });
}