import 'package:flutter/material.dart';
import 'package:lima/pages/simple_webview/webview_cache_manager.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/common/config.dart';

/// WebView预加载器
class WebViewPreloader {
  static bool _isPreloading = false;
  static bool _isPreloaded = false;

  /// 预加载关键页面
  static Future<void> preloadCriticalPages() async {
    if (_isPreloading || _isPreloaded) {
      QLog('WebViewPreloader: 预加载已执行或正在执行中');
      return;
    }

    _isPreloading = true;
    QLog('WebViewPreloader: 开始预加载关键页面');

    try {
      // 预加载社区页面
      WebViewCacheManager.getCachedWebView(
        cacheKey: 'community_tab_0',
        baseUrl: Config.BASE_URL,
        pagePath: '/',
        pageTitle: '社区',
        enableCache: true,
      );

      // 延迟一下再预加载我的页面，避免同时加载造成卡顿
      await Future.delayed(const Duration(milliseconds: 500));

      // 预加载我的页面
      WebViewCacheManager.getCachedWebView(
        cacheKey: 'profile_tab_3',
        baseUrl:  Config.BASE_URL,
        pagePath: '/#/pages/user/user',
        pageTitle: '我的',
        enableCache: true,
      );

      _isPreloaded = true;
      QLog('WebViewPreloader: 关键页面预加载完成');
    } catch (e) {
      QLog('WebViewPreloader: 预加载失败: $e');
    } finally {
      _isPreloading = false;
    }
  }

  /// 清理预加载缓存
  static void clearPreloadCache() {
    WebViewCacheManager.clearAllCache();
    _isPreloaded = false;
    QLog('WebViewPreloader: 预加载缓存已清理');
  }

  /// 检查是否已预加载
  static bool get isPreloaded => _isPreloaded;
}