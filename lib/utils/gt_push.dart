
import 'dart:async';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:getuiflut/getuiflut.dart';
import 'package:lima/common/config.dart';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/pages/old/home/<USER>';
import 'package:lima/utils/q_log.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 推送初始化状态枚举
enum PushInitStatus {
  notInitialized,    // 未初始化
  initializing,      // 初始化中
  initialized,       // 已初始化
  failed,           // 初始化失败
  retrying          // 重试中
}


class GtPush {
  /// 单例构造
  static final GtPush _instance = GtPush._();
  factory GtPush() => _instance;
  GtPush._();

  // 配置常量
  static const String getuiAppId = 'CwBoQsd8u873FT6wmfRrj2';
  static const String getuiAppKey = 'Cve3Tz8k4G9hJT5a2eQmDA';
  static const String getuiAppSecret = 'F1dBADgSYz8HPdMVRNfMR9';
  
  // 重试配置
  static const int maxRetryCount = 3;
  static const int retryDelaySeconds = 5;
  static const int initTimeoutSeconds = 10;
  static const int cidTimeoutSeconds = 30;

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  
  // 状态管理属性
  PushInitStatus _status = PushInitStatus.notInitialized;
  String? _clientId;
  int _retryCount = 0;
  DateTime? _lastInitTime;
  String? _lastError;
  Timer? _cidTimeoutTimer;
  Timer? _initTimeoutTimer;
  Timer? _statusCheckTimer;
  
  // 状态检查配置
  static const int statusCheckIntervalSeconds = 60;
  
  /// 获取初始化状态
  PushInitStatus get status => _status;
  
  /// 获取是否已初始化
  bool get isInitialized => _status == PushInitStatus.initialized;
  
  /// 获取是否正在初始化
  bool get isInitializing => _status == PushInitStatus.initializing || _status == PushInitStatus.retrying;
  
  /// 获取客户端ID
  String? get clientId => _clientId;
  
  /// 获取重试次数
  int get retryCount => _retryCount;
  
  /// 获取最后错误信息
  String? get lastError => _lastError;


  /// 带重试机制的初始化方法
  Future<bool> initWithRetry() async {
    if (_status == PushInitStatus.initializing || _status == PushInitStatus.retrying) {
      QLog("🔄 个推初始化已在进行中，状态: $_status");
      return false;
    }

    _status = _retryCount > 0 ? PushInitStatus.retrying : PushInitStatus.initializing;
    _lastInitTime = DateTime.now();
    
    QLog("🚀 个推初始化开始 - 第${_retryCount + 1}次尝试");
    
    try {
      // 设置初始化超时
      _initTimeoutTimer?.cancel();
      _initTimeoutTimer = Timer(Duration(seconds: initTimeoutSeconds), () {
        if (_status == PushInitStatus.initializing || _status == PushInitStatus.retrying) {
          _handleInitTimeout();
        }
      });

      // 先注册事件处理器
      _registerEventHandlers();
      
      // 初始化SDK
      if (GetPlatform.isAndroid) {
        QLog("📱 Android 平台：调用 startSdk");
        try {
          Getuiflut.initGetuiSdk;
        } catch (e) {
          QLog("📱 Android 平台报错：${e.toString()}");
        }
      } else {
        QLog("🍎 iOS 平台：调用 startSdk");
        Getuiflut().startSdk(
            appId: getuiAppId,
            appKey: getuiAppKey,
            appSecret: getuiAppSecret);
      }
      
      // 设置CID获取超时
      _cidTimeoutTimer?.cancel();
      _cidTimeoutTimer = Timer(Duration(seconds: cidTimeoutSeconds), () {
        if (_clientId == null && (_status == PushInitStatus.initializing || _status == PushInitStatus.retrying)) {
          QLog("⏰ 个推CID获取超时，将在后台继续尝试");
        }
      });
      
      QLog("✅ 个推SDK调用完成，等待CID回调...");
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      QLog("❌ 个推初始化异常: $e");
      _handleInitFailure();
      return false;
    }
  }

  /// 原有的init方法，现在调用新的initWithRetry
  void init() {
    initWithRetry();
  }

  /// 处理初始化超时
  void _handleInitTimeout() {
    _lastError = "初始化超时";
    QLog("⏰ 个推初始化超时");
    _handleInitFailure();
  }

  /// 处理初始化失败
  void _handleInitFailure() {
    _retryCount++;
    
    if (_retryCount < maxRetryCount) {
      _status = PushInitStatus.retrying;
      QLog("🔄 个推初始化失败，${retryDelaySeconds}秒后重试 (${_retryCount}/$maxRetryCount)");
      
      Timer(Duration(seconds: retryDelaySeconds), () {
        initWithRetry();
      });
    } else {
      _status = PushInitStatus.failed;
      QLog("💥 个推初始化最终失败，已达到最大重试次数");
    }
  }

  /// 重置状态
  void reset() {
    QLog("🔄 重置个推状态");
    _status = PushInitStatus.notInitialized;
    _clientId = null;
    _retryCount = 0;
    _lastInitTime = null;
    _lastError = null;
    _cidTimeoutTimer?.cancel();
    _initTimeoutTimer?.cancel();
    _statusCheckTimer?.cancel();
  }

  /// 启动状态监控
  void _startStatusMonitoring() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(
      Duration(seconds: statusCheckIntervalSeconds),
      (timer) => _performStatusCheck(),
    );
    QLog("📊 个推状态监控已启动，检查间隔: ${statusCheckIntervalSeconds}秒");
  }

  /// 执行状态检查
  void _performStatusCheck() async {
    try {
      QLog("🔍 执行个推状态检查...");
      
      // 检查状态一致性
      await _checkStatusConsistency();
      
      // 如果初始化失败且重试次数未达上限，尝试重新初始化
      if (_status == PushInitStatus.failed && _retryCount < maxRetryCount) {
        QLog("🔄 检测到初始化失败，尝试重新初始化");
        _retryCount = 0; // 重置重试计数
        await initWithRetry();
      }
      
      // 记录当前状态
      final diagnosticInfo = getDiagnosticInfo();
      QLog("📋 个推状态检查完成: ${diagnosticInfo['status']}, CID: ${diagnosticInfo['clientId']}");
      
    } catch (e) {
      QLog("❌ 个推状态检查异常: $e");
    }
  }

  /// 检查状态一致性
  Future<void> _checkStatusConsistency() async {
    try {
      // 检查本地存储的CID与内存中的CID是否一致
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? storedCid = prefs.getString(Config.GT_CID);
      
      if (storedCid != null && storedCid.isNotEmpty && _clientId != storedCid) {
        QLog("🔧 检测到CID不一致，修复中... 存储: $storedCid, 内存: $_clientId");
        _clientId = storedCid;
        if (_status != PushInitStatus.initialized) {
          _status = PushInitStatus.initialized;
          QLog("✅ 状态已修复为已初始化");
        }
      }
      
      // 检查是否应该已经初始化但状态不正确
      if (_status == PushInitStatus.notInitialized && storedCid != null && storedCid.isNotEmpty) {
        QLog("🔧 检测到应该已初始化但状态不正确，尝试修复...");
        _clientId = storedCid;
        _status = PushInitStatus.initialized;
        QLog("✅ 状态已修复");
      }
      
    } catch (e) {
      QLog("❌ 状态一致性检查失败: $e");
    }
  }

  /// 获取诊断信息
  Map<String, dynamic> getDiagnosticInfo() {
    return {
      'status': _status.toString(),
      'clientId': _clientId,
      'retryCount': _retryCount,
      'lastInitTime': _lastInitTime?.toIso8601String(),
      'lastError': _lastError,
      'isInitialized': isInitialized,
      'isInitializing': isInitializing,
    };
  }

  /// 静态方法：获取客户端ID（兼容旧代码）
  static Future<String> get getClientId async {
    final instance = GtPush();
    if (instance._clientId != null) {
      return instance._clientId!;
    }
    
    // 从SharedPreferences获取
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? cid = prefs.getString(Config.GT_CID);
    if (cid != null) {
      instance._clientId = cid;
      return cid;
    }
    
    return '';
  }

  /// 注册事件处理器
  void _registerEventHandlers() {
    QLog("📋 注册个推事件处理器");
    
    Getuiflut().addEventHandler(
      // 注册收到 cid 的回调
      onReceiveClientId: (String message) async {
        QLog("✅ 个推 onReceiveClientId 触发: $message");
        try {
          _clientId = message;
          _status = PushInitStatus.initialized;
          _cidTimeoutTimer?.cancel();
          _initTimeoutTimer?.cancel();
          
          SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
          await sharedPreferences.setString(Config.GT_CID, message);
          QLog("✅ 个推初始化成功 - CID: $message");
          
          // 启动状态监控
          _startStatusMonitoring();
        } catch (e) {
          QLog("❌ 个推 CID 保存失败: $e");
        }
      },
      // 注册 DeviceToken 回调
      onRegisterDeviceToken: (String message) async {
        QLog('个推注册回调: $message');
      },
      // SDK收到透传消息回调
      onReceivePayload: (Map<String, dynamic> message) async {
        QLog('收到推送消息---->\n ${message.toString()}');

        //{messageId: 08ebe8ba00dc46ac8cd109e10cc7034f, taskId: OSS-0129_c51ec06f6155d1869235334bed2d4560, offLine: false, payloadMsg: 这是内容, fromGetui: true}
        if (GetPlatform.isIOS && message['offLine'] == false) {
          DarwinNotificationDetails darwinNotificationDetails =
              DarwinNotificationDetails();
          NotificationDetails notificationDetails = NotificationDetails(
            iOS: darwinNotificationDetails,
          );
          int id = DateTime.now().millisecondsSinceEpoch ~/ 1000;
          FlutterLocalNotificationsPlugin()
              .show(id, '立马科技', '${message['payloadMsg']}', notificationDetails);
        }
      },
      // 点击通知回调
      onReceiveNotificationResponse: (Map<String, dynamic> message) async {
        QLog(message);
       if(GetPlatform.isIOS){
         Config.BASE_WEBSITE = 'https://h5-lima.newtest.senthink.com/message';
         Get.find<HomeLogic>().refreshUrl(Config.BASE_WEBSITE);
       }
      },
      // APPLink中携带的透传payload信息
      onAppLinkPayload: (String message) async {},
      //通知服务开启\关闭回调
      onPushModeResult: (Map<String, dynamic> message) async {
        QLog("个推通知服务状态: $message");
      },
      // SetTag回调
      onSetTagResult: (Map<String, dynamic> message) async {
        QLog("个推设置标签结果: $message");
      },
      //设置别名回调
      onAliasResult: (Map<String, dynamic> message) async {
        QLog("个推设置别名结果: $message");
      },
      //查询别名回调
      onQueryTagResult: (Map<String, dynamic> message) async {
        QLog("个推查询标签结果: $message");
      },
      //APNs通知即将展示回调
      onWillPresentNotification: (Map<String, dynamic> message) async {
        QLog("APNs通知即将展示: $message");
      },
      //APNs通知设置跳转回调
      onOpenSettingsForNotification: (Map<String, dynamic> message) async {
        QLog("APNs通知设置跳转: $message");
      },
      onNotificationMessageArrived: (Map<String, dynamic> event) async {
        QLog('收到通知: ${event.toString()}');
      },
      onNotificationMessageClicked: (Map<String, dynamic> event) async {
        QLog('点击通知，event: $event');

        if (Get.isRegistered<HomeLogic>()) {
          Config.BASE_WEBSITE = 'https://h5-lima.newtest.senthink.com/message';
          Get.find<HomeLogic>().refreshUrl(Config.BASE_WEBSITE);
        } else {
          Config.BASE_WEBSITE = 'https://h5-lima.newtest.senthink.com/message';
          Get.to(() => HomePage());
        }
      },
      onReceiveMessageData: (Map<String, dynamic> event) async {
        QLog('收到透传消息: ${event.toString()}');
      },
      onTransmitUserMessageReceive: (Map<String, dynamic> event) async {},
      onGrantAuthorization: (String res) async {},
    );
  }
}
