import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ToastUtil {
  /// 显示成功提示
  static void showSuccess(String message) {
    Get.snackbar(
      '成功',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      icon: Icon(
        Icons.check_circle,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  /// 显示错误提示
  static void showError(String message) {
    Get.snackbar(
      '错误',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      icon: Icon(
        Icons.error,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  /// 显示警告提示
  static void showWarning(String message) {
    Get.snackbar(
      '警告',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      icon: Icon(
        Icons.warning,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  /// 显示信息提示
  static void showInfo(String message) {
    Get.snackbar(
      '提示',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      icon: Icon(
        Icons.info,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  /// 显示加载提示
  static void showLoading(String message) {
    Get.snackbar(
      '加载中',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.grey[800],
      colorText: Colors.white,
      duration: const Duration(seconds: 10), // 较长时间，需要手动关闭
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      showProgressIndicator: true,
      progressIndicatorBackgroundColor: Colors.white,
      progressIndicatorValueColor: AlwaysStoppedAnimation<Color>(Colors.grey[800]!),
    );
  }

  /// 关闭当前显示的Snackbar
  static void dismiss() {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
  }

  /// 显示自定义Snackbar
  static void showCustom({
    required String title,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    Duration? duration,
    SnackPosition? position,
  }) {
    Get.snackbar(
      title,
      message,
      snackPosition: position ?? SnackPosition.TOP,
      backgroundColor: backgroundColor ?? Colors.grey[800],
      colorText: textColor ?? Colors.white,
      duration: duration ?? const Duration(seconds: 2),
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.w,
      icon: icon != null
          ? Icon(
              icon,
              color: textColor ?? Colors.white,
              size: 24.sp,
            )
          : null,
    );
  }
}
