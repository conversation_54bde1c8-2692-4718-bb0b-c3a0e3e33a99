import 'dart:convert';

/// 密码加密工具类
/// @zoucb-2023-05-25
class CryptoUtils {
  /// base64加密
  /// @params data String 要加密的字符串
  /// @zoucb-2023-05-25
  static String base64Encrypt(String data) {
    // 定义字符映射表
    Map<String, String> b64Obj = {
      'a': 'CABDE',
      'b': 'FoHIJ',
      'c': 'KLpNO',
      'd': 'PQRyr',
      'e': 'UVWXi',
      'f': 'Zght6',
      'g': 'efabYzS',
      'h': 'jkmu012l',
      'm': 'GMqTs345d',
      'n': 'cnvwx789()=',
    };

    // 定义位置映射
    List<String> position = [
      'a:0',
      'b:1',
      'c:2',
      'd:3',
      'd:4',
      'e:4',
      'f:1',
      'f:2',
      'f:3',
      'n:8',
      'a:1',
      'n:9',
      'g:6',
      'h:7',
      'b:1',
      'm:8',
      'b:1',
      'n:1',
    ];

    // 根据位置映射添加字符
    for (String pos in position) {
      List<String> parts = pos.split(':');
      String k = parts[0];
      int v = int.parse(parts[1]);
      
      if (b64Obj.containsKey(k) && v < b64Obj[k]!.length) {
        data = data + b64Obj[k]![v];
      }
    }

    // 构建完整的base64字符表
    String b64 = b64Obj.values.join('');

    // 如果数据为空，直接返回
    if (data.isEmpty) {
      return data;
    }

    // UTF-8编码
    List<int> utf8Data = utf8.encode(data);
    
    int o1, o2, o3, h1, h2, h3, h4, bits;
    int i = 0;
    String enc = '';
    List<String> tmpArr = [];

    // Base64编码过程
    do {
      o1 = i < utf8Data.length ? utf8Data[i++] : 0;
      o2 = i < utf8Data.length ? utf8Data[i++] : 0;
      o3 = i < utf8Data.length ? utf8Data[i++] : 0;

      bits = (o1 << 16) | (o2 << 8) | o3;

      h1 = (bits >> 18) & 0x3f;
      h2 = (bits >> 12) & 0x3f;
      h3 = (bits >> 6) & 0x3f;
      h4 = bits & 0x3f;

      tmpArr.add(b64[h1] + b64[h2] + b64[h3] + b64[h4]);
    } while (i < utf8Data.length);

    enc = tmpArr.join('');

    // 添加填充字符
    switch (utf8Data.length % 3) {
      case 1:
        enc = enc.substring(0, enc.length - 2) + '==';
        break;
      case 2:
        enc = enc.substring(0, enc.length - 1) + '=';
        break;
    }

    return enc;
  }

  /// 密码加密方法（专门用于登录密码）
  /// @params password String 原始密码
  /// @return String 加密后的密码
  static String encryptPassword(String password) {
    if (password.isEmpty) {
      return password;
    }
    return base64Encrypt(password);
  }
}
