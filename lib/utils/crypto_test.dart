import 'package:lima/utils/crypto_utils.dart';
import 'package:lima/utils/q_log.dart';

/// 密码加密测试类
class CryptoTest {
  /// 测试密码加密功能
  static void testPasswordEncryption() {
    QLog('=== 密码加密功能测试 ===');
    
    // 测试用例
    List<String> testPasswords = [
      '123456',
      'password123',
      'Test@123',
      'a',
      '',
      '中文密码123',
      'very_long_password_with_special_characters_!@#\$%^&*()',
    ];
    
    for (String password in testPasswords) {
      try {
        String encrypted = CryptoUtils.encryptPassword(password);
        QLog('原始密码: "$password" (长度: ${password.length})');
        QLog('加密后: "$encrypted" (长度: ${encrypted.length})');
        QLog('---');
      } catch (e) {
        QLog('加密失败 - 密码: "$password", 错误: $e');
      }
    }
    
    QLog('=== 密码加密功能测试完成 ===');
  }
}
