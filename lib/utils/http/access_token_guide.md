# Access Token 使用指南

## 🔐 功能概述

本系统已实现完整的access_token自动管理功能，包括：
- ✅ 自动保存access_token到本地存储
- ✅ 自动在API请求中添加Authorization header
- ✅ 支持多种token字段名称
- ✅ 自动添加Bearer前缀

## 🚀 使用方法

### 1. 登录时自动保存token

当用户登录成功后，系统会自动检测并保存access_token：

```dart
// 登录API返回的数据格式
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userInfo": {
    "id": "12345",
    "nickname": "用户名",
    "avatar": "头像URL"
  },
  "expires_in": 7200
}
```

系统会自动：
1. 检测`access_token`、`token`或`accessToken`字段
2. 使用`Global.setToken()`保存到本地存储
3. 保存用户信息（排除敏感token数据）

### 2. API请求时自动添加Authorization header

所有API请求都会自动添加Authorization header：

```dart
// 自动添加的header格式
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3. 支持的token字段名称

系统支持以下token字段名称（按优先级排序）：
1. `access_token` - 最常用的OAuth2标准字段
2. `token` - 简化的token字段
3. `accessToken` - 驼峰命名的token字段

## 🔧 技术实现

### 1. Token保存机制

**文件**: `lib/pages/login/login_logic.dart`

```dart
/// 保存用户信息
Future<void> saveUserInfo(dynamic userData) async {
  // 检测多种token字段名
  String? token;
  if (userData['access_token'] != null) {
    token = userData['access_token'];
  } else if (userData['token'] != null) {
    token = userData['token'];
  } else if (userData['accessToken'] != null) {
    token = userData['accessToken'];
  }
  
  // 使用Global类保存token
  if (token != null && token.isNotEmpty) {
    await Global.setToken(token);
  }
}
```

### 2. Token存储机制

**文件**: `lib/global.dart`

```dart
/// 保存access_token
static Future<bool> setToken(String token) async{
  SharedPreferences pref = await SharedPreferences.getInstance();
  QLog('保存access_token: $token');
  return await pref.setString(Config.TOKEN, token);
}

/// 获取access_token
static Future<String?> getToken() async{
  SharedPreferences pref = await SharedPreferences.getInstance();
  return pref.getString(Config.TOKEN);
}
```

### 3. Authorization Header构建

**文件**: `lib/utils/http/request.dart`

```dart
/// 读取token并构建Authorization header
Future<Map<String, dynamic>> getAuthorizationHeader() async{
  var headers = <String, dynamic>{};
  String? token = await Global.getToken();
  
  if (token != null && token.isNotEmpty) {
    // 如果token已经包含Bearer前缀，直接使用
    if (token.startsWith('Bearer ')) {
      headers['Authorization'] = token;
    } 
    // 如果token不包含Bearer前缀，添加Bearer前缀
    else {
      headers['Authorization'] = 'Bearer $token';
    }
  }
  
  return headers;
}
```

### 4. 自动注入机制

系统使用Dio拦截器自动为所有API请求添加Authorization header：

```dart
dio.interceptors.add(
  InterceptorsWrapper(
    onRequest: (options, handler) async{
      Map<String, dynamic> _authorization = await getAuthorizationHeader();
      if (_authorization.isNotEmpty && !whiteList.contains(options.path)) {
        options = options.copyWith(headers: _authorization);
      }
      return handler.next(options);
    },
  ),
);
```

## 🧪 测试方法

### 1. 使用内置测试

在调试模式下：
1. 进入登录页面
2. 点击"测试access_token"按钮
3. 观察控制台日志

### 2. 预期日志输出

```
=== 测试access_token保存和使用 ===
模拟登录数据: {access_token: eyJ..., userInfo: {...}}
检测到access_token字段: eyJ...
保存access_token: eyJ...
✅ access_token已保存到本地存储
✅ 用户信息已保存
✅ 用户信息和token保存成功
✅ access_token保存成功: eyJhbGciOiJIUzI1NiIsI...
测试API请求header构建...
添加Bearer前缀: Bearer eyJhbGciOiJIUzI1NiIsI...
✅ Authorization header构建成功: Bearer eyJ...
```

### 3. 真实API测试

```dart
// 任何API调用都会自动包含Authorization header
final result = await API.getUserInfo();
// 请求会自动包含: Authorization: Bearer <token>
```

## 📋 白名单机制

某些API不需要token验证，可以添加到白名单：

**文件**: `lib/utils/http/request.dart`

```dart
// 不需要token的API白名单
List<String> whiteList = [
  API.adv,           // 广告接口
  API.login,         // 登录接口
  API.register,      // 注册接口
  // 添加其他不需要token的接口
];
```

## 🔍 调试信息

### 关键日志点

1. **Token保存**: `保存access_token: xxx`
2. **Token检测**: `检测到access_token字段: xxx`
3. **Header构建**: `添加Bearer前缀: Bearer xxx`
4. **API请求**: 在请求日志中查看Authorization header

### 常见问题排查

#### 1. Token未保存
- 检查登录API返回的数据格式
- 确认包含`access_token`、`token`或`accessToken`字段
- 查看保存日志是否有错误

#### 2. API请求未包含Authorization header
- 检查API路径是否在白名单中
- 确认token已正确保存
- 查看header构建日志

#### 3. Token格式错误
- 确认token是有效的JWT或其他格式
- 检查是否需要特定的前缀（如Bearer）

## 🚀 最佳实践

### 1. 登录流程
```dart
// 1. 调用登录API
final result = await API.login(username, password);

// 2. 系统自动保存access_token
await saveUserInfo(result.data);

// 3. 后续API调用自动包含Authorization header
final userInfo = await API.getUserInfo();
```

### 2. Token刷新
```dart
// 当token过期时，重新登录或刷新token
if (response.statusCode == 401) {
  // 清除旧token
  await Global.removeToken();
  
  // 重新登录
  await redirectToLogin();
}
```

### 3. 退出登录
```dart
// 清除本地存储的token和用户信息
await Global.removeToken();
await Global.removeUserInfo();
```

## 📱 支持的登录方式

所有登录方式都支持access_token自动管理：
- ✅ 手机号验证码登录
- ✅ 微信登录
- ✅ Apple登录
- ✅ 运营商一键登录

## 🔒 安全考虑

1. **Token存储**: 使用SharedPreferences安全存储
2. **敏感数据**: 用户信息中排除token数据
3. **日志安全**: 生产环境中隐藏敏感token信息
4. **白名单**: 合理配置不需要token的API

现在您的应用已经具备完整的access_token自动管理功能！🎯
