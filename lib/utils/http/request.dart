import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:lima/common/config.dart';
import 'package:lima/global.dart';
import 'api.dart';

import '../q_log.dart';

// 不需要token解开白名单
List<String> whiteList = [
  API.adv
];

/*
  * http 操作类
  *
  * 手册
  * https://github.com/flutterchina/dio/blob/master/README-ZH.md
  *
  * 从3.x升级到 4.x
  * https://github.com/flutterchina/dio/blob/master/migration_to_4.x.md
*/
class Request {
  static Request _instance = Request._internal();
  factory Request() => _instance;

  late Dio dio;
  CancelToken cancelToken = new CancelToken();

  Request._internal() {
    // BaseOptions、Options、RequestOptions 都可以配置参数，优先级别依次递增，且可以根据优先级别覆盖参数
    // QLog("Config.BASE_URL:$Config.BASE_URL");
    BaseOptions options = new BaseOptions(
      // 请求基地址,可以包含子路径
      baseUrl: Config.BASE_URL,

      //连接服务器超时时间，单位是毫秒.
      connectTimeout: Duration(seconds: 30),

      // 响应流上前后两次接受到数据的间隔，单位为毫秒。
      receiveTimeout: Duration(seconds: 30),

      // Http请求头.
      // headers: {},
      contentType: 'application/json; charset=utf-8',
      responseType: ResponseType.json,
    );

    dio = new Dio(options);

    // Cookie管理
    CookieJar cookieJar = CookieJar();
    dio.interceptors.add(CookieManager(cookieJar));
    // 添加拦截器
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async{
          // 在请求被发送之前做一些预处理
          Map<String, dynamic> _authorization = await getAuthorizationHeader();
          if (_authorization.isNotEmpty && !whiteList.contains(options.path)) {
            options = options.copyWith(headers: _authorization);
          }
          return handler.next(options); //continue
        },
        onResponse: (response, handler) {
          // 在返回响应数据之前做一些预处理
          // QLog(
          //     '\n--------------------------请求头--------------------------\n${response.requestOptions.headers}');

          // 处理请求参数的显示
          String requestParams = '';
          if (response.requestOptions.method == 'POST') {
            final data = response.requestOptions.data;
            if (data is FormData) {
              // 处理FormData类型的参数
              final formFields = <String, dynamic>{};
              for (final field in data.fields) {
                formFields[field.key] = field.value;
              }
              requestParams = 'FormData: ${jsonEncode(formFields)}';
            } else {
              requestParams = data?.toString() ?? 'null';
            }
          } else {
            requestParams = response.requestOptions.queryParameters.toString();
          }

          QLog(
              '\n--------------------------请求地址--------------------------\n ${response.realUri}\n--------------------------请求参数：--------------------------\n$requestParams\n--------------------------请求结果：--------------------------\n${jsonEncode(response.data)}');

          return handler.next(response);
        },
        onError: (DioError e, handler) {
          // 当请求失败时做一些预处理
          ErrorEntity eInfo = createErrorEntity(e);
          // 错误提示
          // EasyLoading.showInfo(eInfo.message.toString());
          // 错误交互处理
          // switch (eInfo.code) {
          //   case 401: // 没有权限 重新登录
          //     deleteTokenAndReLogin();
          //     break;
          //   default:
          // }
          return handler.next(e);
        },
      ),
    );

  }

  /// 读取token并构建Authorization header
  Future<Map<String, dynamic>> getAuthorizationHeader({bool isRefresh = false}) async{
    var headers = <String, dynamic>{};
    String? token = await Global.getToken();

    if (token != null && token.isNotEmpty) {
      // 如果token已经包含Bearer前缀，直接使用
      if (token.startsWith('Bearer ')) {
        headers['Authorization'] = token;
        QLog('使用Bearer token: ${token.substring(0, 20)}...');
      }
      // 如果token不包含Bearer前缀，添加Bearer前缀
      else {
        headers['Authorization'] = 'Bearer $token';
        QLog('添加Bearer前缀: Bearer ${token.substring(0, 20)}...');
      }
    } else {
      QLog('未找到有效的access_token');
    }

    return headers;
  }

  /// restful get 操作
  Future get(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();
    // if (path != ApiPath.refreshToken && path != ApiPath.login) {
    //   await checkToken();
    // }
    var response = await dio.get(
      path,
      queryParameters: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// restful post 操作
  Future post(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();
    // if (path != ApiPath.refreshToken && path != ApiPath.login) {
    //   await checkToken();
    // }
    var response = await dio.post(
      path,
      data: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// restful put 操作
  Future put(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();
    // if (path != ApiPath.refreshToken && path != ApiPath.login) {
    //   await checkToken();
    // }
    var response = await dio.put(
      path,
      data: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// restful patch 操作
  Future patch(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();

    var response = await dio.patch(
      path,
      data: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );

    return response.data;
  }

  /// restful delete 操作
  Future delete(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();
    // if (path != ApiPath.refreshToken && path != ApiPath.login) {
    //   await checkToken();
    // }
    var response = await dio.delete(
      path,
      data: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// restful post form 表单提交操作
  Future postForm(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();
    // 设置Content-Type为application/x-www-form-urlencoded
    requestOptions = requestOptions.copyWith(
      contentType: 'application/x-www-form-urlencoded',
    );

    var response = await dio.post(
      path,
      data: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// restful post multipart form-data 操作
  Future postFormData(String path, {dynamic params, Options? options}) async {
    Options requestOptions = options ?? Options();

    var response = await dio.post(
      path,
      data: FormData.fromMap(params),
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /*
   * error统一处理
   */
  ErrorEntity createErrorEntity(DioError error) {
    switch (error.type) {
      case DioErrorType.cancel:
        {
          return ErrorEntity(code: -1, message: "请求取消");
        }
      case DioErrorType.connectionTimeout:
        {
          return ErrorEntity(code: -1, message: "连接超时");
        }
      case DioErrorType.sendTimeout:
        {
          return ErrorEntity(code: -1, message: "请求超时");
        }

      case DioErrorType.receiveTimeout:
        {
          return ErrorEntity(code: -1, message: "响应超时");
        }
      case DioErrorType.badResponse:
        {
          try {
            int? errCode = error.response?.statusCode;
            if (errCode == null) {
              return ErrorEntity(code: -2, message: error.message);
            }
            switch (errCode) {
              case 400:
                {
                  return ErrorEntity(
                      code: errCode,
                      message: error.response?.data['message'] ?? "请求语法错误");
                }

              case 401:
                {
                  return ErrorEntity(
                      code: errCode,
                      message: error.response?.data['message'] ?? "没有权限");
                }

              case 403:
                {
                  return ErrorEntity(
                      code: errCode,
                      message: error.response?.data['message'] ?? "服务器拒绝执行");
                }
              case 404:
                {
                  return ErrorEntity(code: errCode, message: "无法连接服务器");
                }
              case 405:
                {
                  return ErrorEntity(
                      code: errCode,
                      message: error.response?.data['message'] ?? "请求方法被禁止");
                }
              case 500:
                {
                  return ErrorEntity(code: errCode, message: "服务器内部错误");
                }
              case 502:
                {
                  return ErrorEntity(code: errCode, message: "无效的请求");
                }
              case 503:
                {
                  return ErrorEntity(
                      code: errCode,
                      message: error.response?.data['message'] ?? "服务器挂了");
                }
              case 505:
                {
                  return ErrorEntity(
                      code: errCode,
                      message:
                          error.response?.data['message'] ?? "不支持HTTP协议请求");
                }
              default:
                {
                  return ErrorEntity(
                      code: errCode, message: error.response?.data['message']);
                }
            }
          } on Exception catch (_) {
            return ErrorEntity(code: -1, message: "未知错误");
          }
        }
      default:
        {
          return ErrorEntity(code: -1, message: error.message);
        }
    }
  }

//   checkToken() async {
//     DateTime times = DateTime.now();
//     int now = times.millisecondsSinceEpoch ~/ 1000;
//
//     if (Global.profile?.accessToken == null) {
//       return null;
//     }
//     int tokenExp = Global.profile!.tokenExp!;
//     int time = 24 * 60 * 60;
//     if (now < tokenExp - time) {
//       return null;
//     // } else if (now > tokenExp - time && now < tokenExp) {
//     } else if ((now > (tokenExp - time)) && (now < tokenExp)) {
//       await API.refreshToken().then((bool value) {
//         if (!value) {
//           /// 退出登录
//           deleteTokenAndReLogin();
//         }
//       });
//     } else {
//       /// 退出登录
//       deleteTokenAndReLogin();
//     }
//   }
}

// 异常处理
class ErrorEntity implements Exception {
  int code;
  String? message;
  ErrorEntity({required this.code, this.message});

  String toString() {
    if (message == null) return "Exception";
    return "Exception: code $code, $message";
  }
}
