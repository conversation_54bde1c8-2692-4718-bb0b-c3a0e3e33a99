import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData,MultipartFile;
import 'package:lima/utils/http/request.dart';
import 'package:lima/utils/q_log.dart';
import 'package:lima/utils/crypto_utils.dart';
import 'package:path_provider/path_provider.dart';

class API {
  static String adv = 'challenger/auth/app/getAd';
  static String uploadImage = 'zuul/mars/image/upload/challenger/applogo';
  static String checkFirmware = 'columbia/app/v1/checkOta';//APP检查固件更新

  // 登录相关API
  static String sendSmsCode = 'auth/sendSmsCode'; // 发送短信验证码
  static String getSmsCode = 'v3/msg/front/commons/smsCode'; // 获取验证码（免密登录或微信授权使用）
  static String bindMobile = 'v3/member/front/login/wechat/bindMobile'; // 授权登录绑定手机号
  static String login = 'auth/login'; // 手机号登录（旧接口）
  static String freeSecretLogin = 'v3/member/front/login/freeSecretLogin'; // 手机号+验证码免密登录
  static String secretLogin = 'v3/member/front/login/secretLogin'; // 手机号+密码登录
  static String resetLoginPwd = 'v3/member/front/login/resetLoginPwd'; // 重置登录密码
  static String wechatLogin = 'v3/member/front/login/wechat/login'; // 微信/苹果统一登录接口
  static String appleLogin = 'auth/appleLogin'; // 苹果登录（备用）
  static String carrierLogin = 'auth/carrierLogin'; // 运营商一键登录（旧接口）
  static String dypnsLogin = 'v3/member/front/login/dypnsLogin'; // 运营商一键登录（新接口）
  static String logout = 'v3/member/front/login/logout'; // 退出登录
  static String memberInfo = 'v3/member/front/member/memberInfo'; // 获取个人信息
  static String updateMemberInfo = 'v3/member/front/member/updateInfo'; // 更新个人信息


  static Future<NetModel> getAdv() async {
    var response = await Request().get(
      adv,
    );
    return NetModel.fromJson(response);
  }

  /// 发送短信验证码
  static Future<NetModel> sendSmsCodeApi({
    required String phone,
    String type = 'login',
    String? clientId,
  }) async {
    var params = {
      'phone': phone,
      'type': type,
    };

    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    var response = await Request().post(
      sendSmsCode,
      params: params,
    );
    return NetModel.fromJson(response);
  }

  /// 获取验证码（免密登录或微信授权使用）
  /// mobile: 手机号
  static Future<NetModel> getSmsCodeApi({
    required String mobile,
  }) async {
    var params = {
      'mobile': mobile,
    };

    QLog('=== 获取验证码API请求 ===');
    QLog('接口地址: $getSmsCode');
    QLog('请求参数: $params');
    QLog('========================');

    var response = await Request().get(
      getSmsCode,
      params: params,
    );

    QLog('=== 获取验证码API响应 ===');
    QLog('响应数据: $response');
    QLog('========================');

    return NetModel.fromJson(response);
  }

  /// 授权登录绑定手机号
  /// bindKey: 用户信息code
  /// mobile: 将绑定的手机号
  /// smsCode: 将绑定的手机号验证码
  /// aliCode: 阿里一键获取手机号CODE（可选）
  /// appType: app类型，1-android，2-ios（可选）
  /// clientId: 客户端身份ID app传参（可选）
  static Future<NetModel> bindMobileApi({
    required String bindKey,
    required String mobile,
    String? smsCode,
    String? aliCode,
    String? appType,
    String? clientId,
  }) async {
    var params = {
      'bindKey': bindKey,
      'mobile': mobile,
    };

    // 添加可选参数
    if (smsCode != null && smsCode.isNotEmpty) {
      params['smsCode'] = smsCode;
    }
    if (aliCode != null && aliCode.isNotEmpty) {
      params['aliCode'] = aliCode;
    }
    if (appType != null && appType.isNotEmpty) {
      params['appType'] = appType;
    }

    QLog('=== 绑定手机号API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $bindMobile');
    QLog('Content-Type: multipart/form-data');
    QLog('bindKey: $bindKey');
    QLog('mobile: $mobile');
    QLog('smsCode: $smsCode');
    QLog('aliCode: $aliCode');
    QLog('appType: $appType');
    QLog('clientId: $clientId');
    QLog('Multipart Form-Data参数: $params');
    QLog('===============================================');

    // 使用postFormData发送multipart/form-data请求
    var response = await Request().postFormData(
      bindMobile + (clientId != null ? '?clientId=$clientId' : ''),
      params: params,
    );

    QLog('=== 绑定手机号API响应 ===');
    QLog('响应数据: $response');
    QLog('========================');

    return NetModel.fromJson(response);
  }

  /// 手机号登录（旧接口）
  static Future<NetModel> phoneLogin({
    required String phone,
    required String code,
    String? clientId,
  }) async {
    var params = {
      'phone': phone,
      'code': code,
      'loginType': 'sms',
    };

    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    var response = await Request().post(
      login,
      params: params,
    );
    return NetModel.fromJson(response);
  }

  /// 手机号+验证码免密登录（新接口）
  static Future<NetModel> freeSecretLoginApi({
    required String mobile,
    required String smsCode,
    String? appType, // 会员来源：1、Android；2、IOS
    String? clientId,
  }) async {
    // 使用multipart/form-data格式
    var params = {
      'mobile': mobile,
      'smsCode': smsCode,
      'appType': appType,
      'clientId': clientId,
    };

    // 打印请求参数
    QLog('=== 手机号+验证码免密登录API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $freeSecretLogin');
    QLog('Content-Type: multipart/form-data');
    QLog('Form参数: $params');
    QLog('===============================================');

    // 构建完整的URL（包含查询参数）
    String fullUrl = freeSecretLogin;

    var response = await Request().postFormData(
      fullUrl,
      params: params,
    );

    QLog('=== 手机号+验证码免密登录API响应 ===');
    QLog('响应数据: ${jsonEncode(response)}');
    QLog('===============================================');

    return NetModel.fromJson(response);
  }

  /// 手机号+密码登录
  static Future<NetModel> secretLoginApi({
    required String mobile,
    required String password,
    String? appType, // 会员来源：1、Android；2、IOS
    String? clientId,
  }) async {
    // 对密码进行加密
    String encryptedPassword = CryptoUtils.encryptPassword(password);
    QLog('密码登录 - 原始密码长度: ${password.length}, 加密后密码长度: ${encryptedPassword.length}');

    // 使用multipart/form-data格式
    var params = {
      'mobile': mobile,
      'password': encryptedPassword, // 使用加密后的密码
    };

    // 添加可选参数
    if (appType != null && appType.isNotEmpty) {
      params['appType'] = appType;
    }
    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    // 打印请求参数
    QLog('=== 手机号+密码登录API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $secretLogin');
    QLog('Content-Type: multipart/form-data');
    QLog('Form参数: $params');
    QLog('===============================================');

    var response = await Request().postFormData(
      secretLogin,
      params: params,
    );

    QLog('=== 手机号+密码登录API响应 ===');
    QLog('响应数据: ${jsonEncode(response)}');
    QLog('===============================================');

    return NetModel.fromJson(response);
  }

  /// 重置登录密码
  static Future<NetModel> resetLoginPwdApi({
    required String mobile,
    required String password,
    required String smsCode,
  }) async {
    // 对密码进行加密
    String encryptedPassword = CryptoUtils.encryptPassword(password);
    QLog('重置密码 - 原始密码长度: ${password.length}, 加密后密码长度: ${encryptedPassword.length}');

    // 使用multipart/form-data格式
    var params = {
      'mobile': mobile,
      'password': encryptedPassword, // 使用加密后的密码
      'smsCode': smsCode,
    };

    // 打印请求参数
    QLog('=== 重置登录密码API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $resetLoginPwd');
    QLog('Content-Type: multipart/form-data');
    QLog('Form参数: $params');
    QLog('===============================================');

    var response = await Request().postFormData(
      resetLoginPwd,
      params: params,
    );

    QLog('=== 重置登录密码API响应 ===');
    QLog('响应数据: ${jsonEncode(response)}');
    QLog('===============================================');

    return NetModel.fromJson(response);
  }

  /// 统一授权登录接口 - 支持微信和苹果登录
  static Future<NetModel> authLoginApi({
    required String source, // "1"=Apple授权, "2"=微信授权
    required Map<String, dynamic> userInfo, // 用户信息
    String? clientId,
    String? appType, // 1-android，2-ios
  }) async {
    var params = {
      'source': source,
      'userInfo': Uri.encodeComponent(jsonEncode(userInfo)),
    };

    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    if (appType != null && appType.isNotEmpty) {
      params['appType'] = appType;
    }

    // 打印请求参数
    QLog('=== 统一授权登录API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $wechatLogin');
    QLog('Content-Type: multipart/form-data');
    QLog('source: $source');
    QLog('userInfo原始数据: ${jsonEncode(userInfo)}');
    QLog('userInfo编码后: ${Uri.encodeComponent(jsonEncode(userInfo))}');
    QLog('clientId: $clientId');
    QLog('appType: $appType');
    QLog('Multipart Form-Data参数: $params');
    QLog('===============================================');

    var response = await Request().postFormData(
      wechatLogin, // 统一授权登录接口
      params: params,
    );

    QLog('=== 统一授权登录API响应 ===');
    QLog('响应数据: $response');
    QLog('========================');

    return NetModel.fromJson(response);
  }

  /// 微信登录 - 便捷方法
  static Future<NetModel> wechatLoginApi({
    required String openid,
    String? unionid,
    String? nickname,
    String? sex,
    String? province,
    String? city,
    String? country,
    String? headimgurl,
    String? clientId,
    String? appType,
  }) async {
    var userInfo = {
      'openid': openid,
      if (unionid != null) 'unionid': unionid,
      if (nickname != null) 'nickname': nickname,
      if (sex != null) 'sex': sex,
      if (province != null) 'province': province,
      if (city != null) 'city': city,
      if (country != null) 'country': country,
      if (headimgurl != null) 'headimgurl': headimgurl,
    };

    return authLoginApi(
      source: '2', // 微信授权
      userInfo: userInfo,
      clientId: clientId,
      appType: appType,
    );
  }

  /// 苹果登录 - 便捷方法
  static Future<NetModel> appleLoginApi({
    required String appleOpenid,
    String? nickname,
    String? email,
    String? clientId,
    String? appType,
  }) async {
    var userInfo = {
      'appleOpenid': appleOpenid,
      if (nickname != null) 'nickname': nickname,
      if (email != null) 'email': email,
    };

    return authLoginApi(
      source: '1', // Apple授权
      userInfo: userInfo,
      clientId: clientId,
      appType: appType,
    );
  }

  /// 运营商一键登录（旧接口）
  static Future<NetModel> carrierLoginApi({
    required String token,
    required String phone,
    String? operator, // 运营商类型：移动、联通、电信
    String? clientId,
  }) async {
    var params = {
      'token': token,
      'phone': phone,
      'operator': operator,
      'loginType': 'carrier',
    };

    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    var response = await Request().post(
      carrierLogin,
      params: params,
    );
    return NetModel.fromJson(response);
  }

  /// 运营商一键登录（新接口）- DYPNS
  /// aliCode: 运营商返回的手机码
  /// clientId: 客户端身份ID app传参（可选）
  static Future<NetModel> dypnsLoginApi({
    required String aliCode,
    String? clientId,
  }) async {
    // 使用multipart/form-data格式
    var params = {
      'aliCode': aliCode,
      'clientId': clientId
    };

    // 打印请求参数
    QLog('=== 运营商一键登录API请求 (Multipart Form-Data) ===');
    QLog('接口地址: $dypnsLogin');
    QLog('Content-Type: multipart/form-data');
    QLog('Form参数: $params');
    QLog('===============================================');

    // 构建完整的URL（包含查询参数）
    String fullUrl = dypnsLogin;

    var response = await Request().postFormData(
      fullUrl,
      params: params,
    );

    QLog('=== 运营商一键登录API响应 ===');
    QLog('响应数据: ${jsonEncode(response)}');
    QLog('===============================================');

    return NetModel.fromJson(response);
  }

  /// 退出登录
  /// alias: 客户端身份别名 app传参（可选）
  /// clientId: 客户端身份ID app传参（可选）
  /// refreshKey: 登录刷新令牌（可选）
  static Future<NetModel> logoutApi({
    String? alias,
    String? clientId,
    String? refreshKey,
  }) async {
    // 构建查询参数
    var params = <String, dynamic>{};

    if (alias != null && alias.isNotEmpty) {
      params['alias'] = alias;
    }

    if (clientId != null && clientId.isNotEmpty) {
      params['clientId'] = clientId;
    }

    if (refreshKey != null && refreshKey.isNotEmpty) {
      params['refreshKey'] = refreshKey;
    }

    QLog('=== 退出登录API请求 ===');
    QLog('接口地址: $logout');
    QLog('请求方式: POST');
    QLog('查询参数: $params');
    QLog('需要Authorization: true');
    QLog('========================');
       // 构建查询字符串
    String queryString = '';
    if (params.isNotEmpty) {
      queryString = '?${params.entries
          .map((entry) => '${Uri.encodeComponent(entry.key)}=${Uri.encodeComponent(entry.value.toString())}')
          .join('&')}';
    }

    String fullUrl = '$logout$queryString';

    try {
      var response = await Request().post(fullUrl);

      QLog('=== 退出登录API响应 ===');
      QLog('响应数据: $response');
      QLog('========================');

      return NetModel.fromJson(response);
    } catch (e) {
      QLog('=== 退出登录API异常 ===');
      QLog('异常信息: $e');
      QLog('========================');
      rethrow;
    }
  }

  /// 获取个人信息
  static Future<NetModel> getMemberInfoApi() async {
    QLog('=== 获取个人信息API请求 ===');
    QLog('接口地址: $memberInfo');
    QLog('请求方式: GET');
    QLog('需要Authorization: true');
    QLog('========================');

    try {
      var response = await Request().get(memberInfo);

      QLog('=== 获取个人信息API响应 ===');
      QLog('响应数据: $response');
      QLog('========================');

      return NetModel.fromJson(response);
    } catch (e) {
      QLog('=== 获取个人信息API异常 ===');
      QLog('异常信息: $e');
      QLog('========================');
      rethrow;
    }
  }

  /// 更新个人信息
  /// gender: 性别：0、保密；1、男；2、女
  /// memberAvatar: 用户头像
  /// memberBirthday: 生日
  /// memberNickName: 会员昵称
  /// memberTrueName: 真实姓名
  static Future<NetModel> updateMemberInfoApi({
    int? gender,
    String? memberAvatar,
    String? memberBirthday,
    String? memberNickName,
    String? memberTrueName,
  }) async {
    var params = <String, dynamic>{};

    // 添加非空参数到查询字符串
    if (gender != null) {
      params['gender'] = gender.toString();
    }
    if (memberAvatar != null && memberAvatar.isNotEmpty) {
      params['memberAvatar'] = memberAvatar;
    }
    if (memberBirthday != null && memberBirthday.isNotEmpty) {
      params['memberBirthday'] = memberBirthday;
    }
    if (memberNickName != null && memberNickName.isNotEmpty) {
      params['memberNickName'] = memberNickName;
    }
    if (memberTrueName != null && memberTrueName.isNotEmpty) {
      params['memberTrueName'] = memberTrueName;
    }

    // 构建查询字符串
    String queryString = '';
    if (params.isNotEmpty) {
      queryString = '?${params.entries
          .map((entry) => '${Uri.encodeComponent(entry.key)}=${Uri.encodeComponent(entry.value.toString())}')
          .join('&')}';
    }

    String fullUrl = '$updateMemberInfo$queryString';

    QLog('=== 更新个人信息API请求 ===');
    QLog('接口地址: $fullUrl');
    QLog('请求方式: POST');
    QLog('查询参数: $params');
    QLog('需要Authorization: true');
    QLog('========================');

    try {
      var response = await Request().post(fullUrl);

      QLog('=== 更新个人信息API响应 ===');
      QLog('响应数据: $response');
      QLog('========================');

      return NetModel.fromJson(response);
    } catch (e) {
      QLog('=== 更新个人信息API异常 ===');
      QLog('异常信息: $e');
      QLog('========================');
      rethrow;
    }
  }

  ///上传图片/视频/logo

  static Future<NetModel> uploadFile({dynamic filePath}) async {
    FormData params = FormData.fromMap({
      "image": await MultipartFile.fromFile(filePath),
    });
    var response = await Request().post(
      uploadImage,
      params: params,
    );
    return NetModel.fromJson(response);
  }

  static Future<NetModel> checkOTAMission({dynamic deviceNo}) async {
    var response = await Request().get(
      checkFirmware,
      params: {'dvcNo':deviceNo},
    );
    return NetModel.fromJson(response);
  }

  /// 下载广告
  static Future downloadFile({String? path}) async {
    // path = 'https://new-gateway-lima.newtest.senthink.com/mars/static/whole/202312/1703663171748l4n38lzat49sif4fe.png';
    // path = 'https://new-gateway-lima.newtest.senthink.com/mars/static/whole/202401/1704165105374db4zs4saqxx4lnry9.mp4';
    // path = 'https://new-gateway-lima.newtest.senthink.com/mars/static/whole/202401/17041673360699x8hdc6pckknibev8.gif';
    if (path != null) {
      var fileName = path.split('/').last;
      var localPath = await getApplicationDocumentsDirectory();
      var savePath = '${localPath.path}/adv';
      var saveDir = Directory(savePath);
      if (!saveDir.existsSync()) {
        saveDir.createSync();
      }else{
        ///检查本地文件是否存在
        var listSync = saveDir.listSync();
        for (var element in listSync) {
          if (element is File) {
            var name = element.path.split('/').last;
            if(name == fileName){
              QLog('存在已缓存的广告 $name');
              return;
            }
          }
        }
        /// 清理过期广告
        for(var element in listSync){
          if(element is File){
            if(element.lastModifiedSync().isBefore(DateTime.now().subtract(Duration(days: 1)))){
              QLog('清理过期广告: ${element.path}');
              element.deleteSync();
            }
          }
        }
      }
      await Dio().download(path, '$savePath/$fileName',
          onReceiveProgress: (received, total) {
        if (total != -1) {
          print((received / total * 100).toStringAsFixed(0) + "%");
        }
      }).catchError((e) {
        QLog('下载$path 失败,${e.toString()}');
        return e;
      });
    }
  }

  /// type 1:图片 2:视频/gif 0:全部
  static Future clearCacheAdv(int type) async {
    var localPath = await getApplicationDocumentsDirectory();
    var savePath = '${localPath.path}/adv';
    var saveDir = Directory(savePath);
    if (saveDir.existsSync()) {
      if (type == 0) {
        QLog('清理本地广告');
        saveDir.deleteSync(recursive: true);
      } else if (type == 1) {
        QLog('清理图片广告');
        var listSync = saveDir.listSync();
        for (var element in listSync) {
          if (element is File) {
            if (element.path.isImageFileName) {
              element.deleteSync();
            }
          }
        }
      } else if (type == 2) {
        QLog('清理视频广告');
        var listSync = saveDir.listSync();
        for (var element in listSync) {
          if (element is File) {
            if (element.path.isVideoFileName || element.path.endsWith('.gif')) {
              element.deleteSync();
            }
          }
        }
      } 
    }
  }

  ///下载ota文件
  static Future downloadOTAFile({String? path}) async {
      if (path != null) {
      var fileName = path.split('/').last;
      var localPath = await getApplicationDocumentsDirectory();
      var savePath = '${localPath.path}/ota';
      var saveDir = Directory(savePath);
      if (!saveDir.existsSync()) {
        saveDir.createSync();
      }else{
        if(saveDir.listSync().isNotEmpty){
          QLog('存在已缓存的ota文件');

          ///清除本地已经缓存的ota文件
          var listSync = saveDir.listSync();
          for (var element in listSync) {
            if (element is File) {
              element.deleteSync();
            }
          }
        }

      }
      var res = await Dio().download(path, '$savePath/$fileName',
          onReceiveProgress: (received, total) {
            if (total != -1) {
              print((received / total * 100).toStringAsFixed(0) + "%");
            }
          }).catchError((e) {
        QLog('下载$path 失败,${e.toString()}');
        return e;
      });
      QLog(res);
      return res;

    }else{
      return null;
    }
  }

  ///下载分享需要的缓存文件
  /// 返回一个本地地址
  static Future downloadShareFile({String? path}) async {
    if (path != null) {
      var fileName = path.split('/').last;
      var localPath = await getApplicationDocumentsDirectory();
      var savePath = '${localPath.path}/shareFile';
      var saveDir = Directory(savePath);
      if (!saveDir.existsSync()) {
        saveDir.createSync();
      }else{
        if(saveDir.listSync().isNotEmpty){

          ///清除本地已经缓存的ota文件
          var listSync = saveDir.listSync();
          for (var element in listSync) {
            if (element is File) {
              element.deleteSync();
            }
          }
        }

      }
      await Dio().download(path, '$savePath/$fileName',
          onReceiveProgress: (received, total) {
            if (total != -1) {
              print((received / total * 100).toStringAsFixed(0) + "%");
            }
          }).catchError((e) {
        QLog('下载$path 失败,${e.toString()}');
        return e;
      });
      return '$savePath/$fileName';

    }else{
      return null;
    }
  }

}

class NetModel {
  dynamic code;
  dynamic msg;
  dynamic data;
  dynamic state; // 新API的状态码字段
  dynamic timestamp; // 新API的时间戳字段

  NetModel({this.code, this.msg, this.data, this.state, this.timestamp});

  NetModel.fromJson(Map<String, dynamic> json) {
    // 兼容旧API的code字段和新API的state字段
    code = json['code'] ?? json['state'];
    state = json['state'];
    msg = json['msg'];
    data = json['data'];
    timestamp = json['timestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['state'] = this.state;
    data['msg'] = this.msg;
    data['data'] = this.data;
    data['timestamp'] = this.timestamp;
    return data;
  }
}
