import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/src/platform/platform.dart';
import 'package:lima/global.dart';
import 'package:lima/utils/q_log.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MQTT {
  //instance
  static final MQTT _instance = MQTT._();

  factory MQTT() => _instance;

  MQTT._() {
    init();
  }

  static String url = 'simlinkmqtt.test.senthink.com';

  final String userName = 'admin';

  final String passWord = 'senthink123!';

  static int port = 1884;

  ///topic中的客户端标识
  String clientTopicIdentify = 'app/log';

  String saveLogTopic = '';

  ///listen的回调
  StreamSubscription? update, publish, userUpdate;

  ///客户端标识
  static String clientIdentifier = '';

  ///mqtt对象
  MqttServerClient client = MqttServerClient.withPort(url, '', port);

  /// 连接状态
  MqttConnectionState state = MqttConnectionState.faulted;

  void init() async {
    ///
    var pid = await Global.getUserPhone();
    if (pid == null) {
      pid = (await Global.getGTID())?.substring(0, 8);
      pid ??= Random().nextInt(10000).toString();
    }

    if (GetPlatform.isAndroid) {
      clientIdentifier = 'Android-$pid';
      saveLogTopic = '$clientTopicIdentify/$pid';
    } else {
      // IosDeviceInfo iosInfo = await infoPlugin.iosInfo;
      // print('iosInfo: ${iosInfo.toString()}');

      clientIdentifier = 'iOS-$pid';
      saveLogTopic = '$clientTopicIdentify/$pid';
    }

    /// Set logging on if needed, defaults to off
    client.logging(on: false);

    /// Set the correct MQTT protocol for mosquito
    client.setProtocolV311();

    /// If you intend to use a keep alive you must set it here otherwise keep alive will be disabled.
    client.keepAlivePeriod = 20;

    /// Add the unsolicited disconnection callback
    client.onDisconnected = () {
      print('mqtt onDisconnected');

      ///如果mqtt断开连接，则持续保持重连
      Future.delayed(Duration(seconds: 5)).then((value) {
        connect();
      });
    };

    /// Add the successful connection callback
    client.onConnected = () {
      print('mqtt onConnected');
      _dispatchCacheMsg();
      if (client.connectionStatus?.state == MqttConnectionState.connected) {
        ///log日志无需订阅
        // subscribe(clientTopicIdentify);
      }
    };

    client.onUnsubscribed = (s) {
      print('onUnsubscribed, $s');
      saveLogTopic = '';
    };

    client.onSubscribed = (s) async {
      //dvc/unbound/1171
      print('onSubscribed, $s');
      saveLogTopic = s;

      ///iOS（暂不确定Android）超过3分钟锁屏后，mqtt心跳会被系统销毁，此时需要重新连接。
      ///订阅前我们重新初始化流监听回调的对象。
      await update?.cancel();
      update = null;
      update =
          client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
        final recMess = c![0].payload as MqttPublishMessage;
        final pt =
            MqttPublishPayload.bytesToStringAsString(recMess.payload.message);

        /// The above may seem a little convoluted for users only interested in the
        /// payload, some users however may be interested in the received publish message,
        /// lets not constrain ourselves yet until the package has been in the wild
        /// for a while.
        /// The payload is a byte buffer, this will be specific to the topic
        print(
            'EXAMPLE::Change notification:: topic is <${c[0].topic}>, payload is <-- $pt -->');

        if (saveLogTopic == c[0].topic) {
          parseMsg(pt);
        }
      });

      // /// If needed you can listen for published messages that have completed the publishing
      // /// handshake which is Qos dependant. Any message received on this stream has completed its
      // /// publishing handshake with the broker.
      // publish = client.published!.listen((MqttPublishMessage message) {
      //   print(
      //       'EXAMPLE::Published notification:: topic is ${message.variableHeader!.topicName}, with Qos ${message.header!.qos}');
      // });
    };

    /// Set a ping received callback if needed, called whenever a ping response(pong) is received
    /// from the broker.
    ///心跳回调
    client.pongCallback = () {
      /// 心跳包检查还有没有遗漏没有上报的数据
      _heartPongDispatchMsg();
    };

    /// Create a connection message to use or use the default one. The default one sets the
    /// client identifier, any supplied username/password and clean session,
    /// an example of a specific one below.
    final connMess = MqttConnectMessage()
        .authenticateAs(userName, passWord)
        .withClientIdentifier(clientIdentifier)
        .withWillTopic(
            'willtopic') // If you set this you must set a will message
        .withWillMessage('My Will message')
        .startClean() // Non persistent session for testing
        .withWillQos(MqttQos.atLeastOnce);
    print('EXAMPLE::Mosquitto client connecting....');
    client.connectionMessage = connMess;
  }

  Future connect() async {
    try {
      return await client.connect();
    } on NoConnectionException catch (e) {
      // Raised by the client when connection fails.
      print('EXAMPLE::client exception - $e');
      client.disconnect();
    } on SocketException catch (e) {
      // Raised by the socket layer
      print('EXAMPLE::socket exception - $e');
      client.disconnect();
    }
  }

  Future disconnect() async {
    try {
      client.disconnect();
    } on Exception catch (e) {
      e.toString();
    }
  }

  ///重新订阅
  Future reconnectAct() async {
    if (client.connectionStatus?.state == MqttConnectionState.connected) {
      await disconnect();
    }

    var pid = await Global.getUserPhone();
    if (pid == null) {
      pid = (await Global.getGTID())?.substring(0, 8);
      pid ??= Random().nextInt(10000).toString();
    }
    if (GetPlatform.isAndroid) {
      clientIdentifier = 'Android-$pid';
      saveLogTopic = '$clientTopicIdentify/$pid';
    } else {
      clientIdentifier = 'iOS-$pid';
      saveLogTopic = '$clientTopicIdentify/$pid';
    }
    final connMess = MqttConnectMessage()
        .authenticateAs(userName, passWord)
        .withClientIdentifier(clientIdentifier)
        .withWillTopic(
            'willtopic') // If you set this you must set a will message
        .withWillMessage('My Will message')
        .startClean() // Non persistent session for testing
        .withWillQos(MqttQos.atLeastOnce);
    print('EXAMPLE::Mosquitto client connecting....');
    client.connectionMessage = connMess;

    connect();
  }

  MqttConnectionState getConnectState() {
    /// Check we are connected
    if (client.connectionStatus!.state == MqttConnectionState.connected) {
      print('EXAMPLE::Mosquitto client connected');
      state = MqttConnectionState.connected;
    } else {
      state = MqttConnectionState.disconnected;
    }
    return state;
  }

  Future subscribe(String topic) async {
    // Not a wildcard topic
    if (topic == saveLogTopic &&
        client.getSubscriptionsStatus(topic) == MqttSubscriptionStatus.active) {
      return;
    }

    client.subscribe(topic, MqttQos.atLeastOnce);

    /// If needed you can listen for published messages that have completed the publishing
    /// handshake which is Qos dependant. Any message received on this stream has completed its
    /// publishing handshake with the broker.
    // publish = client.published!.listen((MqttPublishMessage message) {
    //   print(
    //       'EXAMPLE::Published notification:: topic is ${message.variableHeader!.topicName}, with Qos ${message.header!.qos}');
    // });
  }

  void unsubscribe() {
    try {
      if (saveLogTopic.isNotEmpty && client.subscriptionsManager != null) {
        client.unsubscribe(saveLogTopic);
      }
    } catch (e) {
      e.printError();
    }
    update?.cancel();
    publish?.cancel();
    update = null;
  }

  void parseMsg(String s) {
    try {
      Map<String, dynamic> m = Map.from(json.decode(s));
    } catch (e) {
      e.printError();
    }
  }

  final builder = MqttClientPayloadBuilder();

  /// 日志上报策略

  var cacheMsg = <String>[];

  var uploadList = <String>[];

  //最大缓存条数
  final int MSG_MAX_COUNT = 1000;

  //上报的最大时间间隔，
  final int TIME_TICKET = 3;

  // 每包的消息数
  final int MSG_TICKET = 3;

  // 心跳包检查的时间间隔
  final int HEART_TIME_TICKET = 10;

  DateTime currentReportTime = DateTime.now().subtract(Duration(days: 2));

  void sendMsg(String s, {DateTime? time}) {
    builder.clear();
    if (client.connectionStatus?.state == MqttConnectionState.connected &&
        s.isNotEmpty &&
        saveLogTopic.isNotEmpty) {
      _saveCacheMsg(s);

      if (time != null) {
        // print('当前时间对比：${time.difference(currentReportTime).inSeconds}');
        // print('当前缓存数量:${cacheMsg.length}');
        if ((time.difference(currentReportTime).inSeconds >= TIME_TICKET) ||
            cacheMsg.length >= MSG_TICKET) {
          currentReportTime = time;
          _dispatchCacheMsg();
        }
      }

      // _dispatchCacheMsg();
      // builder.addUTF8String(s);
      // client.publishMessage(
      //     saveLogTopic, MqttQos.atMostOnce, builder.payload!);
    } else if (client.connectionStatus?.state !=
        MqttConnectionState.connected) {
      _saveCacheMsg(s);
    }
  }

  void _heartPongDispatchMsg() {
    var now = DateTime.now();
    if (cacheMsg.isNotEmpty &&
        now.difference(currentReportTime).inSeconds >= HEART_TIME_TICKET) {
      currentReportTime = now;
      _dispatchCacheMsg();
    }
  }

  void _saveCacheMsg(String s) {
    ///缓存日志
    if (cacheMsg.length >= MSG_MAX_COUNT) {
      cacheMsg.removeAt(0);
    }
    cacheMsg.add(s);
  }

  void _dispatchCacheMsg() {
    ///分发缓存日志
    if (client.connectionStatus?.state == MqttConnectionState.connected) {
      if (cacheMsg.isNotEmpty) {
        uploadList.clear();
        uploadList.addAll(cacheMsg);
        cacheMsg.clear();
        for (int i = 0; i < uploadList.length; i += MSG_TICKET) {
          builder.clear();
          if ((i + MSG_TICKET) >= uploadList.length) {
            builder.addUTF8String(uploadList.sublist(i).join('\n'));
          } else {
            builder.addUTF8String(
                uploadList.sublist(i, i + MSG_TICKET).join('\n'));
          }
          client.publishMessage(
              saveLogTopic, MqttQos.atMostOnce, builder.payload!);
        }
        uploadList.clear();
      }
    }
  }

  ///释放mqtt资源
  void close() {
    unsubscribe();
    client.disconnect();
  }
}

class LogModel {
  final String project;
  final int time;
  dynamic level;
  final String userId;
  final String rawLog;
  dynamic environment;

  LogModel({
    required this.project,
    required this.time,
    this.level,
    required this.userId,
    required this.rawLog,
    this.environment,
  });

  factory LogModel.fromJson(Map<String, dynamic> json) {
    return LogModel(
      project: json['project'],
      time: json['time'],
      level: json['level'],
      userId: json['userId'],
      rawLog: json['rawLog'],
      environment: json['environment'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['project'] = this.project;
    data['time'] = this.time;
    data['level'] = this.level;
    data['userId'] = this.userId;
    data['rawLog'] = this.rawLog;
    data['environment'] = this.environment;
    return data;
  }
}
