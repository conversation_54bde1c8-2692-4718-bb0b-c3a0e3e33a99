import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:lima/utils/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:stack_trace/stack_trace.dart';

enum QLogMode {
  debug, // 💚 DEBUG
  warning, // 💛 WARNING
  info, // 💙 INFO
  error, // ♥️ ERROR
}

enum LogInfoType {
  webview, // 浏览器
  web, //  web
  app, // app
  ble, // 蓝牙
  error, //  ERROR
}

QLog(
  dynamic msg, {
  QLogMode mode = QLogMode.debug,
  LogInfoType type = LogInfoType.app,
}) {
  // 获取当前堆栈
  var chain = Chain.current(); // Chain.forTrace(StackTrace.current);
  // 将 core 和 flutter 包的堆栈合起来（即相关数据只剩其中一条）
  chain =
      chain.foldFrames((frame) => frame.isCore || frame.package == "flutter");
  // 取出所有信息帧
  final frames = chain.toTrace().frames;
  // 找到当前函数的信息帧
  final idx = frames.indexWhere((element) => element.member == "QLog");
  if (idx == -1 || idx + 1 >= frames.length) {
    return "";
  }
  // 调用当前函数的函数信息帧
  final frame = frames[idx + 1];
  // 💜 💟 💙 💚 💛 🛑 🔴 🧡 ⭕️
  // var modeStr = "";
  // switch (mode) {
  //   case QLogMode.debug:
  //     modeStr = "💚 DEBUG";
  //     break;
  //   case QLogMode.warning:
  //     modeStr = "💛 WARNING";
  //     break;
  //   case QLogMode.info:
  //     modeStr = "💙 INFO";
  //     break;
  //   case QLogMode.error:
  //     modeStr = "🔴 ERROR";
  //     break;
  // }

  var modeStr = "";
  var modeIcon = "💚";

  switch(type){
    case LogInfoType.web:
      modeStr = 'web';
      modeIcon = "💚";
      break;
    case LogInfoType.ble:
      modeStr = 'bluetooth';
      modeIcon = "💛";
      break;
    case LogInfoType.error:
      modeStr = 'error';
      modeIcon = "🔴";
      break;
    case LogInfoType.webview:
      modeStr = 'webview';
      modeIcon = "🌐";
      break;
    case LogInfoType.app:
      modeStr = 'app';
      modeIcon = "💙";
      break;
  }

  var time = DateTime.now();
  final printStr =
      "[$time]-[$modeIcon]-[$modeStr]-[${frame.uri.toString().split("/").last}(${frame.line})] - $msg ";

  /// 上报逻辑
  String userId = MQTT.clientIdentifier.split('-').last;
  var env = {
    'os': MQTT.clientIdentifier.split('-').first,
    'filepath':'${frame.uri.toString().split("/").last}(${frame.line})',
    'logSource': modeStr
  };
  final LogModel logm = LogModel(project: 'lima', time: time.millisecondsSinceEpoch, userId: userId, rawLog: msg,environment: env);

  MQTT().sendMsg(jsonEncode(logm.toJson()),time: time);

  if (kReleaseMode) {
    // release模式不打印
    return;
  }
  // log(printStr);
  debugPrint(printStr);
}
